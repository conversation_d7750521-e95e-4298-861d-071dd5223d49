{"compilerOptions": {"declaration": true, "target": "ES2020", "module": "commonjs", "rootDir": "./src", "outDir": "./dist", "noEmitOnError": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "experimentalDecorators": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "**/mocks/**", "**/*.mock.ts", "**/__mocks__/**"]}