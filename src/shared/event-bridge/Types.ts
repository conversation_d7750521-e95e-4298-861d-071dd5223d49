import { IUpdatableUnifiedDisputeObject } from '../../Dispute/Types';

// For backward compatibility. This should be validated and updated in future tasks
export interface IDisputeEventDetail {
    disputeId: string,
    chargeflowId: string,
    processorName: string,
    caseId?: string,
}

export interface IDusputePropertiesForLinking {
    transactionOrderId?: string,
    transactionId?: string,
    transactionDate?: Date,
}

export interface IDisputeCreatedEventDetail extends IDisputeEventDetail {
    disputePropertiesForLinking?: IDusputePropertiesForLinking
}

export interface IDisputeUpdatedEventDetail extends IDisputeEventDetail {
    updated?: Partial<IUpdatableUnifiedDisputeObject>,
}

export interface IEvidencePdfCreatedEventDetail {
    disputeId: string;
    signedUrlLink: string;
}
