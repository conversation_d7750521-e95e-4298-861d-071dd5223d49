import { buildUpdateObject } from './helper-functions';

describe('buildUpdateObject', () => {
    it('should handle flat objects', () => {
        const prefix = 'extensions';
        const obj = { key1: 'value1', key2: 2 };
        const setAggr: Record<string, unknown> = {};

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            'extensions.key1': 'value1',
            'extensions.key2': 2,
        });
    });

    it('should handle nested objects', () => {
        const prefix = 'extensions';
        const obj = { paypal: { buyerName: 'buyerName', sellerName: 'sellerName' }, key2: 2 };
        const setAggr: Record<string, unknown> = {};

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            'extensions.paypal.buyerName': 'buyerName',
            'extensions.paypal.sellerName': 'sellerName',
            'extensions.key2': 2,
        });
    });

    it('should handle nested objects with aggregation having values', () => {
        const prefix = 'extensions';
        const obj = { paypal: { buyerName: 'buyerName', sellerName: 'sellerName' }, key2: 2 };
        const setAggr: Record<string, unknown> = {
            lastDisputeStatus: {
                status: 'status',
                dateCreated: new Date(),
                statusDate: new Date(),
                processorStatus: 'processorStatus',
                source: 'fetch',
            },
            'dispute.amount': 5,
            'chargeflow.submitted': {
                submittedAt: new Date(),
                amountInUsd: 10,
            },
        };

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            lastDisputeStatus: {
                status: 'status',
                dateCreated: expect.any(Date),
                statusDate: expect.any(Date),
                processorStatus: 'processorStatus',
                source: 'fetch',
            },
            'dispute.amount': 5,
            'chargeflow.submitted': {
                submittedAt: expect.any(Date),
                amountInUsd: 10,
            },
            'extensions.paypal.buyerName': 'buyerName',
            'extensions.paypal.sellerName': 'sellerName',
            'extensions.key2': 2,
        });
    });

    it('should handle Date objects', () => {
        const prefix = 'extensions';
        const date = new Date();
        const obj = { dateUpdated: date, key2: 2 };
        const setAggr: Record<string, unknown> = {};

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            'extensions.dateUpdated': date,
            'extensions.key2': 2,
        });
    });

    it('should handle mixed nested objects and Date objects', () => {
        const prefix = 'extensions';
        const date = new Date();
        const obj = { paypal: { dateUpdated: date }, key2: 2 };
        const setAggr: Record<string, unknown> = {};

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            'extensions.paypal.dateUpdated': date,
            'extensions.key2': 2,
        });
    });

    it('should ignore undefined values', () => {
        const prefix = 'extensions';
        const obj = { key1: undefined, key2: 2 };
        const setAggr: Record<string, unknown> = {};

        buildUpdateObject(prefix, obj, setAggr);

        expect(setAggr).toEqual({
            'extensions.key2': 2,
        });
    });
});
