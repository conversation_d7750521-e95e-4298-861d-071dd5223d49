import { paginationCalculator } from 'pagination-calculator';
import { IPaginationCalculatorOptions } from './IPaginationCalculatorOptions';
import { IPaginationCalculatorResult } from './IPaginationCalculatorOptionsResult';

export class PaginationCalculator {
    static calculate(options: IPaginationCalculatorOptions): IPaginationCalculatorResult {
        const result = paginationCalculator({
            total: options.totalItems,
            current: options.currentPage,
            pageSize: options.pageSize || options.totalItems,
        });

        return {
            totalPages: result.pageCount,
            currentPage: result.current,
            hasPreviousPage: !!(result.previous || null),
            hasNextPage: !!(result.next || null),
            itemsPerPage: options.pageSize || options.totalItems,
            totalItems: options.totalItems,
        };
    }
}
