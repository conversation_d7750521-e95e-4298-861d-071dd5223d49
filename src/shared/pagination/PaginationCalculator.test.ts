import { IPaginationCalculatorOptions } from './IPaginationCalculatorOptions';
import { PaginationCalculator } from './PaginationCalculator';

describe(PaginationCalculator.name, () => {
    describe(PaginationCalculator.calculate.name, () => {
        it('returns pagination for full options', () => {
            // GIVEN
            const options: IPaginationCalculatorOptions = {
                totalItems: 100,
                currentPage: 5,
                pageSize: 10,
            };

            // WHEN
            const result = PaginationCalculator.calculate(options);

            // THEN
            expect(result).toEqual({
                totalPages: 10,
                currentPage: 5,
                hasPreviousPage: true,
                hasNextPage: true,
                itemsPerPage: 10,
                totalItems: 100,
            });
        });

        it('returns pagination for minimal options', () => {
            // GIVEN
            const options: IPaginationCalculatorOptions = {
                totalItems: 100,
            };

            // WHEN
            const result = PaginationCalculator.calculate(options);

            // THEN
            expect(result).toEqual({
                totalPages: 1,
                currentPage: 1,
                hasPreviousPage: false,
                hasNextPage: false,
                itemsPerPage: 100,
                totalItems: 100,
            });
        });
    });
});
