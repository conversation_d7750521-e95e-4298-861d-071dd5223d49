import type { ObjectId } from 'mongodb';

export type WithId<T> = T & { _id: string };

export interface IAuditableFields {
    dateCreated: Date,
    dateUpdated?: Date,
}

export interface Id {
    _id: string,
}

export type DeepPartial<T> = T extends object ? {
  [P in keyof T]?: DeepPartial<T[P]>;
} : T;

export interface ICachedEnrichmentDocumentBase<DataType, MetadataType> extends IAuditableFields {
  cacheKey: string;
  data: DataType;
  metadata: MetadataType;
}

export interface IMongoCachedEnrichmentDocument<DataType, MetadataType> extends ICachedEnrichmentDocumentBase<DataType, MetadataType> {
  _id?: ObjectId;
  chargeflowId: ObjectId;
}

export interface ICachedEnrichmentDocument<DataType, MetadataType> extends ICachedEnrichmentDocumentBase<DataType, MetadataType> {
  _id?: string;
  chargeflowId: string
}
