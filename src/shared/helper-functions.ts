import { ObjectId } from 'mongodb';

export function deleteUndefinedFields<T>(entity: Partial<T>): Partial<T> {
    Object.keys(entity)
        .map(key => key as keyof T)
        .forEach(key => entity[key] === undefined && delete entity[key]);

    return entity;
}

export function buildUpdateObject(prefix: string, obj: Record<string, unknown>, setAggr: Record<string, unknown>) {
    Object.entries(obj).forEach(([ key, value ]) => {
        const path = `${prefix}.${key}`;
        if (value instanceof Date) {
            setAggr[path] = value;
        }
        if (typeof value === 'object' && value !== null) {
            buildUpdateObject(path, value as Record<string, unknown>, setAggr);
        } else if (value !== undefined) {
            setAggr[path] = value;
        }
    });
}

export function isMongoObjectId(value: ObjectId | string | number | any): boolean {
    return value && ObjectId.isValid(value.toString()) && new ObjectId(value.toString()).toString() === value.toString();
}

function processingMongoId(obj: Record<string, any>, callback: (value: any) => any): Record<string, any> {
    const result = Array.isArray(obj) ? [] as any : {};
    const isDate = (value: unknown): value is Date => value instanceof Date && !isNaN(+value);

    Object.entries(obj)
        .forEach(([ key, value ]) => {
            switch (true) {
                case value && isMongoObjectId(value.toString()):
                    result[key] = callback(value);
                    break;
                case isDate(value):
                    result[key] = value;
                    break;
                case value && typeof value === 'object' && !Array.isArray(value):
                    result[key] = processingMongoId(value, callback);
                    break;
                case Array.isArray(value):
                    result[key] = value.map(item => (typeof item === 'object' ? processingMongoId(item, callback) : callback(item)));
                    break;
                default:
                    result[key] = value;
            }
        });

    return result;
}

export function convertObjectIdToString<T>(value: Record<string, any>): T {
    return processingMongoId(value, item => new ObjectId(item.toString()).toHexString()) as T;
}

export function convertStringToObjectId<T>(value: Record<string, any>): T {
    return processingMongoId(value, item => new ObjectId(item.toString())) as T;
}

// #region Flatten from https://github.com/hughsk/flat
interface FlattenOptions {
    delimiter?: string;
    maxDepth?: number;
    safe?: boolean;
    transformKey?: (key: string) => string;
}

function keyIdentity(key: string): string {
    return key;
}

export function flatten<T extends Record<string, unknown>, R extends Record<string, unknown>>(target: T, opts?: FlattenOptions): R {
    const finalOpts = opts || {};

    const delimiter = finalOpts.delimiter || '.';
    const transformKey = finalOpts.transformKey || keyIdentity;
    const output: R = {} as R;

    function step(object: T, prev?: string, currentDepth?: number) {
        currentDepth = currentDepth || 1;
        Object.keys(object).forEach(function(key) {
            const value = object[key];
            const isarray = finalOpts.safe && Array.isArray(value);
            const type = Object.prototype.toString.call(value);
            const isbuffer = Buffer.isBuffer(value);
            const isobject = type === '[object Object]' || type === '[object Array]';

            const newKey = prev ? prev + delimiter + transformKey(key) : transformKey(key);

            if (!isarray && !isbuffer && isobject && Object.keys(value as Record<string, unknown>).length && (!finalOpts.maxDepth || currentDepth < finalOpts.maxDepth!)) {
                return step(value as T, newKey, currentDepth + 1);
            }

            // Cast output to allow writing to generic type
            (output as Record<string, unknown>)[newKey] = value;
        });
    }

    step(target);

    return output;
}
// #endregion
