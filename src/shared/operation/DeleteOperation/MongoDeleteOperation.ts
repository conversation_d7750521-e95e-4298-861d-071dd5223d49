import { ClientSession, Document, Filter, MongoClient, ObjectId } from 'mongodb';
import { IDeleteOperation } from './IDeleteOperation';

export class MongoDeleteOperation<T extends Document> implements IDeleteOperation {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async delete(id: ObjectId, session?: ClientSession): Promise<ObjectId> {
        await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .deleteOne({ _id: id } as Filter<T>, { session });

        return id;
    }
}
