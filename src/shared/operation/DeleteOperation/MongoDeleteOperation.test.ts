import { faker } from '@faker-js/faker';
import { MongoDeleteOperation } from './MongoDeleteOperation';
import { ClientSession, ObjectId } from 'mongodb';

import mongoClientMocks from '../__mocks__/mongoClient';
import { ITestEntity } from '../__mocks__/ITestEntity';
import { mock } from 'jest-mock-extended';

describe(MongoDeleteOperation.name, () => {
    let mongoDeleteOperation: MongoDeleteOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const deleteOne = jest.fn();
    const mongoClient = mongoClientMocks.getMongoClient({ deleteOne });

    beforeEach(() => {
        mongoDeleteOperation = new MongoDeleteOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoDeleteOperation.prototype.delete.name, () => {
        it('returns deleted mongo document id', async () => {
            expect.assertions(3);

            // GIVEN
            const id = new ObjectId();
            const session = mock<ClientSession>();

            // WHEN
            const result = await mongoDeleteOperation.delete(id, session);

            // THEN
            expect(result).toEqual(id);

            expect(deleteOne).toHaveBeenCalledTimes(1);
            expect(deleteOne).toHaveBeenCalledWith({ _id: id }, { session });
        });
    });
});
