import { faker } from '@faker-js/faker';
import { MongoUpdateOperation } from './MongoUpdateOperation';

import mongoClientMocks from '../__mocks__/mongoClient';
import { ITestEntity } from '../__mocks__/ITestEntity';
import testEntityMocks from '../__mocks__/TestEntity';
import { mock } from 'jest-mock-extended';
import { ClientSession } from 'mongodb';

describe(MongoUpdateOperation.name, () => {
    let mongoUpdateOperation: MongoUpdateOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const testEntity = testEntityMocks.getTestEntity();
    const findOneAndUpdate = jest.fn().mockResolvedValueOnce({ value: testEntity });
    const bulkWrite = jest.fn().mockResolvedValueOnce({
        modifiedCount: 1,
        hasWriteErrors: jest.fn().mockReturnValue(false),
        getWriteErrors: jest.fn().mockReturnValue([]),
    });
    const mongoClient = mongoClientMocks.getMongoClient({ findOneAndUpdate, bulkWrite });

    beforeEach(() => {
        mongoUpdateOperation = new MongoUpdateOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoUpdateOperation.prototype.update.name, () => {
        it('returns one updated mongo document', async () => {
            expect.assertions(3);

            // GIVEN
            const updatedTestEntity = {
                ...testEntityMocks.getTestEntityDto(),
                _id: testEntity._id,
            };
            const session = mock<ClientSession>();

            // WHEN
            const result = await mongoUpdateOperation.update(updatedTestEntity, session);

            // THEN
            expect(result).toEqual(testEntity);

            expect(findOneAndUpdate).toHaveBeenCalledTimes(1);
            expect(findOneAndUpdate).toHaveBeenCalledWith(
                { _id: updatedTestEntity._id },
                { $set: updatedTestEntity },
                { returnDocument: 'after', session },
            );
        });
    });

    describe(MongoUpdateOperation.prototype.bulkUpdate.name, () => {
        it('returns bulk update response', async () => {
            // GIVEN
            const bulkOps = [
                {
                    updateOne: {
                        filter: { _id: testEntity._id },
                        update: { $set: testEntity },
                        upsert: true,
                    },
                },
            ];

            mongoClientMocks.getMongoClient({ bulkWrite });
            const session = mock<ClientSession>();

            // WHEN
            const result = await mongoUpdateOperation.bulkUpdate(bulkOps, session);

            // THEN
            expect(result.result.modifiedCount).toEqual(bulkOps.length);
            expect(result.notUpdatedIds.length).toEqual(0);

            expect(bulkWrite).toHaveBeenCalledTimes(1);
            expect(bulkWrite).toHaveBeenCalledWith(bulkOps, { session });
        });
    });
});
