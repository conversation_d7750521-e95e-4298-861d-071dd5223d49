import { AnyBulkWriteOperation, ClientSession, Document, Filter, MongoClient, UpdateFilter, WithId } from 'mongodb';
import { IUpdateOperation } from './IUpdateOperation';
import { IBulkUpdateResponse } from '../../../Dispute/Types';

export class MongoUpdateOperation<T extends Document> implements IUpdateOperation<T> {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async update(entity: Readonly<WithId<Partial<T>>>, session?: ClientSession): Promise<WithId<T>> {
        const updateFilter: UpdateFilter<T> = {
            $set: entity as Readonly<Partial<T>>,
        };

        return await this.updateWithFilter({ _id: entity._id }, updateFilter, session);
    }

    async updateWithFilter(filter: Filter<T>, updateFilter: UpdateFilter<T>, session?: ClientSession): Promise<WithId<T>> {
        const { value } = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .findOneAndUpdate(
                filter,
                updateFilter,
                { returnDocument: 'after', session },
            );

        return value as WithId<T>;
    }

    async updateWithFilterAndArrayFilters(filter: Filter<T>, updateFilter: UpdateFilter<T>, arrayFilters: Document[], session?: ClientSession): Promise<WithId<T>> {
        const { value } = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .findOneAndUpdate(
                filter,
                updateFilter,
                { returnDocument: 'after', session, arrayFilters: arrayFilters },
            );

        return value as WithId<T>;
    }

    async upsert(filter: Filter<T>, updateFilter: UpdateFilter<T>, session?: ClientSession | undefined): Promise<WithId<T>> {
        const { value } = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .findOneAndUpdate(
                filter,
                updateFilter,
                { returnDocument: 'after', session, upsert: true },
            );

        return value as WithId<T>;
    }

    async bulkUpdate(bulkOps: AnyBulkWriteOperation<T>[], session?: ClientSession | undefined): Promise<IBulkUpdateResponse> {
        const notUpdatedIds: string[] = [];
        const result = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .bulkWrite(bulkOps, { session });

        if (result.modifiedCount !== bulkOps.length || result.hasWriteErrors()) {
            console.error(`Failed to upload all disputes.(Modified ${result.modifiedCount}/${bulkOps.length}). Errors: ${JSON.stringify(result.getWriteErrors())}`);
        }

        if (result.hasWriteErrors()) {
            result.getWriteErrors().forEach(error => {
                notUpdatedIds.push(error.getOperation().filter?._id);
            });
        }

        return { result, notUpdatedIds };
    }
}
