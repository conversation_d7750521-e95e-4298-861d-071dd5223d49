import { Document } from 'mongodb';
import { IUpdateOperation } from '../IUpdateOperation';

function getUpdateOperation<T extends Document>(): jest.Mocked<IUpdateOperation<T>> {
    return {
        update: jest.fn(),
        upsert: jest.fn(),
        updateWithFilter: jest.fn(),
        updateWithFilterAndArrayFilters: jest.fn(),
        bulkUpdate: jest.fn(),
    } as jest.Mocked<IUpdateOperation<T>>;
}

export default {
    getUpdateOperation,
};
