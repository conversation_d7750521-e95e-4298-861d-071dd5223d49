import { AnyBulkWriteOperation, ClientSession, Document, Filter, UpdateFilter, WithId } from 'mongodb';
import { IBulkUpdateResponse } from '../../../Dispute/Types';

export interface IUpdateOperation<T extends Document> {
    update(entity: Readonly<WithId<Partial<T>>>, session?: ClientSession): Promise<WithId<T>>
    updateWithFilter(filter: Filter<T>, updateFilter: UpdateFilter<T>, session?: ClientSession): Promise<WithId<T>>
    upsert(filter: Filter<T>, updateFilter: UpdateFilter<T>, session?: ClientSession): Promise<WithId<T>>
    updateWithFilterAndArrayFilters(filter: Filter<T>, updateFilter: UpdateFilter<T>, arrayFilters: Document[], session?: ClientSession): Promise<WithId<T>>
    bulkUpdate(bulkOps: AnyBulkWriteOperation<T>[], session?: ClientSession): Promise<IBulkUpdateResponse>
}
