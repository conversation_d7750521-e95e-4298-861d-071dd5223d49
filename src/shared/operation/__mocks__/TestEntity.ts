import { ITestEntity, ITestEntityDto } from './ITestEntity';
import { ObjectId, WithId } from 'mongodb';

const getTestEntityDto = (overrides: Partial<ITestEntityDto> = {}): ITestEntityDto => ({
    name: 'Test name',
    ...overrides,
});

const getTestEntity = (overrides: Partial<ITestEntity> = {}): WithId<ITestEntity> => ({
    _id: new ObjectId(),
    ...getTestEntityDto(),
    ...overrides,
});

export default {
    getTestEntityDto,
    getTestEntity,
};
