import { ClientSession, Document, Filter, FindOptions, MongoClient, Sort, WithId } from 'mongodb';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from './IFindByQueryOperation';
import { PaginationCalculator } from '../../pagination/PaginationCalculator';

const DefaultPage = 1;
const DefaultPageSize = 10;

export class MongoFindByQueryOperation<T extends Document> implements IFindByQueryOperation<T> {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async find(
        filter: Filter<T>,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
        projection?: Document,
        sort?: Sort,
    ): Promise<IPaginatedFindByQueryResult<T>> {
        const pageOrDefault = page ?? DefaultPage;
        const pageSizeOrDefault = pageSize ?? DefaultPageSize;

        const localSession = session ?? await this.mongoClient.startSession();

        const totalItems = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .countDocuments(filter, { session: localSession });

        const findOptions: FindOptions = {
            session: localSession,
            projection,
            sort,
        };

        const items = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .find(filter, findOptions)
            .skip((pageOrDefault - 1) * pageSizeOrDefault)
            .limit(pageSizeOrDefault)
            .toArray();

        if (!session) {
            await localSession.endSession();
        }

        return {
            items,
            pagination: PaginationCalculator.calculate({ totalItems, currentPage: page, pageSize }),
        };
    }

    async findOne(
        filter: Filter<T>, session?: ClientSession, projection?: Document,
    ): Promise<WithId<T>|null> {
        const localSession = session ?? await this.mongoClient.startSession();
        const findOptions = {
            projection,
        };
        const result = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .findOne(filter, findOptions);

        if (!session) {
            await localSession.endSession();
        }

        return result;
    }

    async findWithAggregation<T>(aggregation: Document[], page: number = 1, pageSize: number = 10, session?: ClientSession): Promise<IPaginatedFindByQueryResult<T>> {
        const pageOrDefault = page ?? DefaultPage;
        const pageSizeOrDefault = pageSize ?? DefaultPageSize;

        const localSession = session ?? await this.mongoClient.startSession();

        const [ countResult ] = await this.mongoClient
            .db(this.dbName)
            .collection(this.collectionName)
            .aggregate(aggregation.concat({ $count: 'totalItems' }), { session: localSession })
            .toArray();

        const totalItems = countResult ? countResult.totalItems : 0;

        aggregation.push(
            { $skip: (pageOrDefault - 1) * pageSizeOrDefault },
            { $limit: pageSizeOrDefault },
        );

        const items = await this.mongoClient
            .db(this.dbName)
            .collection(this.collectionName)
            .aggregate(aggregation, { session: localSession })
            .toArray() as unknown as WithId<T>[];

        if (!session) {
            await localSession.endSession();
        }
        return {
            items,
            pagination: PaginationCalculator.calculate({ totalItems, currentPage: page, pageSize }),
        };
    }

    async findDistinct(
        fieldName:string, filter: Filter<T>, session?: ClientSession,
    ): Promise<string[] | number []> {
        const localSession = session ?? await this.mongoClient.startSession();
        const result = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .distinct(fieldName,filter);

        if (!session) {
            await localSession.endSession();
        }

        return result;
    }
}
