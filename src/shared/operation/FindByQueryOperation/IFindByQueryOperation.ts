import { Filter, WithId, Document, ClientSession, Sort } from 'mongodb';
import { IPaginationCalculatorResult } from '../../pagination/IPaginationCalculatorOptionsResult';

export interface IPaginatedFindByQueryResult<T> {
    items: WithId<T>[],
    pagination: IPaginationCalculatorResult,
}

export interface IFindByQueryOperation<T extends Document> {
    find(
        filter: Filter<T>,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
        projection?: Document,
        sort?: Sort,
    ): Promise<IPaginatedFindByQueryResult<T>>

    findOne(
        filter: Filter<T>, session?: ClientSession, projection?: Document
    ): Promise<WithId<T>|null>

    findWithAggregation<T>(
        aggregation: Document[], page?: number, pageSize?: number, session?: ClientSession
    ): Promise<IPaginatedFindByQueryResult<T>>

    findDistinct(
        fieldName:string, filter: Filter<T>, session?: ClientSession,
    ): Promise<string[] | number []>
}
