import { faker } from '@faker-js/faker';
import { MongoFindByQueryOperation } from './MongoFindByQueryOperation';

import mongoClientMocks from '../__mocks__/mongoClient';
import { ITestEntity } from '../__mocks__/ITestEntity';
import testEntityMocks from '../__mocks__/TestEntity';
import { ClientSession } from 'mongodb';
import { mock } from 'jest-mock-extended';

describe(MongoFindByQueryOperation.name, () => {
    let mongoFindByQueryOperation: MongoFindByQueryOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const testEntity = testEntityMocks.getTestEntity();
    const totalItems = faker.number.int();
    const countDocuments = jest.fn().mockResolvedValueOnce(totalItems);
    const toArray = jest.fn().mockResolvedValueOnce([ testEntity ]);
    const limit = jest.fn().mockReturnValue({ toArray });
    const skip = jest.fn().mockReturnValue({ limit });
    const find = jest.fn().mockReturnValue({ skip });
    const mongoClient = mongoClientMocks.getMongoClient({ countDocuments, find });

    beforeEach(() => {
        mongoFindByQueryOperation = new MongoFindByQueryOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoFindByQueryOperation.prototype.find.name, () => {
        it('returns found mongo documents', async () => {
            expect.assertions(10);

            // GIVEN
            const filter = {};
            const page = faker.number.int();
            const pageSize = faker.number.int();
            const session = mock<ClientSession>();
            mongoClient.startSession = jest.fn().mockReturnValue(session);

            // WHEN
            const result = await mongoFindByQueryOperation.find(filter, page, pageSize);

            // THEN
            expect(result).toEqual({
                items: [ testEntity ],
                pagination: {
                    currentPage: page,
                    totalPages: expect.any(Number),
                    hasPreviousPage: expect.any(Boolean),
                    hasNextPage: expect.any(Boolean),
                    totalItems,
                    itemsPerPage: pageSize,
                },
            });

            expect(countDocuments).toHaveBeenCalledTimes(1);
            expect(countDocuments).toHaveBeenCalledWith(filter, { session });

            expect(find).toHaveBeenCalledTimes(1);
            expect(find).toHaveBeenCalledWith(filter, { session });

            expect(skip).toHaveBeenCalledTimes(1);
            expect(skip).toHaveBeenCalledWith((page - 1) * pageSize);

            expect(limit).toHaveBeenCalledTimes(1);
            expect(limit).toHaveBeenCalledWith(pageSize);

            expect(toArray).toHaveBeenCalledTimes(1);
        });
    });
});
