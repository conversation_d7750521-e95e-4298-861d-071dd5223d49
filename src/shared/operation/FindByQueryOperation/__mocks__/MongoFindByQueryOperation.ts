import { Document } from 'mongodb';
import { IFindByQueryOperation } from '../IFindByQueryOperation';

function getFindByQueryOperation<T extends Document>(): jest.Mocked<IFindByQueryOperation<T>> {
    return {
        findOne: jest.fn(),
        find: jest.fn(),
        findWithAggregation: jest.fn(),
        findDistinct: jest.fn(),
    } as jest.Mocked<IFindByQueryOperation<T>>;
}

export default {
    getFindByQueryOperation,
};
