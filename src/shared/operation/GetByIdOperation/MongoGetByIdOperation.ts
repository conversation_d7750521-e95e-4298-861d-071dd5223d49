import { ClientSession, Document, Filter, MongoClient, ObjectId, WithId } from 'mongodb';
import { IGetByIdOperation } from './IGetByIdOperation';

export class MongoGetByIdOperation<T extends Document> implements IGetByIdOperation<T> {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async get(id: ObjectId, session?: ClientSession): Promise<WithId<T> | null> {
        const result = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .findOne({ _id: id } as Filter<T>, { session });

        return result;
    }
}
