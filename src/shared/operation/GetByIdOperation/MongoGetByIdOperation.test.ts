import { faker } from '@faker-js/faker';
import { MongoGetByIdOperation } from './MongoGetByIdOperation';

import mongoClientMocks from '../__mocks__/mongoClient';
import { ITestEntity } from '../__mocks__/ITestEntity';
import testEntityMocks from '../__mocks__/TestEntity';
import { mock } from 'jest-mock-extended';
import { ClientSession } from 'mongodb';

describe(MongoGetByIdOperation.name, () => {
    let mongoGetByIdOperation: MongoGetByIdOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const testEntity = testEntityMocks.getTestEntity();
    const findOne = jest.fn().mockResolvedValueOnce(testEntity);
    const mongoClient = mongoClientMocks.getMongoClient({ findOne });

    beforeEach(() => {
        mongoGetByIdOperation = new MongoGetByIdOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoGetByIdOperation.prototype.get.name, () => {
        it('returns one mongo document', async () => {
            expect.assertions(3);

            // GIVEN
            const id = testEntity._id;
            const session = mock<ClientSession>();

            // WHEN
            const result = await mongoGetByIdOperation.get(id, session);

            // THEN
            expect(result).toEqual(testEntity);

            expect(findOne).toHaveBeenCalledTimes(1);
            expect(findOne).toHaveBeenCalledWith({ _id: id }, { session });
        });
    });
});
