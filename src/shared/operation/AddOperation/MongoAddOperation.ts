import { ClientSession, Document, MongoClient, OptionalUnlessRequiredId, WithId } from 'mongodb';
import { IAddOperation } from './IAddOperation';

export class MongoAddOperation<T extends Document> implements IAddOperation<T> {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async add(entity: OptionalUnlessRequiredId<T>, session?: ClientSession): Promise<WithId<T>> {
        const { insertedId } = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .insertOne(entity, { session });

        if (!insertedId) {
            throw new Error('Failed to insert document');
        }

        return {
            _id: insertedId,
            ...entity,
        } as WithId<T>;
    }
}
