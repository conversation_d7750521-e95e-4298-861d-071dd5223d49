import { faker } from '@faker-js/faker';
import { MongoAddOperation } from './MongoAddOperation';
import { ClientSession, ObjectId } from 'mongodb';

import mongoClientMocks from '../__mocks__/mongoClient';
import testEntityMocks from '../__mocks__/TestEntity';
import { ITestEntity } from '../__mocks__/ITestEntity';
import { mock } from 'jest-mock-extended';

describe(MongoAddOperation.name, () => {
    let mongoAddOperation: MongoAddOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const insertedId = new ObjectId();
    const insertOne = jest.fn().mockResolvedValueOnce({ insertedId });
    const mongoClient = mongoClientMocks.getMongoClient({ insertOne });

    beforeEach(() => {
        mongoAddOperation = new MongoAddOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoAddOperation.prototype.add.name, () => {
        it('returns added mongo document', async () => {
            expect.assertions(3);

            // GIVEN
            const testEntityDto = testEntityMocks.getTestEntityDto();
            const session = mock<ClientSession>();

            // WHEN
            const result = await mongoAddOperation.add(testEntityDto, session);

            // THEN
            expect(result).toEqual({
                _id: insertedId,
                ...testEntityDto,
            });

            expect(insertOne).toHaveBeenCalledTimes(1);
            expect(insertOne).toHaveBeenCalledWith(testEntityDto, { session });
        });
    });
});
