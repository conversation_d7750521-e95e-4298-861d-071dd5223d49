import { faker } from '@faker-js/faker';
import { MongoBulkAddOperation } from './MongoBulkAddOperation';
import { ClientSession, ObjectId } from 'mongodb';

import mongoClientMocks from '../__mocks__/mongoClient';
import testEntityMocks from '../__mocks__/TestEntity';
import { ITestEntity } from '../__mocks__/ITestEntity';
import { mock } from 'jest-mock-extended';

describe(MongoBulkAddOperation.name, () => {
    let mongoBulkAddOperation: MongoBulkAddOperation<ITestEntity>;
    const dbName = faker.word.noun();
    const collectionName = faker.word.noun();
    const insertedId = new ObjectId();
    const insertMany = jest.fn().mockResolvedValueOnce({ insertedIds: [ insertedId ] });
    const mongoClient = mongoClientMocks.getMongoClient({ insertMany });

    beforeEach(() => {
        mongoBulkAddOperation = new MongoBulkAddOperation<ITestEntity>(mongoClient, dbName, collectionName);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoBulkAddOperation.prototype.bulkAdd.name, () => {
        it('returns added mongo documents', async () => {
            expect.assertions(3);

            // GIVEN
            const testEntityDto = testEntityMocks.getTestEntityDto();
            const session = mock<ClientSession>();
            const testEntities: ITestEntity[] = [ testEntityDto ];

            // WHEN
            const result = await mongoBulkAddOperation.bulkAdd(testEntities, session);

            // THEN
            expect(result).toEqual([ {
                _id: insertedId,
                ...testEntityDto,
            } ]);

            expect(insertMany).toHaveBeenCalledTimes(1);
            expect(insertMany).toHaveBeenCalledWith(testEntities, { session });
        });
    });
});
