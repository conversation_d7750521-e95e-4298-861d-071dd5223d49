import { ClientSession, Document, MongoClient, OptionalUnlessRequiredId, WithId } from 'mongodb';
import { IBulkAddOperation } from './IBulkAddOperation';

export class MongoBulkAddOperation<T extends Document> implements IBulkAddOperation<T> {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly dbName: string,
        private readonly collectionName: string,
    ) {}

    async bulkAdd(entities: OptionalUnlessRequiredId<T>[], session?: ClientSession): Promise<WithId<T>[]> {
        const { insertedIds } = await this.mongoClient
            .db(this.dbName)
            .collection<T>(this.collectionName)
            .insertMany(entities, { session });
        const insertedDocuments: WithId<T>[] = [];
        const isSuccess = insertedIds !== undefined && Object.keys(insertedIds).length > 0;

        if (!isSuccess) {
            throw new Error('Failed to insert documents');
        }

        for (let i = 0; i < entities.length; i++) {
            const insertedId = insertedIds[i];
            const insertedDocument = {
                _id: insertedId,
                ...entities[i],
            } as WithId<T>;
            insertedDocuments.push(insertedDocument);
        }

        return insertedDocuments;
    }
}
