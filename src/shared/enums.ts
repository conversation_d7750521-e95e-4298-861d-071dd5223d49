import { z } from 'zod';

/**
 * Used for source and processor.
 */
export enum Source {
    PayPal = 'paypal',
    ShopifyPayments = 'shopify',
    Maverick = 'maverick',
    FiserveOrBusinessTrack = 'businessTrack',
    CPanel = 'cpanel',
    GlobalPayments = 'globalPayments',
    Merlink = 'merlink',
    Braintree = 'braintree',
    Klarna = 'klarna',
    Square = 'square',
    AfterpayOrClearpay = 'afterpay',
    Oceanpayment = 'oceanpayment',
    Payarc = 'payarc',
    Firstview = 'firstview',
    Nexio = 'nexio',
    Elevate = 'elevate',
    Stripe = 'stripe',
    GOATPayments = 'goatPayments',
    Woocommerce = 'woopay',
    Nuvei = 'nuvei',
    Safecharge = 'safecharge', // This is nuvei now but for backward compatibility is here
    ZenPayments = 'zenPayments',
    SelectBankcard = 'select',
    CheckouDotCom = 'checkout',
    Deposyt = 'deposyt',
    Affirm = 'affirm',
    Adyen = 'adyen',
    Asana = 'asana',
    Import = 'import',
    SamCart = 'samcart',
    EasyPayDirect = 'easyPayDirect',
    PaySafe = 'paysafe',
    BravaPay = 'bravapay',
    Micamp = 'micamp',
    Approvely = 'approvely',
    Platpay = 'platpay',
    Clover = 'clover',
    Airwallex = 'airwallex',
    Worldpay = 'worldpay',
    Sezzle = 'sezzle',
    Luqra = 'luqra',
    PaymentTech = 'paymentTech',
    PaymentsAI = 'paymentsAI',
    Mamopay = 'mamopay',
    EMSdata = 'emsdata',
    PaymentsHub = 'paymentsHub',
    PublicApi = 'publicApi',
    Nmi = 'nmi',
    WePay = 'wepay',
    ZipPay = 'zippay',
    PaymentInsider = 'paymentInsider',
    TRXServices = 'trxServices',
    Merchantic = 'merchantic',
    WooPayments = 'wooPayments',
    WooCommerce = 'wooCommerce',
    SwipeSimple = 'swipeSimple',
    Gmail = 'gmail',
    ZohoPayments = 'zohoPayments',
    Recharge = 'recharge',
    Trustpay = 'trustpay',
    WixPayments = 'wixPayments',
    Venmo = 'venmo',
    Skrill = 'skrill',
    BLogicSystems = 'blogicSystems',
    UseePay = 'useePay',
    Thinkific = 'thinkific',
    Revolut = 'revolut',
}

export const NormalizedIntegrationNames: Record<Source, string> = {
    [Source.PayPal]: 'PayPal',
    [Source.ShopifyPayments]: 'Shopify',
    [Source.Maverick]: 'Maverick',
    [Source.FiserveOrBusinessTrack]: 'Business Track',
    [Source.CPanel]: 'CPanel',
    [Source.GlobalPayments]: 'Global Payments',
    [Source.Merlink]: 'Merlink',
    [Source.Braintree]: 'Braintree',
    [Source.Klarna]: 'Klarna',
    [Source.Square]: 'Square',
    [Source.AfterpayOrClearpay]: 'AfterpayOrClearpay',
    [Source.Oceanpayment]: 'Oceanpayment',
    [Source.Payarc]: 'Payarc',
    [Source.Firstview]: 'First View',
    [Source.Nexio]: 'Nexio',
    [Source.Elevate]: 'Elevate',
    [Source.Stripe]: 'Stripe',
    [Source.GOATPayments]: 'GOAT Payments',
    [Source.Woocommerce]: 'WooPay',
    [Source.Nuvei]: 'Nuvei',
    [Source.Safecharge]: 'Safecharge', // This is nuvei now but for backward compatibility is here
    [Source.ZenPayments]: 'ZenPayments',
    [Source.SelectBankcard]: 'SelectBankcard',
    [Source.CheckouDotCom]: 'Checkout.com',
    [Source.Deposyt]: 'Deposyt',
    [Source.Affirm]: 'Affirm',
    [Source.Adyen]: 'Adyen',
    [Source.Asana]: 'Asana',
    [Source.Import]: 'Import',
    [Source.SamCart]: 'SamCart',
    [Source.EasyPayDirect]: 'EasyPayDirect',
    [Source.PaySafe]: 'PaySafe',
    [Source.BravaPay]: 'BravaPay',
    [Source.Micamp]: 'Micamp',
    [Source.Approvely]: 'Approvely',
    [Source.Platpay]: 'PlatPay',
    [Source.Clover]: 'Clover',
    [Source.Airwallex]: 'Airwallex',
    [Source.Worldpay]: 'Worldpay',
    [Source.Sezzle]: 'Sezzle',
    [Source.Luqra]: 'Luqra',
    [Source.PaymentTech]: 'PaymentTech',
    [Source.PaymentsAI]: 'PaymentsAI',
    [Source.Mamopay]: 'Mamo Pay',
    [Source.EMSdata]: 'EMSdata',
    [Source.PaymentsHub]: 'PaymentsHub',
    [Source.PublicApi]: 'PublicApi',
    [Source.Nmi]: 'Nmi',
    [Source.WePay]: 'WePay',
    [Source.ZipPay]: 'ZipPay',
    [Source.PaymentInsider]: 'PaymentInsider',
    [Source.TRXServices]: 'TRXServices',
    [Source.Merchantic]: 'Merchantic',
    [Source.WooPayments]: 'WooPayments',
    [Source.WooCommerce]: 'WooCommerce',
    [Source.SwipeSimple]: 'SwipeSimple',
    [Source.Gmail]: 'Gmail',
    [Source.ZohoPayments]: 'ZohoPayments',
    [Source.Recharge]: 'Recharge',
    [Source.Trustpay]: 'Trustpay',
    [Source.WixPayments]: 'Wix Payments',
    [Source.Venmo]: 'Venmo',
    [Source.Skrill]: 'Skrill',
    [Source.BLogicSystems]: 'BLogic Systems',
    [Source.UseePay]: 'UseePay',
    [Source.Thinkific]: 'Thinkific',
    [Source.Revolut]: 'Revolut',
};

export const orderSourceSchema = z.enum([
    'wooCommerce',
    'shopify',
]);

export type OrderSource = z.infer<typeof orderSourceSchema>;
export const OrderSourceEnum = orderSourceSchema.enum;
