
import { ObjectId, WithId } from 'mongodb';
import { HistoryEntry, IMerchantProfile, IMongoMerchantProfile, ProductEnrollmentStatuses } from '../IMerchantProfile';

const defaultHistory:HistoryEntry[] = [
    {
        field: 'processor',
        oldValue: null,
        newValue: 'Shopify Payments',
        date: new Date('2024-09-09T04:00:05.691Z'),
    },
    {
        field: 'statementDescriptor',
        oldValue: null,
        newValue: 'ATTRACT HIM',
        date: new Date('2024-09-09T04:00:05.691Z'),
    },
    {
        field: 'merchantId',
        oldValue: null,
        newValue: '4fccd06b-62ab-4072-bf3b-76ffa02d4b9d',
        date: new Date('2024-09-10T13:37:42.879Z'),
    },
    {
        field: 'midEnrollmentStatus',
        oldValue: null,
        newValue: 'in_progress',
        date: new Date('2024-09-10T19:20:54.123Z'),
    },
];

const defaultProductEnrollmentStatuses:ProductEnrollmentStatuses = {
    ethoca: 'enrolled',
    ethoca_consumer_clarity: 'enrollment_error',
    verifi_rdr: 'in_progress',
    verifi_oi: 'enrollment_error',
    verifi_fraud: 'in_progress',
    verifi_dispute: 'enrollment_error',
    verifi_cdrn: 'enrolled',
};

const defaultData = {
    processor: 'Shopify Payments',
    statementDescriptor: 'ATTRACT HIM',
    dateCreated: new Date('2024-09-09T04:00:05.691Z'),
    history: defaultHistory,
    midEnrollmentStatus: 'in_progress',
    productEnrollmentStatuses: defaultProductEnrollmentStatuses,
    dateUpdated: new Date('2024-09-10T19:20:54.123Z'),
    merchantId: '4fccd06b-62ab-4072-bf3b-76ffa02d4b9d',
};

const getSettings = (overrides: Partial<IMerchantProfile> = {}): WithId<IMerchantProfile> => ({
    _id: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    chargeflow_id: new ObjectId().toHexString(),
    ...defaultData,
    ...overrides,
});

const getMongoSettings = (overrides: Partial<IMongoMerchantProfile> = {}): WithId<IMongoMerchantProfile> => ({
    _id: new ObjectId(),
    accountId: new ObjectId(),
    chargeflowId: new ObjectId(),
    ...defaultData,
    ...overrides,
});

export default {
    getSettings,
    getMongoSettings,
};
