import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoMerchantProfile } from './IMerchantProfile';
import { IMerchantProfileRepository } from './IMerchantProfileRepository';
import { MongoMerchantProfileRepository } from './MongoMerchantProfileRepository';

export class MerchantProfileRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IMerchantProfileRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoMerchantProfile>(mongoClient, dbName, collectionName);

        return new MongoMerchantProfileRepository(
            findByQueryOperation,
        );
    }
}
