import { ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMerchantProfile, IMongoMerchantProfile } from './IMerchantProfile';
import { IMerchantProfileRepository } from './IMerchantProfileRepository';
import { MongoMerchantProfileMapper } from './MongoMerchantProfileMapper';

export class MongoMerchantProfileRepository implements IMerchantProfileRepository {
    constructor(
    private readonly findByQueryOperation: IFindByQueryOperation<IMongoMerchantProfile>,
    ) {}

    async findMerchanProfileByChargeflowId(
        chargeflowId: string,
    ): Promise<WithId<IMerchantProfile> | null> {
        const result = await this.findByQueryOperation.findOne(
            { chargeflowId: new ObjectId(chargeflowId) },
        );

        return result ? MongoMerchantProfileMapper.fromMongoMerchantProfile(result) : null;
    }
}
