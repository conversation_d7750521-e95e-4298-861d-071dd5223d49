import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { MerchantProfileRepositoryFactory } from './MerchantProfileRepositoryFactory';
import { MongoMerchantProfileRepository  } from './MongoMerchantProfileRepository';

describe(MerchantProfileRepositoryFactory.name, () => {
    describe(MerchantProfileRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            const result = MerchantProfileRepositoryFactory.create(mongoClient, dbName, collectionName);

            expect(result).toBeInstanceOf(MongoMerchantProfileRepository);
        });
    });
});
