import { ObjectId } from 'mongodb';
import { MongoMerchantProfileRepository } from './MongoMerchantProfileRepository';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoMerchantProfile, IMerchantProfile } from './IMerchantProfile';
import { MongoMerchantProfileMapper } from './MongoMerchantProfileMapper';
import { WithId } from '../shared/helper-types';

jest.mock('./MongoMerchantProfileMapper');

describe('MongoMerchantProfileRepository', () => {
    let findByQueryOperation: IFindByQueryOperation<IMongoMerchantProfile>;
    let repository: MongoMerchantProfileRepository;

    beforeEach(() => {
        findByQueryOperation = {
            findOne: jest.fn(),
        } as unknown as IFindByQueryOperation<IMongoMerchantProfile>;
        repository = new MongoMerchantProfileRepository(findByQueryOperation);
        jest.clearAllMocks();
    });

    describe('findMerchanProfileByChargeflowId', () => {
        it('should return a merchant profile when found', async () => {
            const chargeflowId = new ObjectId().toHexString();
            const mongoMerchantProfile: WithId<IMongoMerchantProfile> = {
                _id: new ObjectId() as unknown as ObjectId & string,
                processor: 'Shopify Payments',
                chargeflowId: new ObjectId(chargeflowId),
                statementDescriptor: 'ATTRACT HIM',
                accountId: new ObjectId(),
                dateCreated: new Date(),
                history: [],
                midEnrollmentStatus: 'in_progress',
                productEnrollmentStatuses: {
                    ethoca: 'enrolled',
                    ethoca_consumer_clarity: 'enrollment_error',
                    verifi_rdr: 'in_progress',
                    verifi_oi: 'enrollment_error',
                    verifi_fraud: 'in_progress',
                    verifi_dispute: 'enrollment_error',
                    verifi_cdrn: 'enrolled',
                },
                dateUpdated: new Date(),
                merchantId: '4fccd06b-62ab-4072-bf3b-76ffa02d4b9d',
            };

            const merchantProfile: WithId<IMerchantProfile> = {
                _id: mongoMerchantProfile._id.toHexString(),
                processor: mongoMerchantProfile.processor,
                chargeflow_id: mongoMerchantProfile.chargeflowId.toHexString(),
                statementDescriptor: mongoMerchantProfile.statementDescriptor,
                accountId: mongoMerchantProfile.accountId?.toHexString(),
                dateCreated: mongoMerchantProfile.dateCreated,
                history: mongoMerchantProfile.history,
                midEnrollmentStatus: mongoMerchantProfile.midEnrollmentStatus,
                productEnrollmentStatuses: mongoMerchantProfile.productEnrollmentStatuses,
                dateUpdated: mongoMerchantProfile.dateUpdated,
                merchantId: mongoMerchantProfile.merchantId,
            };

            (findByQueryOperation.findOne as jest.Mock).mockResolvedValue(mongoMerchantProfile);
            (MongoMerchantProfileMapper.fromMongoMerchantProfile as jest.Mock).mockReturnValue(merchantProfile);

            const result = await repository.findMerchanProfileByChargeflowId(chargeflowId);

            expect(findByQueryOperation.findOne).toHaveBeenCalledWith({ chargeflowId: new ObjectId(chargeflowId) });
            expect(MongoMerchantProfileMapper.fromMongoMerchantProfile).toHaveBeenCalledWith(mongoMerchantProfile);
            expect(result).toEqual(merchantProfile);
        });

        it('should return null when no merchant profile is found', async () => {
            const chargeflowId = new ObjectId().toHexString();

            (findByQueryOperation.findOne as jest.Mock).mockResolvedValue(null);

            const result = await repository.findMerchanProfileByChargeflowId(chargeflowId);

            expect(findByQueryOperation.findOne).toHaveBeenCalledWith({ chargeflowId: new ObjectId(chargeflowId) });
            expect(MongoMerchantProfileMapper.fromMongoMerchantProfile).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });
    });
});
