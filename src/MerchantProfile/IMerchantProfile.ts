import { ObjectId, Document } from 'mongodb';

export interface HistoryEntry {
  field: string;
  oldValue: string | null;
  newValue: string;
  date: Date;
}

export interface ProductEnrollmentStatuses {
  ethoca: string;
  ethoca_consumer_clarity: string;
  verifi_rdr: string;
  verifi_oi: string;
  verifi_fraud: string;
  verifi_dispute: string;
  verifi_cdrn: string;
}

export interface IMerchantProfileBase {
  processor: string;
  statementDescriptor: string;
  dateCreated: Date;
  history: HistoryEntry[];
  midEnrollmentStatus: string;
  productEnrollmentStatuses: ProductEnrollmentStatuses;
  dateUpdated: Date;
  merchantId: string;
}

export interface IMerchantProfile extends IMerchantProfileBase {
  _id?: string;
  chargeflow_id?: string;
  accountId?: string;
}

export interface IMongoMerchantProfile extends IMerchantProfileBase, Document {
  _id?: ObjectId;
  chargeflow_id?: ObjectId;
  accountId?: ObjectId;
}
