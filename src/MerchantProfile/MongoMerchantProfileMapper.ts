
import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoMerchantProfile, IMerchantProfile } from './IMerchantProfile';

export class MongoMerchantProfileMapper {
    static toMongoMerchantProfile(MerchantProfile: IMerchantProfile): IMongoMerchantProfile {
        if (!MerchantProfile) {
            throw new Error('MerchantProfile is required for mapping');
        }

        return {
            ...MerchantProfile,
            _id: MerchantProfile._id ? new ObjectId(MerchantProfile._id) : undefined,
            accountId: MerchantProfile.accountId ? new ObjectId(MerchantProfile.accountId) : undefined,
            chargeflow_id: MerchantProfile.chargeflow_id ? new ObjectId(MerchantProfile.chargeflow_id) : undefined,
        };
    }

    static fromMongoMerchantProfile(mongoMerchantProfile: MongoWithId<IMongoMerchantProfile>): WithId<IMerchantProfile> {
        if (!mongoMerchantProfile) {
            throw new Error('charge not found');
        }

        return {
            ...mongoMerchantProfile,
            _id: mongoMerchantProfile._id.toHexString(),
            accountId: mongoMerchantProfile.accountId?.toHexString(),
            chargeflow_id: mongoMerchantProfile.chargeflow_id?.toHexString(),
        };
    }

    static fromMongoMerchantProfiles(mongoMerchantProfiles: MongoWithId<IMongoMerchantProfile>[]): WithId<IMerchantProfile>[] {
        return mongoMerchantProfiles.map(mongoMerchantProfile => ({
            ...mongoMerchantProfile,
            _id: mongoMerchantProfile._id.toHexString(),
            accountId: mongoMerchantProfile.accountId?.toHexString(),
            chargeflow_id: mongoMerchantProfile.chargeflow_id?.toHexString(),
        }));
    }
}
