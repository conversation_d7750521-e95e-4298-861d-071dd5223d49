import { mock } from 'jest-mock-extended';
import { DataAccessUnitOfWork } from './DataAccessUnitOfWork';
import { ClientSession, MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';

describe(DataAccessUnitOfWork.name, () => {
    let dataAccessUnitOfWork: DataAccessUnitOfWork;
    const startTransaction = jest.fn();
    const commitTransaction = jest.fn();
    const abortTransaction = jest.fn();
    const endSession = jest.fn();
    const session = mock<ClientSession>({
        startTransaction,
        commitTransaction,
        abortTransaction,
        endSession,
    });

    const mockCollection = jest.fn();
    const mockDb = {
        collection: mockCollection.mockReturnValue({}),
    };

    const startSession = jest.fn().mockReturnValue(session);
    const mongoClient = mock<MongoClient>({
        startSession,
        db: jest.fn().mockReturnValue(mockDb),
    });

    beforeAll(() => {
        process.env.DISPUTES_EVENT_BUS = faker.word.noun();
        process.env.STACK_NAME = faker.word.noun();
    });

    beforeEach(() => {
        dataAccessUnitOfWork = new DataAccessUnitOfWork(mongoClient);
    });

    afterEach(jest.clearAllMocks);

    describe(DataAccessUnitOfWork.prototype.startTransactionV2.name, () => {
        it('starts a new session and transaction', () => {
            expect.assertions(3);

            // WHEN
            const result = dataAccessUnitOfWork.startTransactionV2();

            // THEN
            expect(result).toEqual(session);

            expect(startSession).toHaveBeenCalledTimes(1);
            expect(startTransaction).toHaveBeenCalledTimes(1);
        });

        it('does start a new session and transaction even when ones are already started', () => {
            expect.assertions(3);

            // GIVEN
            dataAccessUnitOfWork.startTransactionV2();

            // WHEN
            const result = dataAccessUnitOfWork.startTransactionV2();

            // THEN
            expect(result).toEqual(session);

            expect(startSession).toHaveBeenCalledTimes(2);
            expect(startTransaction).toHaveBeenCalledTimes(2);
        });
    });

    describe(DataAccessUnitOfWork.prototype.startTransaction.name, () => {
        it('starts a new session and transaction', () => {
            expect.assertions(3);

            // WHEN
            const result = dataAccessUnitOfWork.startTransaction();

            // THEN
            expect(result).toEqual(session);

            expect(startSession).toHaveBeenCalledTimes(1);
            expect(startTransaction).toHaveBeenCalledTimes(1);
        });

        it('does not start a new session and transaction when ones are already started', () => {
            expect.assertions(3);

            // GIVEN
            dataAccessUnitOfWork.startTransaction();

            // WHEN
            const result = dataAccessUnitOfWork.startTransaction();

            // THEN
            expect(result).toEqual(session);

            expect(startSession).toHaveBeenCalledTimes(1);
            expect(startTransaction).toHaveBeenCalledTimes(1);
        });
    });

    describe(DataAccessUnitOfWork.prototype.commitTransaction.name, () => {
        it('commits transaction and ends session', async () => {
            expect.assertions(2);

            // GIVEN
            dataAccessUnitOfWork.startTransaction();

            // WHEN
            await dataAccessUnitOfWork.commitTransaction();

            // THEN
            expect(commitTransaction).toHaveBeenCalledTimes(1);
            expect(endSession).toHaveBeenCalledTimes(1);
        });

        it('throws an error when no transaction is started', async () => {
            expect.assertions(1);

            // WHEN
            await expect(dataAccessUnitOfWork.commitTransaction())
                // THEN
                .rejects.toThrow('No transaction started');
        });
    });

    describe(DataAccessUnitOfWork.prototype.abortTransaction.name, () => {
        it('aborts transaction and ends session', async () => {
            expect.assertions(2);

            // GIVEN
            dataAccessUnitOfWork.startTransaction();

            // WHEN
            await dataAccessUnitOfWork.abortTransaction();

            // THEN
            expect(abortTransaction).toHaveBeenCalledTimes(1);
            expect(endSession).toHaveBeenCalledTimes(1);
        });

        it('throws an error when no transaction is started', async () => {
            expect.assertions(1);

            // WHEN
            await expect(dataAccessUnitOfWork.abortTransaction())
                // THEN
                .rejects.toThrow('No transaction started');
        });
    });
});
