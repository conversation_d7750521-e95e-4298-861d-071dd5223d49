import { ObjectId } from 'mongodb';
import { IAlertLinking, IMongoAlertLinking } from '../IAlertLinking';
import { LinkingStatusDict } from '@chargeflow-team/common-models';

const defaultAlertId = '0adc61ff-250f-42b7-8c11-c0f81401d9cf';
const defaultChargeflowId = '639b05c90110ad17f469a432';

const defaultLinking = ({
    isAuto: true,
    status: LinkingStatusDict.SUCCEEDED,
    transactionId: 'pi_3L29BER9g8OVaOCW146BoUPF',
    order: {
        orderId: '4778492788952',
        orderName: '#1001',
        orderTransactionId: '5587248382168',
        paymentGateway: 'shopify_payments',
    },
    updatedAt: new Date(),
});

const getMongoAlertLinking = (overrides?: Partial<IMongoAlertLinking>): IMongoAlertLinking => ({
    _id: new ObjectId(),
    alertId: defaultAlertId,
    chargeflowId: new ObjectId(defaultChargeflowId),
    linking: {
        ...defaultLinking,
    },
    ...overrides,
});

const getAlertLinking = (overrides?: Partial<IAlertLinking>): IAlertLinking => ({
    alertId: defaultAlertId,
    chargeflowId: defaultChargeflowId,
    ...defaultLinking,
    ...overrides,
});

export default {
    getMongoAlertLinking,
    getAlertLinking,
};
