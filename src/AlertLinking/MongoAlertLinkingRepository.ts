import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IAlertLinking, IMongoAlertLinking } from './IAlertLinking';
import { IAlertLinkingRepository } from './IAlertLinkingRepository';
import { MongoAlertLinkingMapper } from './MongoAlertLinkingMapper';

export class MongoAlertLinkingRepository implements IAlertLinkingRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoAlertLinking>,
    ) {}

    public async getByAlertId(alertId: string, session?: ClientSession): Promise<IAlertLinking | null> {
        const filter = {
            alertId,
        };
        const projection = this.getRequiredProjection();
        const result = await this.findByQueryOperation.findOne(
            filter,
            session,
            projection,
        );

        return result ? MongoAlertLinkingMapper.fromMongoAlertLinking(result) : null;
    }

    public async getByChargeflowIdAndAlertId(chargeflowId: string, alertId: string, session?: ClientSession): Promise<IAlertLinking | null> {
        const filter = {
            chargeflowId: new ObjectId(chargeflowId),
            alertId,
        };
        const projection = this.getRequiredProjection();
        const result = await this.findByQueryOperation.findOne(
            filter,
            session,
            projection,
        );

        return result ? MongoAlertLinkingMapper.fromMongoAlertLinking(result) : null;
    }

    private getRequiredProjection(): Record<string, number> {
        return {
            'chargeflowId': 1,
            'alertId': 1,
            'linking': 1,
        };
    }

}
