import { MongoClient } from 'mongodb';
import { MongoAlertLinkingRepository } from './MongoAlertLinkingRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoAlertLinking } from './IAlertLinking';
import { IAlertLinkingRepository } from './IAlertLinkingRepository';

export class AlertLinkingRepositoryFactory {

    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IAlertLinkingRepository {

        const findByQueryOperation = new MongoFindByQueryOperation<IMongoAlertLinking>(mongoClient, dbName, collectionName);
        return new MongoAlertLinkingRepository(findByQueryOperation);

    }
}
