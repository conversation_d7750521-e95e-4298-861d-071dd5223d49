import { LinkingStatus } from '@chargeflow-team/common-models';
import { Nullable, LinkingFailureDetails } from '@chargeflow-team/events-infra';
import { ObjectId } from 'mongodb';

export type IOrderLinking = {
    orderId: string,
    orderName: string,
    orderTransactionId: string,
    paymentGateway?: Nullable<string>,
};

export interface ILinking {
    status: LinkingStatus;
    transactionId?: Nullable<string>;
    order?: Nullable<IOrderLinking>;
    linkingFailuresDetails?: LinkingFailureDetails[];
    isAuto: boolean;
    updatedAt: Date;
}

export interface IAlertLinking extends ILinking {
    chargeflowId: string;
    alertId: string;
}

export interface IMongoAlertLinking {
    _id: ObjectId;
    chargeflowId: ObjectId;
    alertId: string;
    linking?: ILinking;
}
