import { MongoClient } from 'mongodb';
import { AlertLinkingRepositoryFactory } from './AlertLinkingRepositoryFactory';
import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoAlertLinkingRepository } from './MongoAlertLinkingRepository';

describe(AlertLinkingRepositoryFactory.name, () => {

    describe(AlertLinkingRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = AlertLinkingRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoAlertLinkingRepository);
        });
    });

});
