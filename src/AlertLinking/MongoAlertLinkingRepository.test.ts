
import mocks from './__mocks__/AlertLinking';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { MongoAlertLinkingRepository } from './MongoAlertLinkingRepository';
import { IMongoAlertLinking } from './IAlertLinking';

describe(MongoAlertLinkingRepository.name, () => {
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoAlertLinking>>;
    let repository: MongoAlertLinkingRepository;

    beforeEach(() => {
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoAlertLinking>();
        repository = new MongoAlertLinkingRepository(mockQueryOperation);
    });

    describe('getByAlertId', () => {
        it('should return doc by alertId', async () => {

            const mongoLinkingMock = mocks.getMongoAlertLinking();
            const alertId = mongoLinkingMock.alertId;
            mockQueryOperation.findOne.mockResolvedValue(mongoLinkingMock);

            const result = await repository.getByAlertId(alertId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { alertId },
                undefined,
                {
                    'chargeflowId': 1,
                    'alertId': 1,
                    'linking': 1,
                },
            );
            expect(result).toEqual(mocks.getAlertLinking());
        });
    });

    describe('getByChargeflowIdAndAlertId', () => {
        it('should return doc by chargeflowId and alertId', async () => {
            const mongoLinkingMock = mocks.getMongoAlertLinking();
            const chargeflowId = mongoLinkingMock.chargeflowId;
            const alertId = mongoLinkingMock.alertId;
            mockQueryOperation.findOne.mockResolvedValue(mongoLinkingMock);

            const result = await repository.getByChargeflowIdAndAlertId(chargeflowId.toHexString(), alertId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { chargeflowId, alertId },
                undefined,
                {
                    'chargeflowId': 1,
                    'alertId': 1,
                    'linking': 1,
                },
            );
            expect(result).toEqual(mocks.getAlertLinking());
        });
    });
});
