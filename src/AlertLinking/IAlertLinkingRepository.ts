import { ClientSession } from 'mongodb';
import { IAlertLinking } from './IAlertLinking';

export interface IAlertLinkingRepository {

    getByAlertId: (
        alertId: string,
        session?: ClientSession,
    ) => Promise<IAlertLinking | null>,

    getByChargeflowIdAndAlertId: (
        chargeflowId: string,
        alertId: string,
        session?: ClientSession,
    ) => Promise<IAlertLinking | null>,

}
