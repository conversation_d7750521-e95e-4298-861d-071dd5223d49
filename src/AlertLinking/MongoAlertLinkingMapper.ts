import { IAlertLinking, IMongoAlertLinking } from './IAlertLinking';

export class MongoAlertLinkingMapper {

    static fromMongoAlertLinking(mongoAlertLinking: IMongoAlertLinking): IAlertLinking | null {

        if (!mongoAlertLinking) {
            throw new Error('Mongo alert linking document is required for mapping');
        }

        if (!mongoAlertLinking.linking) {
            return null;
        }

        const {
            isAuto,
            status,
            transactionId,
            order,
            linkingFailuresDetails,
            updatedAt,
        } = mongoAlertLinking.linking;

        return {
            chargeflowId: mongoAlertLinking.chargeflowId.toHexString(),
            alertId: mongoAlertLinking.alertId,
            isAuto,
            status,
            transactionId,
            order,
            linkingFailuresDetails,
            updatedAt,
        };
    }
}
