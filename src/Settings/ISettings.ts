import { ObjectId, Document } from 'mongodb';

export interface IShopDetails {
  full_name: string;
  shop_name: string;
  contact_email: string;
  phone_number?: string;
  automate_paypal?: boolean;
  automate_stripe?: boolean;
  paypal_tracking_numbers?: boolean;
  shop_status: boolean;
  merchant_expert?: boolean;
  automate_dispute_type?: string;
  currency: string;
  address: string;
}

export interface INotifications {
  new_disputes: boolean;
  response_submissions: boolean;
  lost_disputes: boolean;
  won_disputes: boolean;
  manual_review: boolean;
  response_due: boolean;
  weekly_summary: boolean;
  monthly_summary: boolean;
  additional_information?: boolean;
  evidence_collection: boolean;
}

export interface IPolicyParagraph {
  data: {
    text: string;
  };
  id: string;
  type: 'paragraph';
  tunes: {
    TextAlignmentTune: {
      alignment: string;
    };
  };
}

export interface IFields {
  // Base shop information
  company_name?: string;
  site_url?: string;
  sign_up_url?: string | null;
  support_email: string;
  support_phone_number?: string;
  checkout_url?: string | null;
  shop_type?: string;
  business_description?: string;
  business_industry?: string;
  business_product_types?: string[];
  business_payment_models?: string[];

  // Policies
  business_available_policies?: string[];
  shipping_policy_url?: string | string[0] | null;
  cancellation_policy_url?: string | null;
  refund_policy_url?: string | string[0] | null;
  return_policy_url?: string | null;
  return_refund_policy?: IPolicyParagraph | IPolicyParagraph[];
  subscription_policy_url?: string | null;
  tos_url?: string | string[0] | null;
  custom_policy_urls?: Array<{ name: string; value: string }>;

  // Policy objects
  shipping_policy?: IPolicyParagraph | IPolicyParagraph[];
  tos?: IPolicyParagraph | IPolicyParagraph[];
  cancellation_policy?: IPolicyParagraph[];

  // Timeframes
  policy_timeframe_delivery_domestic_days?: number | null;
  policy_timeframe_delivery_international_days?: number | null;
  policy_timeframe_refund_days?: number | null;
  policy_timeframe_return_and_replacement_days?: number | null;

  // Images
  tos_image?: string | null;
  refund_image?: string | null;
  return_image?: string | null;
  checkout_image?: string | null;

  // Subscription
  subscription_renewal_reminder_days?: number | null;
  subscription_cancellation_methods?: string[];

  // Checkout Process
  checkout_elements_before_payment?: string[];
  checkout_elements_after_payment?: string[];
  order_protection_services?: string[];
  toc_consent_stages?: string[];

  // Verifications
  verification_signup_methods?: string[];
  verification_first_purchase_methods?: string[];
  verification_every_purchase_methods?: string[];

  // Automation
  auto_submit?: boolean;
}

export interface IAutoSubmit {
  auto_submit_dispute_initiation_date_delay_days?: number;
  auto_submit_dispute_due_date_delay_days?: number;
}

export interface IFile {
  file_key: string;
  file_name: string;
  file_purpose: string;
  date_added: Date;
}

export interface IFeatureRequest {
  requested_stripe: boolean;
  requested_braintree: boolean;
  requested_shopify_payments: boolean;
}

export interface ISettingsBase {
  date_created: Date;
  date_updated: Date | null;
  shop: IShopDetails;
  notifications: INotifications;
  fields: IFields;
  files: IFile | IFile[];
  feature_request: IFeatureRequest;
  auto_submit?: IAutoSubmit;
}

export interface ISettings extends ISettingsBase {
  _id?: string;
  chargeflow_id?: string;
  accountId?: string;
}

export interface IMongoSettings extends ISettingsBase, Document {
  _id?: ObjectId;
  chargeflow_id?: ObjectId;
  accountId?: ObjectId;
}
