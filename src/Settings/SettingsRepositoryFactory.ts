import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoSettings } from './ISettings';
import { ISettingsRepository } from './ISettingsRepository';
import { MongoSettingsRepository } from './MongoSettingsRepository';

export class SettingsRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): ISettingsRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoSettings>(mongoClient, dbName, collectionName);

        return new MongoSettingsRepository(
            findByQueryOperation,
        );
    }
}
