import { ClientSession, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoSettings, ISettings } from './ISettings';
import { ISettingsRepository } from './ISettingsRepository';
import { MongoSettingsMapper } from './MongoSettingsMapper';

export class MongoSettingsRepository implements ISettingsRepository {
    constructor(
    private readonly findByQueryOperation: IFindByQueryOperation<IMongoSettings>,
    ) {}

    async findShopSettingsByChargeflowId(
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<WithId<ISettings> | null> {
        const result = await this.findByQueryOperation.findOne(
            { chargeflow_id: new ObjectId(chargeflowId) },
            session,
        );

        return result ? MongoSettingsMapper.fromMongoSettings(result) : null;
    }
}
