import { ObjectId, WithId } from 'mongodb';
import { IMongoSettings, ISettings } from '../ISettings';

const defaultSettings = {
    date_created: new Date(),
    date_updated: new Date(),
    feature_request: {
        requested_braintree: false,
        requested_shopify_payments: false,
        requested_stripe: false,
    },
    fields: {
        support_email: '<EMAIL>',
    },
    files: [],
    notifications: {
        evidence_collection: false,
        lost_disputes: false,
        manual_review: false,
        monthly_summary: false,
        new_disputes: false,
        response_due: false,
        response_submissions: false,
        weekly_summary: false,
        won_disputes: false,
        additional_information: false,
    },
    shop: {
        address: 'Test address',
        contact_email: '<EMAIL>',
        currency: 'USD',
        full_name: 'Test full name',
        shop_name: 'Test shop name',
        shop_status: true,
    },
};

const getSettings = (overrides: Partial<ISettings> = {}): WithId<ISettings> => ({
    _id: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    chargeflow_id: new ObjectId().toHexString(),
    ...defaultSettings,
    ...overrides,
});

const getMongoSettings = (overrides: Partial<IMongoSettings> = {}): WithId<IMongoSettings> => ({
    _id: new ObjectId(),
    accountId: new ObjectId(),
    chargeflow_id: new ObjectId(),
    ...defaultSettings,
    ...overrides,
});

export default {
    getSettings,
    getMongoSettings,
};
