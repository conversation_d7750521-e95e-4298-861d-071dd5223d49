import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoSettings } from './ISettings';
import { MongoSettingsMapper } from './MongoSettingsMapper';
import { MongoSettingsRepository } from './MongoSettingsRepository';
import SettingsMocks from './__mocks__/Settings';

describe(MongoSettingsRepository.name, () => {
    let mongoSettingsRepository: MongoSettingsRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoSettings>>();

    beforeEach(() => {
        mongoSettingsRepository = new MongoSettingsRepository(
            findByQueryOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoSettingsRepository.prototype.findShopSettingsByChargeflowId.name, () => {
        it('returns shop settings object', async () => {
            expect.assertions(3);

            // GIVEN
            const chargeflowId = new ObjectId();
            const Settings = SettingsMocks.getSettings();
            const session = mock<ClientSession>();

            const findByOneResult = MongoSettingsMapper.toMongoSettings(Settings) as WithId<IMongoSettings>;

            findByQueryOperation.findOne.mockResolvedValue(findByOneResult);

            // WHEN
            const result = await mongoSettingsRepository.findShopSettingsByChargeflowId(chargeflowId.toHexString(), session);

            // THEN
            expect(result).toEqual(Settings);

            expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.findOne).toHaveBeenCalledWith({ 'chargeflow_id': chargeflowId }, session);
        });
    });
});
