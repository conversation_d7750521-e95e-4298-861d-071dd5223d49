
import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoSettings, ISettings } from './ISettings';

export class MongoSettingsMapper {
    static toMongoSettings(Settings: ISettings): IMongoSettings {
        if (!Settings) {
            throw new Error('Settings is required for mapping');
        }

        return {
            ...Settings,
            _id: Settings._id ? new ObjectId(Settings._id) : undefined,
            accountId: Settings.accountId ? new ObjectId(Settings.accountId) : undefined,
            chargeflow_id: Settings.chargeflow_id ? new ObjectId(Settings.chargeflow_id) : undefined,
        };
    }

    static fromMongoSettings(mongoSettings: MongoWithId<IMongoSettings>): WithId<ISettings> {
        if (!mongoSettings) {
            throw new Error('Settings not found');
        }

        return {
            ...mongoSettings,
            _id: mongoSettings._id.toHexString(),
            accountId: mongoSettings.accountId?.toHexString(),
            chargeflow_id: mongoSettings.chargeflow_id?.toHexString(),
        };
    }
}
