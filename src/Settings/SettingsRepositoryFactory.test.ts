import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { SettingsRepositoryFactory } from './SettingsRepositoryFactory';
import { MongoSettingsRepository } from './MongoSettingsRepository';

describe(SettingsRepositoryFactory.name, () => {
    describe(SettingsRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = SettingsRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoSettingsRepository);
        });
    });
});
