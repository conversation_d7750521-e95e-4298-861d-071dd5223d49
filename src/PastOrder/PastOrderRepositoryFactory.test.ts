import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { PastOrderRepositoryFactory } from './PastOrderRepositoryFactory';
import { MongoPastOrderRepository } from './MongoPastOrderRepository';

describe(PastOrderRepositoryFactory.name, () => {
    describe(PastOrderRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = PastOrderRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoPastOrderRepository);
        });
    });
});
