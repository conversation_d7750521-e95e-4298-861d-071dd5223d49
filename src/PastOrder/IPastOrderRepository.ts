import { ClientSession } from 'mongodb';
import { DeepPartial, WithId } from '../shared/helper-types';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IPastOrder } from './IPastOrder';

export interface IPastOrderRepository {
  findPastOrdersByAssociatedDispute: (
    disputeId: string,
    page?: number,
    pageSize?: number,
    session?: ClientSession
  ) => Promise<{
    items: WithId<IPastOrder>[];
    pagination?: IPaginationCalculatorResult;
  }>;

  upsertPastOrder: (
    pastOrder: DeepPartial<Omit<IPastOrder, 'associatedDisputes'>> &
      Pick<IPastOrder, 'customerId' | 'orderId' | 'source'>,
    associatedDisputeId: string,
    session?: ClientSession
  ) => Promise<void>;
}
