import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoPastOrder } from './IPastOrder';
import { IPastOrderRepository } from './IPastOrderRepository';
import { MongoPastOrderRepository } from './MongoPastOrderRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class PastOrderRepositoryFactory {
    static create(
        mongoClient: MongoClient,
        dbName: string,
        collectionName: string,
    ): IPastOrderRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoPastOrder>(
            mongoClient,
            dbName,
            collectionName,
        );
        const updateOperation = new MongoUpdateOperation<IMongoPastOrder>(
            mongoClient,
            dbName,
            collectionName,
        );

        return new MongoPastOrderRepository(findByQueryOperation, updateOperation);
    }
}
