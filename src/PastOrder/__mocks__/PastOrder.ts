import { ObjectId, WithId } from 'mongodb';
import { IPastOrder } from '../IPastOrder';

const getPastOrder = (
    overrides: Partial<IPastOrder> = {},
): WithId<IPastOrder> => ({
    _id: new ObjectId().toHexString(),
    amount: '100',
    associatedDisputes: [ new ObjectId().toHexString() ],
    billingAddress: {
        address1: 'Test address1',
        address2: 'Test address2',
        city: 'Test city',
        country: 'Test country',
        name: 'Test name',
        phone: 'Test phone',
        state: 'Test state',
        latitude: 0,
        longitude: 0,
        zipCode: '12345',
    },
    currency: 'USD',
    customer: {
        firstName: 'Test first name',
        lastName: 'Test last name',
        name: 'Test name',
        orderEmail: '<EMAIL>',
    },
    customerId: 1,
    dateCreated: new Date(),
    dispute: {
        id: 'Test id',
        reason: 'Test reason',
    },
    financialStatus: 'Test financial status',
    fingerprint: 'Test fingerprint',
    fulfillmentStatus: 'Test fulfillment status',
    ipAddress: 'Test ip address',
    orderId: 1,
    rawData: {
        order: {},
        transaction: {},
    },
    shippingAddress: {
        address1: 'Test address1',
        address2: 'Test address2',
        city: 'Test city',
        country: 'Test country',
        name: 'Test name',
        phone: 'Test phone',
        state: 'Test state',
        latitude: 0,
        longitude: 0,
        zipCode: '12345',
    },
    source: 'Test source',
    transaction: {
        dateCreated: new Date().toISOString(),
        declines: [],
        id: 1,
        paymentDetails: {
            attempts: {
                count: 1,
                sameCard: true,
            },
            bin: null,
            cardBrand: null,
            cardholderName: null,
            country: null,
            expMonth: null,
            expYear: null,
            issuer: null,
            last4: null,
            network: null,
            '3dSecure': {
                authenticationFlow: null,
                authenticated: null,
                result: null,
                resultReason: null,
                version: null,
            },
        },
        authorization: null,
        checks: {
            addressLine1Check: null,
            addressZipCheck: null,
            cvcCheck: {
                cvcCode: null,
                description: null,
            },
            avsCheck: {
                Code: null,
                description: null,
            },
        },
    },
    ...overrides,
});

export default {
    getPastOrder,
};
