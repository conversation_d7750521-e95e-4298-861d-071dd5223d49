import { ClientSession, ObjectId } from 'mongodb';
import { WithId, DeepPartial } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoPastOrder, IPastOrder } from './IPastOrder';
import { IPastOrderRepository } from './IPastOrderRepository';
import { MongoPastOrderMapper } from './MongoPastOrderMapper';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { flatten } from '../shared/helper-functions';

const pastOrderSkeleton: Omit<IPastOrder, 'customerId' | 'orderId' | 'source'> =
  {
      amount: null,
      billingAddress: {
          name: null,
          country: null,
          state: null,
          city: null,
          address1: null,
          address2: null,
          zipCode: null,
          phone: null,
          latitude: null,
          longitude: null,
      },
      currency: null,
      customer: {
          name: null,
          firstName: null,
          lastName: null,
          orderEmail: null,
      },
      dateCreated: null,
      dispute: {
          id: null,
          reason: null,
      },
      financialStatus: null,
      fingerprint: null,
      fulfillmentStatus: null,
      ipAddress: null,
      rawData: {
          order: {},
          transaction: {},
      },
      shippingAddress: {
          name: null,
          country: null,
          state: null,
          city: null,
          address1: null,
          address2: null,
          zipCode: null,
          phone: null,
          latitude: null,
          longitude: null,
      },
      transaction: {
          id: null,
          authorization: null,
          dateCreated: null,
          declines: null,
          checks: {
              addressLine1Check: null,
              addressZipCheck: null,
              cvcCheck: {
                  cvcCode: null,
                  description: null,
              },
              avsCheck: {
                  Code: null,
                  description: null,
              },
          },
          paymentDetails: {
              cardholderName: null,
              cardBrand: null,
              country: null,
              expMonth: null,
              expYear: null,
              bin: null,
              network: null,
              last4: null,
              issuer: null,
              '3dSecure': {
                  authenticationFlow: null,
                  authenticated: null,
                  result: null,
                  resultReason: null,
                  version: null,
              },
              attempts: {
                  count: null,
                  sameCard: null,
              },
          },
      },
  };

const flatPastOrderSkeleton: Record<string, unknown> =
  flatten(pastOrderSkeleton);

export class MongoPastOrderRepository implements IPastOrderRepository {
    constructor(
    private readonly findByQueryOperation: IFindByQueryOperation<IMongoPastOrder>,
    private readonly updateOperation: IUpdateOperation<IMongoPastOrder>,
    ) {}

    async findPastOrdersByAssociatedDispute(
        disputeId: string,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
    ): Promise<{
    items: WithId<IPastOrder>[];
    pagination?: IPaginationCalculatorResult;
  }> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        if (!page) {
            page = 1;
        }
        if (!pageSize) {
            pageSize = 10;
        }

        const result = await this.findByQueryOperation.find(
            { associatedDisputes: { $in: [ new ObjectId(disputeId) ] } },
            page,
            pageSize,
            session,
            undefined,
            { dateCreated: -1 },
        );

        return {
            items: MongoPastOrderMapper.fromMongoPastOrders(result.items),
            pagination: result.pagination,
        };
    }

    async upsertPastOrder(
        pastOrder: DeepPartial<
      Omit<IPastOrder, 'associatedDisputes' | 'dateCreated' | 'dateUpdated'>
    > &
      Pick<IPastOrder, 'customerId' | 'orderId' | 'source'>,
        associatedDisputeId: string,
        session?: ClientSession,
    ): Promise<void> {
        const flatPastOrder: Record<string, unknown> = flatten(pastOrder);

        const modifiedFieldsEntries = Object.entries(flatPastOrder).filter(
            ([ , value ]) => {
                return value !== undefined;
            },
        ).filter(([ , value ]) => {
            return value !== null && typeof value === 'object' ? Object.keys(value as Record<string, unknown>).length > 0 : true;
        });

        const modifiedFieldsObject = Object.fromEntries(modifiedFieldsEntries);
        const modifiedFieldKeys = Object.keys(modifiedFieldsObject);
        const unusedEntries = Object.entries(flatPastOrderSkeleton).filter(
            ([ key ]) => {
                return !modifiedFieldsObject[key];
            },
        ).filter(([ key ]) => {
            return !modifiedFieldKeys.some(modifiedFieldKey => {
                return modifiedFieldKey.startsWith(key);
            });
        });
        const unusedEntriesObject = Object.fromEntries(unusedEntries);

        await this.updateOperation.upsert(
            {
                customerId: pastOrder.customerId,
                orderId: pastOrder.orderId,
            },
            {
                $setOnInsert: { ...unusedEntriesObject, dateCreated: new Date() },
                $set: { ...modifiedFieldsObject, dateUpdated: new Date() },
                $addToSet: {
                    associatedDisputes: new ObjectId(associatedDisputeId),
                },
            },
            session,
        );
    }
}
