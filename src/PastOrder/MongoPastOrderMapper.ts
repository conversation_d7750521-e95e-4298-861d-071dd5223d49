
import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoPastOrder, IPastOrder } from './IPastOrder';

export class MongoPastOrderMapper {
    static toMongoPastOrder(PastOrder: IPastOrder): IMongoPastOrder {
        if (!PastOrder) {
            throw new Error('PastOrder is required for mapping');
        }

        return {
            ...PastOrder,
            _id: PastOrder._id ? new ObjectId(PastOrder._id) : undefined,
            associatedDisputes: PastOrder.associatedDisputes?.map(dispute => new ObjectId(dispute)),
        };
    }

    static fromMongoPastOrder(mongoPastOrder: MongoWithId<IMongoPastOrder>): WithId<IPastOrder> {
        if (!mongoPastOrder) {
            throw new Error('PastOrder not found');
        }

        return {
            ...mongoPastOrder,
            _id: mongoPastOrder._id.toHexString(),
            associatedDisputes: mongoPastOrder.associatedDisputes?.map(dispute => dispute.toHexString()),
        };
    }

    static fromMongoPastOrders(pastOrders: MongoWithId<IMongoPastOrder>[]): WithId<IPastOrder>[] {
        return pastOrders.map(MongoPastOrderMapper.fromMongoPastOrder);
    }
}
