import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { IMongoPastOrder } from './IPastOrder';
import { MongoPastOrderMapper } from './MongoPastOrderMapper';
import { MongoPastOrderRepository } from './MongoPastOrderRepository';
import PastOrderMocks from './__mocks__/PastOrder';

const MOCK_SYSTEM_TIME = '2024-04-12T00:00:00Z';

describe(MongoPastOrderRepository.name, () => {
    let mongoPastOrderRepository: MongoPastOrderRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoPastOrder>>();
    const updateOperation = mock<IUpdateOperation<IMongoPastOrder>>();

    beforeEach(() => {
        mongoPastOrderRepository = new MongoPastOrderRepository(findByQueryOperation, updateOperation);

        jest.useFakeTimers().setSystemTime(new Date(MOCK_SYSTEM_TIME));
    });

    afterEach(jest.clearAllMocks);

    describe(MongoPastOrderRepository.prototype.findPastOrdersByAssociatedDispute.name, () => {
        it('returns PastOrder settings object', async () => {
            expect.assertions(3);

            // GIVEN
            const disputeId = new ObjectId();
            const pastOrder = PastOrderMocks.getPastOrder();
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ pastOrder ],
                pagination: paginationMocks.getPaginationResult(),
            };

            const findByQueryResult = {
                items: [ MongoPastOrderMapper.toMongoPastOrder(pastOrder) as WithId<IMongoPastOrder> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoPastOrderRepository.findPastOrdersByAssociatedDispute(disputeId.toHexString(), 1, 1, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith(
                {
                    associatedDisputes: { $in: [ new ObjectId(disputeId) ] },
                },
                1,
                1,
                session,
                undefined,
                { dateCreated: -1 },
            );
        });
    });

    describe(MongoPastOrderRepository.prototype.upsertPastOrder.name, () => {
        it('should upsert past order with associated dispute', async () => {
            expect.assertions(2);

            // GIVEN
            const pastOrder = PastOrderMocks.getPastOrder();
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();

            // WHEN
            await mongoPastOrderRepository.upsertPastOrder(pastOrder, disputeId, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledTimes(1);
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    customerId: pastOrder.customerId,
                    orderId: pastOrder.orderId,
                },
                expect.objectContaining({
                    $setOnInsert: expect.any(Object),
                    $set: expect.any(Object),
                    $addToSet: {
                        associatedDisputes: new ObjectId(disputeId),
                    },
                }),
                session,
            );
        });

        it('should handle partial past order data', async () => {
            expect.assertions(2);

            // GIVEN
            const partialPastOrder = {
                customerId: 123,
                orderId: 456,
                source: 'shopify',
                amount: '100',
                currency: 'USD',
            };
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();

            // WHEN
            await mongoPastOrderRepository.upsertPastOrder(partialPastOrder, disputeId, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledTimes(1);
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    customerId: partialPastOrder.customerId,
                    orderId: partialPastOrder.orderId,
                },
                expect.objectContaining({
                    $setOnInsert: expect.any(Object),
                    $set: expect.any(Object),
                    $addToSet: {
                        associatedDisputes: new ObjectId(disputeId),
                    },
                }),
                session,
            );
        });

        it('should correctly split fields between $set and $setOnInsert, ignoring empty object values', async () => {
            expect.assertions(1);

            // GIVEN
            const pastOrder = {
                customerId: 123,
                orderId: 456,
                source: 'shopify',
                amount: '100',
                currency: 'USD',
                billingAddress: {},
                customer: {
                    name: 'John Doe',
                    firstName: 'John',
                    lastName: 'Doe',
                },
                transaction: {
                    id: 123,
                    paymentDetails: {
                        cardBrand: 'visa',
                        last4: '4242',
                    },
                },
            };
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();

            // WHEN
            await mongoPastOrderRepository.upsertPastOrder(pastOrder, disputeId, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    customerId: pastOrder.customerId,
                    orderId: pastOrder.orderId,
                },
                expect.objectContaining({
                    $addToSet: { associatedDisputes: new ObjectId(disputeId) },
                    $set: {
                        dateUpdated: new Date(MOCK_SYSTEM_TIME),
                        amount: '100',
                        currency: 'USD',
                        'customer.firstName': 'John',
                        'customer.lastName': 'Doe',
                        'customer.name': 'John Doe',
                        customerId: 123,
                        orderId: 456,
                        source: 'shopify',
                        'transaction.id': 123,
                        'transaction.paymentDetails.cardBrand': 'visa',
                        'transaction.paymentDetails.last4': '4242',
                    },
                    $setOnInsert: {
                        'billingAddress.address1': null,
                        'billingAddress.address2': null,
                        'billingAddress.city': null,
                        'billingAddress.country': null,
                        'billingAddress.latitude': null,
                        'billingAddress.longitude': null,
                        'billingAddress.name': null,
                        'billingAddress.phone': null,
                        'billingAddress.state': null,
                        'billingAddress.zipCode': null,
                        'customer.orderEmail': null,
                        dateCreated: new Date(MOCK_SYSTEM_TIME),
                        'dispute.id': null,
                        'dispute.reason': null,
                        financialStatus: null,
                        fingerprint: null,
                        fulfillmentStatus: null,
                        ipAddress: null,
                        'rawData.order': {},
                        'rawData.transaction': {},
                        'shippingAddress.address1': null,
                        'shippingAddress.address2': null,
                        'shippingAddress.city': null,
                        'shippingAddress.country': null,
                        'shippingAddress.latitude': null,
                        'shippingAddress.longitude': null,
                        'shippingAddress.name': null,
                        'shippingAddress.phone': null,
                        'shippingAddress.state': null,
                        'shippingAddress.zipCode': null,
                        'transaction.authorization': null,
                        'transaction.checks.addressLine1Check': null,
                        'transaction.checks.addressZipCheck': null,
                        'transaction.checks.avsCheck.Code': null,
                        'transaction.checks.avsCheck.description': null,
                        'transaction.checks.cvcCheck.cvcCode': null,
                        'transaction.checks.cvcCheck.description': null,
                        'transaction.dateCreated': null,
                        'transaction.declines': null,
                        'transaction.paymentDetails.3dSecure.authenticated': null,
                        'transaction.paymentDetails.3dSecure.authenticationFlow': null,
                        'transaction.paymentDetails.3dSecure.result': null,
                        'transaction.paymentDetails.3dSecure.resultReason': null,
                        'transaction.paymentDetails.3dSecure.version': null,
                        'transaction.paymentDetails.attempts.count': null,
                        'transaction.paymentDetails.attempts.sameCard': null,
                        'transaction.paymentDetails.bin': null,
                        'transaction.paymentDetails.cardholderName': null,
                        'transaction.paymentDetails.country': null,
                        'transaction.paymentDetails.expMonth': null,
                        'transaction.paymentDetails.expYear': null,
                        'transaction.paymentDetails.issuer': null,
                        'transaction.paymentDetails.network': null,
                    },
                }),
                session,
            );
        });

        it('should correctly split fields between $set and $setOnInsert, removing rawData setOnInsert if included in object to store', async () => {
            expect.assertions(1);

            // GIVEN
            const pastOrder = {
                customerId: 123,
                orderId: 456,
                source: 'shopify',
                rawData: {
                    order: {
                        orderId: 1234,
                        customerId: 1234,
                    },
                    transaction: {
                        id: 1,
                    },
                },
            };
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();

            // WHEN
            await mongoPastOrderRepository.upsertPastOrder(pastOrder, disputeId, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    customerId: pastOrder.customerId,
                    orderId: pastOrder.orderId,
                },
                expect.objectContaining({
                    $addToSet: { associatedDisputes: new ObjectId(disputeId) },
                    $set: {
                        dateUpdated: new Date(MOCK_SYSTEM_TIME),
                        customerId: 123,
                        orderId: 456,
                        source: 'shopify',
                        'rawData.order.orderId': 1234,
                        'rawData.order.customerId': 1234,
                        'rawData.transaction.id': 1,
                    },
                    $setOnInsert: {
                        amount: null,
                        currency: null,
                        'customer.firstName': null,
                        'customer.lastName': null,
                        'customer.name': null,
                        'transaction.id': null,
                        'transaction.paymentDetails.cardBrand': null,
                        'transaction.paymentDetails.last4': null,
                        'billingAddress.address1': null,
                        'billingAddress.address2': null,
                        'billingAddress.city': null,
                        'billingAddress.country': null,
                        'billingAddress.latitude': null,
                        'billingAddress.longitude': null,
                        'billingAddress.name': null,
                        'billingAddress.phone': null,
                        'billingAddress.state': null,
                        'billingAddress.zipCode': null,
                        'customer.orderEmail': null,
                        dateCreated: new Date(MOCK_SYSTEM_TIME),
                        'dispute.id': null,
                        'dispute.reason': null,
                        financialStatus: null,
                        fingerprint: null,
                        fulfillmentStatus: null,
                        ipAddress: null,
                        'shippingAddress.address1': null,
                        'shippingAddress.address2': null,
                        'shippingAddress.city': null,
                        'shippingAddress.country': null,
                        'shippingAddress.latitude': null,
                        'shippingAddress.longitude': null,
                        'shippingAddress.name': null,
                        'shippingAddress.phone': null,
                        'shippingAddress.state': null,
                        'shippingAddress.zipCode': null,
                        'transaction.authorization': null,
                        'transaction.checks.addressLine1Check': null,
                        'transaction.checks.addressZipCheck': null,
                        'transaction.checks.avsCheck.Code': null,
                        'transaction.checks.avsCheck.description': null,
                        'transaction.checks.cvcCheck.cvcCode': null,
                        'transaction.checks.cvcCheck.description': null,
                        'transaction.dateCreated': null,
                        'transaction.declines': null,
                        'transaction.paymentDetails.3dSecure.authenticated': null,
                        'transaction.paymentDetails.3dSecure.authenticationFlow': null,
                        'transaction.paymentDetails.3dSecure.result': null,
                        'transaction.paymentDetails.3dSecure.resultReason': null,
                        'transaction.paymentDetails.3dSecure.version': null,
                        'transaction.paymentDetails.attempts.count': null,
                        'transaction.paymentDetails.attempts.sameCard': null,
                        'transaction.paymentDetails.bin': null,
                        'transaction.paymentDetails.cardholderName': null,
                        'transaction.paymentDetails.country': null,
                        'transaction.paymentDetails.expMonth': null,
                        'transaction.paymentDetails.expYear': null,
                        'transaction.paymentDetails.issuer': null,
                        'transaction.paymentDetails.network': null,
                    },
                }),
                session,
            );
        });
    });
});
