import { ObjectId } from 'mongodb';

export interface IAddress {
  name: string | null;
  country: string | null;
  state: string | null;
  city: string | null;
  address1: string | null;
  address2: string | null;
  zipCode: string | null;
  phone: string | null;
  latitude: number | null;
  longitude: number | null;
}

export interface ICustomer {
  name: string | null;
  firstName: string | null;
  lastName: string | null;
  orderEmail: string | null;
}

export interface IDispute {
  id: string | null;
  reason: string | null;
  _id?: ObjectId | null;
}

export interface IChecks {
  addressLine1Check: string | null;
  addressZipCheck: string | null;
  cvcCheck: {
    cvcCode: string | null;
    description: string | null;
  };
  avsCheck: {
    Code: string | null;
    description: string | null;
  };
}

export interface IPaymentDetails {
  cardholderName: string | null;
  cardBrand: string | null;
  country: string | null;
  expMonth: number | null;
  expYear: number | null;
  bin: string | null;
  network: string | null;
  last4: string | null;
  issuer: string | null;
  '3dSecure': {
    authenticationFlow: string | null;
    authenticated: boolean | null;
    result?: string | null;
    resultReason: string | null;
    version: string | null;
  };
  attempts: {
    count: number | null;
    sameCard: boolean | null;
  };
}

export interface ITransaction {
  id: number | null;
  authorization: string | null;
  dateCreated: string | null;
  declines?: unknown[] | null;
  checks: IChecks;
  paymentDetails: IPaymentDetails;
}

export interface IPastOrderBase {
  customerId: number;
  orderId: number;
  amount: string | null;
  billingAddress: IAddress;
  currency: string | null;
  customer: ICustomer;
  dateCreated: Date | null;
  dispute: IDispute;
  financialStatus: string | null;
  fingerprint: string | null;
  fulfillmentStatus: string | null;
  ipAddress: string | null;
  rawData: {
    order: Record<string, unknown> | null;
    transaction: Record<string, unknown> | null;
  };
  shippingAddress: IAddress;
  source: string;
  transaction: ITransaction;
}

export interface IPastOrder extends IPastOrderBase {
  _id?: string;
  associatedDisputes?: string[];
}

export interface IMongoPastOrder extends IPastOrderBase {
  _id?: ObjectId;
  associatedDisputes?: ObjectId[];
}
