import { MongoProcessorRepository } from './MongoProcessorRepository';
import { MongoClient } from 'mongodb';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';
import { IMongoProcessor } from './Types';
import { IProcessorRepository } from './IProcessorRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';

export class ProcessorRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IProcessorRepository {
        const addOperation = new MongoAddOperation<IMongoProcessor>(mongoClient, dbName, collectionName);
        const getByIdOperation = new MongoGetByIdOperation<IMongoProcessor>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoProcessor>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoProcessor>(mongoClient, dbName, collectionName);

        return new MongoProcessorRepository(
            getByIdOperation,
            findByQueryOperation,
            updateOperation,
            addOperation,
        );
    }
}
