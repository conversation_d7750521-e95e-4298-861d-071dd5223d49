import { ObjectId } from 'mongodb';
import {
    IAdyenConnectData,
    IAfterpayConnectData,
    IConnectData,
    ICreateProcessor,
    IManagedIntegrationData,
    IPayPalConnectData,
    IProcessorConcrete,
    IProcessorStatus,
    ProcessorConcrete,
} from '../Types';
import { Source } from '../../shared/enums';
import { faker } from '@faker-js/faker';

const getDefaultProcessor = (overrides: Partial<IProcessorConcrete<IConnectData>> = {}): IProcessorConcrete<IConnectData> => {
    const processor = {
        _id: new ObjectId().toHexString(),
        processor_name: Source.Stripe,
        chargeflow_id: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        connect: {},
        status: {
            status: 'Active' as IProcessorStatus['status'],
            dateCreated: new Date(),
            actor: faker.internet.email(),
            reason: faker.lorem.sentence(),
        },
        statusHistory: null,
        isKeyValid: true,
        date_created: new Date(),
        date_updated: null,
        ...overrides,
    };
    return new ProcessorConcrete(
        processor.chargeflow_id,
        processor.accountId,
        processor.processor_name,
        processor.connect,
        processor.status,
        processor.statusHistory,
        processor.isKeyValid,
        processor._id,
        processor.date_created,
        processor.date_updated,
        processor.isManaged,
        processor.managedConnection,
        processor.hasSubscription,
        processor.lastSyncedAt);
};

const getAdyenProcessor = (overrides: Partial<IProcessorConcrete<IAdyenConnectData>> = {}): IProcessorConcrete<IAdyenConnectData> => {
    const processor = {
        _id: new ObjectId().toHexString(),
        processor_name: Source.Adyen,
        chargeflow_id: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        connect: {
            hmac: new ObjectId().toHexString(),
            apiKey: new ObjectId().toHexString(),
        },
        status: {
            status: 'Active' as IProcessorStatus['status'],
            dateCreated: new Date(),
            actor: faker.internet.email(),
            reason: faker.lorem.sentence(),
        },
        statusHistory: null,
        isKeyValid: true,
        date_created: new Date(),
        date_updated: null,
        ...overrides,
    };
    return new ProcessorConcrete(
        processor.chargeflow_id,
        processor.accountId,
        processor.processor_name,
        processor.connect,
        processor.status,
        processor.statusHistory,
        processor.isKeyValid,
        processor._id,
        processor.date_created,
        processor.date_updated,
        processor.isManaged,
        processor.managedConnection,
        processor.hasSubscription,
        processor.lastSyncedAt);
};

const getAfterpayProcessor = (overrides: Partial<IProcessorConcrete<IAfterpayConnectData>> = {}): IProcessorConcrete<IAfterpayConnectData> => {
    const processor = {
        _id: new ObjectId().toHexString(),
        processor_name: Source.AfterpayOrClearpay,
        chargeflow_id: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        connect: {
            username: faker.internet.userName(),
            password: faker.internet.password(),
        },
        status: null,
        statusHistory: null,
        isKeyValid: true,
        date_created: new Date(),
        date_updated: null,
        ...overrides,
    };
    return new ProcessorConcrete(
        processor.chargeflow_id,
        processor.accountId,
        processor.processor_name,
        processor.connect,
        processor.status,
        processor.statusHistory,
        processor.isKeyValid,
        processor._id,
        processor.date_created,
        processor.date_updated,
        processor.isManaged,
        processor.managedConnection,
        processor.hasSubscription,
        processor.lastSyncedAt);
};

const getPayPalProcessor = (overrides: Partial<IProcessorConcrete<IPayPalConnectData>> = {}): IProcessorConcrete<IPayPalConnectData> => {
    const processor = {
        _id: new ObjectId().toHexString(),
        processor_name: Source.PayPal,
        chargeflow_id: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        connect: {
            scope: faker.internet.url(),
            access_token: faker.string.sample(30),
            token_type: 'Bearer',
            expires_in: faker.number.int(),
            refresh_token: faker.string.sample(30),
            nonce: faker.string.sample(30),
        },
        status: null,
        statusHistory: null,
        isKeyValid: true,
        date_created: new Date(),
        date_updated: null,
        ...overrides,
    };
    return new ProcessorConcrete(
        processor.chargeflow_id,
        processor.accountId,
        processor.processor_name,
        processor.connect,
        processor.status,
        processor.statusHistory,
        processor.isKeyValid,
        processor._id,
        processor.date_created,
        processor.date_updated,
        processor.isManaged,
        processor.managedConnection,
        processor.hasSubscription,
        processor.lastSyncedAt);
};

const getManagedProcessor = (overrides: Partial<IProcessorConcrete<IManagedIntegrationData>> = {}): IProcessorConcrete<IManagedIntegrationData> => {
    const processor = {
        _id: new ObjectId().toHexString(),
        processor_name: Source.Affirm,
        chargeflow_id: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        connect: {
            username: faker.internet.userName(),
            password: faker.internet.password(),
        },
        status: null,
        statusHistory: null,
        isKeyValid: true,
        date_created: new Date(),
        date_updated: null,
        ...overrides,
    };
    return new ProcessorConcrete(
        processor.chargeflow_id,
        processor.accountId,
        processor.processor_name,
        processor.connect,
        processor.status,
        processor.statusHistory,
        processor.isKeyValid,
        processor._id,
        processor.date_created,
        processor.date_updated,
        processor.isManaged,
        processor.managedConnection,
        processor.hasSubscription,
        processor.lastSyncedAt,
    );
};

const concretToCreateProcessor = <T extends IConnectData>(input: IProcessorConcrete<T>): ICreateProcessor<T> => {
    const newRequest: ICreateProcessor<T> = {
        chargeflow_id: input.chargeflow_id,
        accountId: input.accountId,
        processor_name: input.processor_name,
        connect: input.connect,
        isKeyValid: input.isKeyValid,
        createdByEmail: input.status?.actor || '',
        isManaged: input.isManaged,
        managedConnection: input.managedConnection,
    };
    return newRequest;
};

export default {
    getAdyenProcessor,
    getAfterpayProcessor,
    getPayPalProcessor,
    getManagedProcessor,
    concretToCreateProcessor,
    getDefaultProcessor,
};
