import { ObjectId } from 'mongodb';
import { Source } from '../shared/enums';
import { z } from 'zod';
import {
    iAdyenConnectDataSchema,
    iAfterpayConnectDataSchema,
    iBraintreeConnectDataSchema,
    iGmailConnectDataSchema,
    iKlarnaConnectDataSchema,
    iManagedIntegrationDataSchema,
    iPayPalConnectDataSchema,
    iRechargeConnectDataSchema,
    iShopifyPaymentsConnectDataSchema,
    iStripeConnectDataSchema,
    iWooPaymentsConnectDataSchema,
} from './Schemas';

export const processorARNsSyncStatus = z.enum([ 'not_synced', 'synced', 'failed' ]);
export type ProcessorARNsSyncStatus = z.infer<typeof processorARNsSyncStatus>;

export interface IProcessorBase {
    processor_name: Source;
    connect: IConnectData;
    status: IProcessorStatus | null;
    statusHistory: IProcessorStatus[] | null;
    isKeyValid: boolean;
    date_created: Date;
    date_updated: Date | null;
    isManaged?: boolean;
    managedConnection?: IManagedConnectionInfo;
    hasSubscription?: boolean;
    lastSyncedAt?: Date | null;
    disabledProducts?: ChargeflowProductsEnum[];
    arnsSyncStatus?: ProcessorARNsSyncStatus;
    arnsSyncFailureReason?: string | null;
}

export class Processor implements IProcessor {
    public _id: string;
    public chargeflow_id: string;
    public accountId: string;
    public processor_name: Source;
    public connect: IConnectData;
    public status: IProcessorStatus | null;
    public statusHistory: IProcessorStatus[] | null;
    public isKeyValid: boolean;
    public date_created: Date;
    public date_updated: Date | null;
    public isManaged?: boolean;
    public managedConnection?: IManagedConnectionInfo;
    public hasSubscription?: boolean;
    public lastSyncedAt?: Date | null;
    public disabledProducts?: ChargeflowProductsEnum[] = [];
    public arnsSyncStatus?: ProcessorARNsSyncStatus;
    public arnsSyncFailureReason?: string | null;

    constructor(
        chargeflow_id: string,
        accountId: string,
        processor_name: Source,
        connect: IConnectData,
        status: IProcessorStatus | null,
        statusHistory: IProcessorStatus[] | null,
        isKeyValid: boolean,
        _id: string,
        dateCreated: Date,
        dateUpdated: Date | null,
        isManaged?: boolean,
        managedConnection?: IManagedConnectionInfo,
        hasSubscription?: boolean,
        lastSyncedAt?: Date | null,
        arnsSyncStatus?: ProcessorARNsSyncStatus,
        arnsSyncFailureReason?: string | null,
    ) {
        this._id = _id;
        this.chargeflow_id = chargeflow_id;
        this.accountId = accountId;
        this.processor_name = processor_name;
        this.connect = connect;
        this.status = status;
        this.statusHistory = statusHistory;
        this.isKeyValid = isKeyValid;
        this.date_created = dateCreated;
        this.date_updated = dateUpdated;
        this.isManaged = isManaged;
        this.managedConnection = managedConnection;
        this.hasSubscription = hasSubscription;
        this.lastSyncedAt = lastSyncedAt ?? null;
        this.arnsSyncStatus = arnsSyncStatus ?? 'not_synced';
        this.arnsSyncFailureReason = arnsSyncFailureReason ?? null;
    }

    public isActive(): boolean {
        return !this.status || this.status?.status === processorStatusEnumSchema.enum.Active;
    }

    /**
     * Returns false if at least one of the provided product is in
     * this processor's `disabledProducts` (optional) array prop
     * Logs which products are found disabled
     * @param chargeflowProduct ChargeflowProductsEnum | ChargeflowProductsEnum[]
     * @returns boolean
     */
    public isProductEnabled(chargeflowProduct: ChargeflowProductsEnum | ChargeflowProductsEnum[]): boolean {
        if (this.disabledProducts && this.disabledProducts?.length) {
            const products = Array.isArray(chargeflowProduct) ? chargeflowProduct : [ chargeflowProduct ];

            const disableds = products.filter(product => this.disabledProducts!.includes(product));
            console.log('Disabled products: ', disableds);

            return Boolean(disableds.length);
        }

        return true;
    }

    public isDeactivated(): boolean {
        if (!this.status) {
            return false;
        }
        return this.status.status === processorStatusEnumSchema.enum.Deactivated;
    }
}

export class ProcessorConcrete<T extends IConnectData> extends Processor implements IProcessorConcrete<T> {
    public connect: T;

    constructor(
        chargeflow_id: string,
        accountId: string,
        processor_name: Source,
        connect: T,
        status: IProcessorStatus | null,
        statusHistory: IProcessorStatus[] | null,
        isKeyValid: boolean,
        _id: string,
        dateCreated: Date,
        dateUpdated: Date | null,
        isManaged?: boolean,
        managedConnection?: IManagedConnectionInfo,
        hasSubscription?: boolean,
        lastSyncedAt?: Date | null,
        arnsSyncStatus?: ProcessorARNsSyncStatus,
        arnsSyncFailureReason?: string | null,
    ) {
        super(
            chargeflow_id,
            accountId,
            processor_name,
            connect, status,
            statusHistory,
            isKeyValid,
            _id,
            dateCreated,
            dateUpdated,
            isManaged,
            managedConnection,
            hasSubscription,
            lastSyncedAt,
            arnsSyncStatus,
            arnsSyncFailureReason);
        this.connect = connect;
    }
}

export interface IProcessor extends IProcessorBase {
    _id: string;
    chargeflow_id: string;
    accountId: string;
    isActive(): boolean;
    isDeactivated(): boolean;
    isProductEnabled(product: ChargeflowProductsEnum | ChargeflowProductsEnum[]): boolean;
}

export interface ICreateProcessor<T extends IConnectData>
    extends Omit<IProcessorConcrete<IConnectData>, '_id' | 'date_created' | 'status' | 'date_updated' | 'isActive' | 'isProductEnabled' | 'isDeactivated' | 'statusHistory'> {
    status?: IProcessorStatusEnum;
    connect: T;
    createdByEmail: string;
}

export interface IProcessorConcrete<T extends IConnectData> extends IProcessor {
    connect: T;
}

export interface IMongoProcessor extends IProcessorBase {
    _id?: ObjectId;
    chargeflow_id: ObjectId;
    accountId: ObjectId;
}

export const processorStatusEnumSchema = z.enum([ 'Active', 'Deactivating', 'Reactivating', 'Deactivated', 'Invalid' ]);

export const processorStatusSchema = z.object({
    dateCreated: z.date().optional(),
    dateUpdated: z.date().optional(),
    actor: z.string(),
    status: processorStatusEnumSchema,
    reason: z.string().optional(),
});

export const chargeflowProductsEnumSchema = z.enum([ 'disputes-ingestion', 'disputes-management', 'disputes-order-linking' ]);

export const ProcessorStatusEnum = processorStatusEnumSchema.enum;

export type ChargeflowProductsEnum = z.infer<typeof chargeflowProductsEnumSchema>;

export type IProcessorStatus = z.infer<typeof processorStatusSchema>;
export type IProcessorStatusEnum = z.infer<typeof processorStatusEnumSchema>;

export type IAdyenConnectData = z.infer<typeof iAdyenConnectDataSchema>;

export type IStripeConnectData = z.infer<typeof iStripeConnectDataSchema>;

export type IAfterpayConnectData = z.infer<typeof iAfterpayConnectDataSchema>;

export type IPayPalConnectData = z.infer<typeof iPayPalConnectDataSchema>;

export type IKlarnaConnectData = z.infer<typeof iKlarnaConnectDataSchema>;

export type IShopifyPaymentsConnectData = z.infer<typeof iShopifyPaymentsConnectDataSchema>;

export type IWooPaymentsConnectData = z.infer<typeof iWooPaymentsConnectDataSchema>;

export type IGmailConnectData = z.infer<typeof iGmailConnectDataSchema>;

export type IBraintreeConnectData = z.infer<typeof iBraintreeConnectDataSchema>;

export type IRechargeConnectData = z.infer<typeof iRechargeConnectDataSchema>;

export type IConnectData =
    | IAdyenConnectData
    | IAfterpayConnectData
    | IPayPalConnectData
    | IStripeConnectData
    | IKlarnaConnectData
    | IShopifyPaymentsConnectData
    | IManagedIntegrationData
    | IGmailConnectData
    | IBraintreeConnectData
    | IRechargeConnectData
    | IWooPaymentsConnectData;

export type IManagedIntegrationData = z.infer<typeof iManagedIntegrationDataSchema>;

export type IManagedIntegrationAccessType = 'credentials' | 'invited';

export type IManagedIntegrationStatus = 'pending' | 'connected' | 'inactive' | 'action-required';

export type IManagedIntActionRequiredType = 'missing-permissions' | 'invalid-invitation';

export interface IManagedConnectionInfo {
    status: IManagedIntegrationStatus;
    managedType: IManagedIntegrationAccessType;
    actionRequiredType?: IManagedIntActionRequiredType;
    invitedEmail?: string;
}

export interface IManagedIntegrationFilter {
    chargeflowId?: string;
    accountId?: string;
    processorName?: string;
    status?: IManagedIntegrationStatus;
    type?: IManagedIntegrationAccessType;
}

export interface IGetManyByARNsLastSyncedAtPayload {
    from: Date;
    to: Date;
    limit: number;
}
