import { faker } from '@faker-js/faker';
import { IProcessorStatus, IProcessorStatusEnum, processorStatusEnumSchema } from './Types';
import processorMocks from './__mocks__/Processors';

describe('IProcessorBase', () => {
    describe('IProcessorBase.isProductEnabled', () => {
        it('should return true when disabledProducts is undefined', () => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({ disabledProducts: undefined });

            // WHEN
            const result = processor.isProductEnabled('disputes-ingestion');

            // THEN
            expect(result).toBe(true);
        });

        it('should return true when disabledProducts is an empty array', () => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({ disabledProducts: [] });

            // WHEN
            const result = processor.isProductEnabled('disputes-ingestion');

            // THEN
            expect(result).toBe(true);
        });

        it('should return false if at least one is disabled', () => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({ disabledProducts: [ 'disputes-order-linking' ] });

            // WHEN
            const result = processor.isProductEnabled([
                'disputes-ingestion',
                'disputes-order-linking',
            ]);

            // THEN
            expect(result).toBe(true);
        });
    });

    describe('IProcessorBase.isActive', () => {

        it.each([
            [ processorStatusEnumSchema.enum.Active, true ],
            [ processorStatusEnumSchema.enum.Deactivating, false ],
            [ processorStatusEnumSchema.enum.Reactivating, false ],
            [ processorStatusEnumSchema.enum.Deactivated, false ],
            [ processorStatusEnumSchema.enum.Invalid, false ],
        ])('Test isActive for status %s expecting %s', (status: IProcessorStatusEnum, expectedResult: boolean) => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({
                status: {
                    status: status,
                    dateCreated: new Date(),
                    actor: faker.internet.email(),
                    reason: faker.lorem.sentence(),
                } });

            // WHEN
            const result = processor.isActive();

            // THEN
            expect(result).toEqual(expectedResult);
        });

        it.each([
            [ processorStatusEnumSchema.enum.Active, false ],
            [ processorStatusEnumSchema.enum.Deactivating, false ],
            [ processorStatusEnumSchema.enum.Reactivating, false ],
            [ processorStatusEnumSchema.enum.Deactivated, true ],
            [ processorStatusEnumSchema.enum.Invalid, false ],
        ])('Test isDeactivated for status %s expecting %s', (status: IProcessorStatusEnum, expectedResult: boolean) => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({
                status: {
                    status: status,
                    dateCreated: new Date(),
                    actor: faker.internet.email(),
                    reason: faker.lorem.sentence(),
                } });

            // WHEN
            const result = processor.isDeactivated();

            // THEN
            expect(result).toEqual(expectedResult);
        });

        it.each([
            [ null ],
            [ undefined ],
        ])('Test isActive for status %s expecting true', (status: IProcessorStatus | null | undefined) => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({
                status: status });

            // WHEN
            const result = processor.isActive();

            // THEN
            expect(result).toEqual(true);
        });

        it.each([
            [ null ],
            [ undefined ],
        ])('Test isDeactivated for status %s expecting false', (status: IProcessorStatus | null | undefined) => {
            // GIVEN
            const processor = processorMocks.getAdyenProcessor({
                status: status });

            // WHEN
            const result = processor.isDeactivated();

            // THEN
            expect(result).toEqual(false);
        });
    });
});
