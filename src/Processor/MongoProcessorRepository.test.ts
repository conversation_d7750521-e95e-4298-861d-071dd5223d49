import { ClientSession, MongoClient, ObjectId, WithId } from 'mongodb';
import { MongoProcessorRepository } from './MongoProcessorRepository';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import {
    IAdyenConnectData,
    ICreateProcessor,
    IManagedConnectionInfo,
    IManagedIntegrationData,
    IMongoProcessor,
    IPayPalConnectData,
    IProcessor,
    IProcessorConcrete,
    ProcessorStatusEnum,
} from './Types';
import processorMocks from './__mocks__/Processors';
import { MongoProcessorMapper } from './MongoProcessorMapper';
import {
    IFindByQueryOperation,
    IPaginatedFindByQueryResult,
} from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { Source } from '../shared/enums';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import mock from 'jest-mock-extended/lib/Mock';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import updateOperationMocks from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';
import addOperationMocks from '../shared/operation/AddOperation/__mocks__/MongoAddOperation';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { ProcessorCollectionName } from './constants';
import { IProcessorRepository } from './IProcessorRepository';
import { ProcessorRepositoryFactory } from './ProcessorRepositoryFactory';

describe(MongoProcessorRepository.name, () => {
    let mockGetByIdOperation: jest.Mocked<IGetByIdOperation<IMongoProcessor>>;
    let mockFindByQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoProcessor>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoProcessor>>;
    let mockAddOperation: jest.Mocked<IAddOperation<IMongoProcessor>>;
    let mongoClient: MongoClient;
    let mongoServer: MongoMemoryServer;
    let repository: MongoProcessorRepository;
    let repositoryFactory: IProcessorRepository;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repositoryFactory = ProcessorRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ProcessorCollectionName,
        );
    });

    beforeEach(() => {
        mockGetByIdOperation = {
            get: jest.fn(),
        } as jest.Mocked<IGetByIdOperation<IMongoProcessor>>;
        mockFindByQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoProcessor>();
        mockUpdateOperation = updateOperationMocks.getUpdateOperation<IMongoProcessor>();
        mockAddOperation = addOperationMocks.getAddOperation<IMongoProcessor>();
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));

        repository = new MongoProcessorRepository(
            mockGetByIdOperation,
            mockFindByQueryOperation,
            mockUpdateOperation,
            mockAddOperation,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName).collection<IMongoProcessor>(ProcessorCollectionName).deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    describe('insert', () => {
        it('should insert a processor and return the result', async () => {
            // GIVEN
            const sample = processorMocks.getAdyenProcessor({
                date_created: stubbedDate,
                date_updated: stubbedDate,
                _id: undefined,
            });

            const newRequest: ICreateProcessor<IAdyenConnectData> = processorMocks
                .concretToCreateProcessor<IAdyenConnectData>(sample);

            const expectedResult: Omit<WithId<IMongoProcessor>, 'isActive'>  = {
                ...newRequest,
                date_created: stubbedDate,
                date_updated: stubbedDate,
                status: {
                    actor: sample.status?.actor || '',
                    status: sample.status?.status || ProcessorStatusEnum.Active,
                    dateCreated: stubbedDate,
                },
                statusHistory: [
                    {
                        actor: sample.status?.actor || '',
                        status: sample.status?.status || ProcessorStatusEnum.Active,
                        dateCreated: stubbedDate,
                    },
                ],
                chargeflow_id: new ObjectId(sample.chargeflow_id),
                accountId: new ObjectId(sample.accountId),
                _id: new ObjectId(),
            };

            // WHEN
            const result = await repositoryFactory.create(newRequest);

            // THEN
            expect(result).not.toBeNull();
            const found =  await mongoClient.db(ChargeflowDbName)
                .collection<IMongoProcessor>(ProcessorCollectionName)
                .findOne({ _id: new ObjectId(result._id) });
            expect(found).toEqual({
                ...expectedResult,
                isManaged: null,
                managedConnection: null,
                _id: new ObjectId(result._id),
            });
        });

        it('should insert a managed processor and return the result', async () => {
            // GIVEN
            const sample = processorMocks.getManagedProcessor({
                date_created: stubbedDate,
                date_updated: stubbedDate,
                _id: undefined,
                isManaged: true,
                managedConnection: {
                    managedType: 'credentials',
                    status: 'connected',
                },
            });

            const newRequest: ICreateProcessor<IManagedIntegrationData> = processorMocks
                .concretToCreateProcessor<IManagedIntegrationData>(sample);

            const expectedResult: Omit<WithId<IMongoProcessor>, 'isActive'>  = {
                ...newRequest,
                date_created: stubbedDate,
                date_updated: stubbedDate,
                status: {
                    actor: sample.status?.actor || '',
                    status: sample.status?.status || ProcessorStatusEnum.Active,
                    dateCreated: stubbedDate,
                },
                statusHistory: [
                    {
                        actor: sample.status?.actor || '',
                        status: sample.status?.status || ProcessorStatusEnum.Active,
                        dateCreated: stubbedDate,
                    },
                ],
                chargeflow_id: new ObjectId(sample.chargeflow_id),
                accountId: new ObjectId(sample.accountId),
                _id: new ObjectId(),
                isManaged: true,
                managedConnection: {
                    managedType: 'credentials',
                    status: 'connected',
                },
            };

            // WHEN
            const result = await repositoryFactory.create(newRequest);

            // THEN
            expect(result).not.toBeNull();
            const found =  await mongoClient.db(ChargeflowDbName)
                .collection<IMongoProcessor>(ProcessorCollectionName)
                .findOne({ _id: new ObjectId(result._id) });
            expect(found).toEqual({
                ...expectedResult,
                _id: new ObjectId(result._id),
            });
        });
    });

    describe('getConcreteByProcessorId', () => {
        it('should find a processor by id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const processor = processorMocks.getAdyenProcessor();

            const getByIdResult: WithId<IMongoProcessor> = {
                ...MongoProcessorMapper.toMongoProcessor(
                    processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
                ),
                _id: id,
            };
            const expectedResult: IProcessorConcrete<IAdyenConnectData>
                = MongoProcessorMapper.fromMongoProcessorConcrete(
                    getByIdResult,
                );

            mockGetByIdOperation.get.mockResolvedValue(getByIdResult);

            // WHEN
            const result = await repository.getConcreteByProcessorId(id.toHexString());

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(
                id, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByProcessorId', () => {
        it('should find a processor by id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const processor = processorMocks.getAdyenProcessor({
                _id: id.toHexString(),
            });

            const getByIdResult: WithId<IMongoProcessor> = {
                ...MongoProcessorMapper.toMongoProcessor(
                    processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
                ),
                _id: id,
            };
            const expectedResult: IProcessor = MongoProcessorMapper
                .fromMongoProcessor(getByIdResult);

            mockGetByIdOperation.get.mockResolvedValue(getByIdResult);

            // WHEN
            const result = await repository.getByProcessorId(id.toHexString());

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(
                id, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByChargeflowIdAndProcessorId', () => {
        it('should find a processor by chargeflow id and processor id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getAdyenProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IAdyenConnectData>> = MongoProcessorMapper
                .fromMongoProcessorConcrete({
                    ...toMongo,
                    _id: id,
                });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoProcessor> = {
                items: [
                    {
                        ...toMongo,
                        _id: id,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByChargeflowIdAndProcessorId(chargeflowId.toHexString(), id.toHexString());

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { _id: id, chargeflow_id: chargeflowId }, expect.anything(), expect.anything(), undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByChargeflowIdAndProcessor', () => {
        it('should find a processor by chargeflow id and processor name and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getAdyenProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IAdyenConnectData>> = MongoProcessorMapper
                .fromMongoProcessorConcrete({
                    ...toMongo,
                    _id: id,
                });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoProcessor> = {
                items: [
                    {
                        ...toMongo,
                        _id: id,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByChargeflowIdAndProcessor(chargeflowId.toHexString(), Source.Adyen);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { chargeflow_id: chargeflowId, processor_name: Source.Adyen }, expect.anything(), expect.anything(), undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByChargeflowIdAndProcessor', () => {
        it('should find a processor by chargeflow id and processor name and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const accountId = new ObjectId();
            const processor = processorMocks.getAdyenProcessor({
                _id: id.toHexString(),
                accountId: accountId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IAdyenConnectData>> = MongoProcessorMapper
                .fromMongoProcessorConcrete({
                    ...toMongo,
                    _id: id,
                });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoProcessor> = {
                items: [
                    {
                        ...toMongo,
                        _id: id,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByAccountIdAndProcessor(accountId.toHexString(), Source.Adyen);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { accountId: accountId, processor_name: Source.Adyen }, expect.anything(), expect.anything(), undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateAccessTokenByChargeflowIdAndProcessor', () => {
        it('should update acces token and date updated fields based on chargeflow id and processor type', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getPayPalProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IPayPalConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IPayPalConnectData>> = {
                ...MongoProcessorMapper.fromMongoProcessorConcrete(
                    {
                        ...toMongo,
                        _id: id,
                    },
                ),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateAccessTokenByChargeflowIdAndProcessor(chargeflowId.toHexString(), Source.PayPal, 'test');

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { chargeflow_id: chargeflowId, processor_name: Source.PayPal }, {
                    $set: { 'date_updated': stubbedDate, 'connect.accessToken' : 'test' },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateAccessTokenByChargeflowIdAndProcessorId', () => {
        it('should update acces token and date updated fields based on chargeflow id and processor id', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getPayPalProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IPayPalConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IPayPalConnectData>> = {
                ...MongoProcessorMapper.fromMongoProcessorConcrete(
                    {
                        ...toMongo,
                        _id: id,
                    },
                ),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateAccessTokenByChargeflowIdAndProcessorId(chargeflowId.toHexString(), id.toHexString() , 'test');

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id, chargeflow_id: chargeflowId }, {
                    $set: { 'date_updated': stubbedDate, 'connect.access_token' : 'test' },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateAccessTokenByProcessorId', () => {
        it('should update acces token and date updated fields based on processor id', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getPayPalProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IPayPalConnectData>(processor),
            );

            const expectedResult: WithId<IProcessorConcrete<IPayPalConnectData>> = {
                ...MongoProcessorMapper.fromMongoProcessorConcrete(
                    {
                        ...toMongo,
                        _id: id,
                    },
                ),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateAccessTokenByProcessorId(id.toHexString(), 'test');

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { '_id': id }, {
                    $set: { 'date_updated': stubbedDate, 'connect.access_token' : 'test' },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateStatus', () => {
        it('should update status and status history fields', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const newStatus = {
                actor: '<EMAIL>',
                dateCreated: new Date(),
                reason: 'test',
                status: ProcessorStatusEnum.Active,
            };
            const processor = processorMocks.getAdyenProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                status: newStatus,
                statusHistory: [ newStatus ],
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor),
            );

            const expectedResult: WithId<IProcessor> = {
                ...MongoProcessorMapper.fromMongoProcessor({
                    ...toMongo,
                    _id: id,
                }),
                _id: id.toHexString(),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateStatus(id.toHexString(), newStatus);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id }, {
                    $set: { status: newStatus },
                    $push: { statusHistory: newStatus },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateManagedStatus', () => {
        it('should update status of managed processor', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const managedConnection: IManagedConnectionInfo = {
                managedType: 'invited',
                status: 'pending',
                invitedEmail: '<EMAIL>',
            };
            const processor = processorMocks.getManagedProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                isManaged: true,
                managedConnection,
            });
            const newManagedConnection: IManagedConnectionInfo = {
                managedType: 'invited',
                status: 'action-required',
                actionRequiredType: 'invalid-invitation',
            };

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IManagedIntegrationData>(processor),
            );

            const expectedResult: WithId<IProcessor> = {
                ...MongoProcessorMapper.fromMongoProcessor({
                    ...toMongo,
                    _id: id,
                }),
                managedConnection: newManagedConnection,
                _id: id.toHexString(),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                managedConnection: newManagedConnection,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateManagedStatus(id.toHexString(), newManagedConnection);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id }, {
                    $set: { managedConnection: newManagedConnection },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateKeyValidity', () => {
        it('should update key validity field', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = processorMocks.getPayPalProcessor({
                _id: id.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                isKeyValid: true,
            });

            const toMongo = MongoProcessorMapper.toMongoProcessor(
                processorMocks.concretToCreateProcessor<IPayPalConnectData>(processor),
            );

            const expectedResult: WithId<IProcessor> = {
                ...MongoProcessorMapper.fromMongoProcessor(
                    {
                        ...toMongo,
                        _id: id,
                    },
                ),
            };

            const updateResult: WithId<IMongoProcessor> = {
                ...toMongo,
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateKeyValidity(id.toHexString(), false);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id }, {
                    $set: { isKeyValid: false },
                }, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getManyByChargeflowId', () => {
        it('should find a list of disputes by chargeflow id and return the result', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const session = mock<ClientSession>();
            const processor1 = processorMocks.getAdyenProcessor({
                _id: id1.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                'processor_name': Source.Adyen,
                connect: {
                    hmac: 'asf',
                    apiKey: 'asdfqw34',
                },
                status: null,
                statusHistory: null,
            });

            const processor2 = processorMocks.getAdyenProcessor({
                _id: id2.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                'processor_name': Source.AfterpayOrClearpay,
                connect: {
                    hmac: 'adfa32asdfadsfasd',
                    apiKey: 'asdadsf234qadsffqw34',
                },
                status: null,
                statusHistory: null,
            });
            const findByQueryResult = {
                items: [ {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                    ),
                    _id: id1,
                },
                {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor2),
                    ),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            const expectedResult: {items: WithId<IProcessor>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...MongoProcessorMapper.fromMongoProcessor(findByQueryResult.items[0]),
                    _id: id1.toHexString(),

                },
                {
                    ...MongoProcessorMapper.fromMongoProcessor(findByQueryResult.items[1]),
                    _id: id2.toHexString(),
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getManyByChargeflowId(chargeflowId.toHexString(), session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'chargeflow_id': chargeflowId,
                    '$or': [
                        { isManaged: false },
                        { isManaged: { $exists: false } },
                    ],
                },
                1,
                10,
                session,
            );
            expect(result).toEqual(expectedResult);
        });

        it('should throw error if chargeflowId is not passed', async () => {

            const session = mock<ClientSession>();
            try {
                await repository.getManyByChargeflowId('', session);
            } catch (e) {
                expect((e as unknown as Error).message).toBe('Chargeflow id is required');
            }
        });
    });

    describe('getManyManaged', () => {
        it('should find a list of managed processors and return the result', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const accountId = new ObjectId();
            const managedConnection: IManagedConnectionInfo = {
                managedType: 'invited',
                status: 'pending',
                invitedEmail: '<EMAIL>',
            };
            const processor1 = processorMocks.getManagedProcessor({
                _id: id1.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                accountId: accountId.toHexString(),
                isManaged: true,
                managedConnection,
            });
            const processor2 = processorMocks.getManagedProcessor({
                _id: id2.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                accountId: accountId.toHexString(),
                isManaged: true,
                managedConnection,
                processor_name: Source.ZipPay,
            });
            const session = mock<ClientSession>();

            // and
            const findByQueryResult = {
                items: [
                    {
                        ...MongoProcessorMapper.toMongoProcessor(
                            processorMocks.concretToCreateProcessor<IManagedIntegrationData>(processor1),
                        ),
                        _id: id1,
                    },
                    {
                        ...MongoProcessorMapper.toMongoProcessor(
                            processorMocks.concretToCreateProcessor<IManagedIntegrationData>(processor2),
                        ),
                        _id: id2,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            const expectedResult: {items: WithId<IProcessor>[], pagination: IPaginationCalculatorResult} = {
                items: [
                    {
                        ...MongoProcessorMapper.fromMongoProcessor(findByQueryResult.items[0]),
                        _id: id1.toHexString(),

                    },
                    {
                        ...MongoProcessorMapper.fromMongoProcessor(findByQueryResult.items[1]),
                        _id: id2.toHexString(),
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getManyManaged({
                status: 'pending',
                type: 'invited',
                accountId: accountId.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            }, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    isManaged: true,
                    'managedConnection.status': 'pending',
                    'managedConnection.managedType': 'invited',
                    account_id: accountId,
                    chargeflow_id: chargeflowId,
                },
                1,
                10,
                session,
            );
            expect(result).toEqual(expectedResult);
        });

        it('should throw error if chargeflowId is not passed', async () => {

            const session = mock<ClientSession>();
            try {
                await repository.getManyByChargeflowId('', session);
            } catch (e) {
                expect((e as unknown as Error).message).toBe('Chargeflow id is required');
            }
        });
    });

    describe('getManyByChargeflowIdAndProcessorName', () => {
        it('returns processors and pagination when chargeflowId and processorName are provided', async () => {
            const chargeflowId = new ObjectId();
            const processorName = Source.Adyen;
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;
            const id1 = new ObjectId();
            const processor1 = processorMocks.getAdyenProcessor({
                _id: id1.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                'processor_name': Source.Adyen,
                connect: {
                    hmac: 'asf',
                    apiKey: 'asdfqw34',
                },
                status: null,
                statusHistory: null,
            });
            const expectedPagination = {
                totalPages: 1,
                currentPage: 1,
                hasPreviousPage: false,
                hasNextPage: false,
                itemsPerPage: 10,
                totalItems: 1,
            };
            const expectedResult: {items: WithId<IProcessor>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...MongoProcessorMapper.fromMongoProcessor(
                        {
                            ...MongoProcessorMapper.toMongoProcessor(
                                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                            ),
                            _id: id1,
                        },
                    ),
                    _id: id1.toHexString(),
                },
                ],
                pagination: expectedPagination };

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoProcessor> = {
                items: [ {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                    ),
                    _id: id1,
                },
                ],
                pagination: expectedPagination,
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const result = await repository.getManyByChargeflowIdAndProcessorName(chargeflowId.toHexString(), processorName, session, page, pageSize);

            expect(result).toEqual(expectedResult);
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith({ chargeflow_id: new ObjectId(chargeflowId), processor_name: processorName }, page, pageSize, session);
        });

        it('throws an error when chargeflowId is not provided', async () => {
            const processorName = Source.Adyen;
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;

            await expect(() => repository.getManyByChargeflowIdAndProcessorName('', processorName, session, page, pageSize)).rejects.toThrow('Chargeflow id is required');
        });
    });

    describe('getManyByAccountIdAndProcessorName', () => {
        it('returns processors and pagination when accountId and processorName are provided', async () => {
            const accountId = new ObjectId();
            const processorName = Source.Adyen;
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;
            const id1 = new ObjectId();
            const processor1 = processorMocks.getAdyenProcessor({
                _id: id1.toHexString(),
                accountId: accountId.toHexString(),
                'processor_name': Source.Adyen,
                connect: {
                    hmac: 'asf',
                    apiKey: 'asdfqw34',
                },
                status: null,
                statusHistory: null,
            });
            const expectedPagination = {
                totalPages: 1,
                currentPage: 1,
                hasPreviousPage: false,
                hasNextPage: false,
                itemsPerPage: 10,
                totalItems: 1,
            };
            const expectedResult: {items: WithId<IProcessor>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...MongoProcessorMapper.fromMongoProcessor(
                        {
                            ...MongoProcessorMapper.toMongoProcessor(
                                processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                            ),
                            _id: id1,
                        },
                    ),
                    _id: id1.toHexString(),
                },
                ],
                pagination: expectedPagination };

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoProcessor> = {
                items: [ {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                    ),
                    _id: id1,
                },
                ],
                pagination: expectedPagination,
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const result = await repository.getManyByAccountIdAndProcessorName(accountId.toHexString(), processorName, session, page, pageSize);

            expect(result).toEqual(expectedResult);
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith({ accountId: new ObjectId(accountId), processor_name: processorName }, page, pageSize, session);
        });

        it('throws an error when accountId is not provided', async () => {
            const processorName = Source.Adyen;
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;

            await expect(() => repository.getManyByAccountIdAndProcessorName('', processorName, session, page, pageSize)).rejects.toThrow('Account id is required');
        });
    });

    describe('getAllValidProcessorsForEnabledShops', () => {
        it('should return all valid processors for enabled shops', async () => {
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const processor1 = processorMocks.getAdyenProcessor({
                _id: id1.toHexString(),
                chargeflow_id: new ObjectId().toHexString(),
                'processor_name': Source.Adyen,
                connect: {
                    hmac: 'asf',
                    apiKey: 'asdfqw34',
                },
                status: null,
                statusHistory: null,
            });
            const processor2 = processorMocks.getAdyenProcessor({
                _id: id2.toHexString(),
                chargeflow_id: new ObjectId().toHexString(),
                'processor_name': Source.AfterpayOrClearpay,
                connect: {
                    hmac: 'adfa32asdfadsfasd',
                    apiKey: 'asdadsf234qadsffqw34',
                },
                status: null,
                statusHistory: null,
            });
            const expectedPagination = {
                totalPages: 1,
                currentPage: 1,
                hasPreviousPage: false,
                hasNextPage: false,
                itemsPerPage: 1,
                totalItems: 1,
            };

            const findByQueryResult = {
                items: [ {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor1),
                    ),
                    _id: id1,
                },
                {
                    ...MongoProcessorMapper.toMongoProcessor(
                        processorMocks.concretToCreateProcessor<IAdyenConnectData>(processor2),
                    ),
                    _id: id2,
                },
                ],
                pagination: expectedPagination,
            };

            const expectedResult: {items: WithId<IProcessor>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...MongoProcessorMapper.fromMongoProcessor({
                        ...findByQueryResult.items[0],
                        _id: id1,
                    }),
                    _id: id1.toHexString(),
                },
                {
                    ...MongoProcessorMapper.fromMongoProcessor({
                        ...findByQueryResult.items[1],
                        _id: id2,
                    }),
                    _id: id2.toHexString(),
                },
                ],
                pagination: expectedPagination,
            };

            const aggregationPipeline = [
                {
                    $match: {
                        processor_name: Source.AfterpayOrClearpay,
                    },
                },
                {
                    $lookup: {
                        from: 'shops',
                        localField: 'chargeflow_id',
                        foreignField: 'chargeflow_id',
                        as: 'shop',
                    },
                },
                {
                    $unwind: '$shop',
                },
                {
                    $match: {
                        'shop.chargeflow_status': { $nin: [ 'disabled', 'uninstalled', 'inactive' ] },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        processor_name: 1,
                        connect: 1,
                        isKeyValid: 1,
                        chargeflow_id: 1,
                        accountId: 1,
                    },
                },
            ];

            mockFindByQueryOperation.findWithAggregation.mockResolvedValue(findByQueryResult);

            const result = await repository.getAllValidProcessorsForEnabledShops(Source.AfterpayOrClearpay, page, pageSize, session);

            expect(result).toEqual(expectedResult);
            expect(mockFindByQueryOperation.findWithAggregation).toHaveBeenCalledWith(aggregationPipeline, page, pageSize, session);
        });
    });

    describe('getAllByChargeflowId', () => {
        const session = mock<ClientSession>();
        it('should throw an error if chargeflowId is not provided', async () => {
            await expect(repository.getAllByChargeflowId('', session)).rejects.toThrow('Chargeflow id is required');
        });

        it('should return processors and pagination when chargeflowId is provided', async () => {
            const chargeflowId = '60c72b2f9b1e8b3f4c8b4567';
            const page = 1;
            const pageSize = 10;
            const findByQueryResult = {
                items: [
                    { _id: new ObjectId(), processor_name: 'Processor 1', connect: true, isKeyValid: true, chargeflow_id: new ObjectId(chargeflowId), accountId: new ObjectId() },
                    { _id: new ObjectId(), processor_name: 'Processor 2', connect: true, isKeyValid: true, chargeflow_id: new ObjectId(chargeflowId), accountId: new ObjectId() },
                ],
                pagination: {
                    totalItems: 2,
                    totalPages: 1,
                    currentPage: 1,
                    pageSize: 10,
                } as unknown as IPaginationCalculatorResult,
            };

            const expectedResult = {
                items: [
                    { _id: new ObjectId(), processor_name: 'Processor 1', connect: true, isKeyValid: true, chargeflow_id: new ObjectId(chargeflowId), accountId: new ObjectId() },
                    { _id: new ObjectId(), processor_name: 'Processor 2', connect: true, isKeyValid: true, chargeflow_id: new ObjectId(chargeflowId), accountId: new ObjectId() },
                ],
                pagination: findByQueryResult.pagination,
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult as unknown as IPaginatedFindByQueryResult<IMongoProcessor>);
            MongoProcessorMapper.fromMongoProcessors = jest.fn().mockReturnValue(expectedResult.items);

            const result = await repository.getAllByChargeflowId(chargeflowId, session, page, pageSize);

            expect(result).toEqual(expectedResult);
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { chargeflow_id: new ObjectId(chargeflowId) },
                page,
                pageSize,
                session,
            );
        });
    });

    describe('getManyProcessorsByIds', () => {
        it('should fetch processors by an array of ids using findByQueryOperation', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const ids = [ id1.toHexString(), id2.toHexString() ];
            const session = mock<ClientSession>();

            const mockProcessors = [
                {
                    ...processorMocks.getDefaultProcessor(),
                    _id: id1,
                },
                {
                    ...processorMocks.getDefaultProcessor(),
                    _id: id2,
                },
            ];

            const mockResult = {
                items: mockProcessors,
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            } as unknown as IPaginatedFindByQueryResult<IMongoProcessor>;

            mockFindByQueryOperation.find.mockResolvedValue(mockResult);

            // WHEN
            const { items } = await repository.getManyProcessorsByIds(ids, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { _id: { $in: [ id1, id2 ] } },
                1,
                10,
                session,
            );
            expect(items).toEqual(MongoProcessorMapper.fromMongoProcessors(mockResult.items));
        });

        it('should throw an error if ids array is empty', async () => {
            const session = mock<ClientSession>();
            const page = 1;
            const pageSize = 10;
            await expect(repository.getManyProcessorsByIds([], session, page, pageSize)).rejects.toThrow('Processor Ids array is required and cannot be empty');
        });
    });

    describe('updateLastSyncedAt', () => {
        it('should update lastSyncedAt using updateOperation', async () => {
            // GIVEN
            const id = new ObjectId();
            const lastSyncedDate = new Date('2025-02-07T12:00:00Z');
            const mockProcessor = processorMocks.getDefaultProcessor({
                _id: id.toHexString(),
                lastSyncedAt: lastSyncedDate,
            });

            mockUpdateOperation.updateWithFilter.mockResolvedValue({
                ...mockProcessor,
                _id: id,
            } as unknown as WithId<IMongoProcessor>);

            await repository.updateArnsSyncData({
                processorId: id.toHexString(),
                lastSyncedAt: lastSyncedDate,
                arnsSyncStatus: 'synced',
                arnsSyncFailureReason: null,
            });

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id },
                { $set: {
                    lastSyncedAt: lastSyncedDate,
                    arnsSyncFailureReason: null,
                    arnsSyncStatus: 'synced',
                } },
            );
        });
    });

    describe('getManyByARNsLastSyncedAt', () => {
        it('should find processors in date range using findByQueryOperation', async () => {
        // GIVEN
            const from = new Date('2025-02-01');
            const to = new Date('2025-02-07');
            const limit = 50;

            const mockProcessors = [
                {
                    ...processorMocks.getDefaultProcessor({ lastSyncedAt: from }),
                    _id: new ObjectId(),
                },

                {
                    ...processorMocks.getDefaultProcessor({ lastSyncedAt: to }),
                    _id: new ObjectId(),
                },
            ];

            const mockResult = {
                items: mockProcessors,
                pagination: {
                    totalItems: 2,
                    totalPages: 1,
                    currentPage: 1,
                    itemsPerPage: limit,
                },
            } as unknown as IPaginatedFindByQueryResult<IMongoProcessor>;

            mockFindByQueryOperation.find.mockResolvedValue(mockResult);

            // WHEN
            const { items, pagination } = await repository.getManyByARNsLastSyncedAt({ from, to, limit });

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { $or: [
                    { lastSyncedAt: { $gte: from, $lte: to } },
                    { lastSyncedAt: null },
                ]  },
                1,
                limit,
            );
            expect(items).toEqual(MongoProcessorMapper.fromMongoProcessors(mockResult.items));
            expect(pagination?.totalItems).toBe(2);
        });
    });

});
