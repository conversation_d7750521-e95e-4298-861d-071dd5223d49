import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { IConnectData, ICreateProcessor, IMongoProcessor, IProcessor, IProcessorConcrete, IProcessorStatus, ProcessorConcrete, ProcessorStatusEnum } from './Types';
import { WithId } from '../shared/helper-types';

export class MongoProcessorMapper {
    static toMongoProcessor(processor: ICreateProcessor<IConnectData>): IMongoProcessor {
        if (!processor) {
            throw new Error('Processor is required for mapping');
        }
        const processorStatus: IProcessorStatus = {
            status: processor.status ?? ProcessorStatusEnum.Active,
            actor: processor.createdByEmail,
            dateCreated: new Date(),
        };

        return {
            ...processor,
            date_created: new Date(),
            date_updated: new Date(),
            status: processorStatus,
            statusHistory: [ processorStatus ],
            chargeflow_id: new ObjectId(processor.chargeflow_id),
            accountId: new ObjectId(processor.accountId),
        };
    }

    static fromMongoProcessorConcrete<T extends IConnectData>(processor: MongoWithId<IMongoProcessor>): WithId<IProcessorConcrete<T>> {
        if (!processor) {
            throw new Error('Processor is required for mapping');
        }

        if (!processor.chargeflow_id || !processor.accountId || !processor._id) {
            throw new Error('Processor has invalid format');
        }

        return new ProcessorConcrete<T>(
            processor.chargeflow_id.toString(),
            processor.accountId.toString(),
            processor.processor_name,
            processor.connect as T,
            processor.status,
            processor.statusHistory,
            processor.isKeyValid,
            processor._id.toString(),
            processor.date_created,
            processor.date_updated,
            processor.isManaged,
            processor.managedConnection,
            processor.hasSubscription,
            processor.lastSyncedAt,
        );
    }

    static fromMongoProcessor(processor: MongoWithId<IMongoProcessor>): IProcessor {
        return MongoProcessorMapper.fromMongoProcessorConcrete(processor);
    }

    static fromMongoProcessors(processors: MongoWithId<IMongoProcessor>[]): IProcessor[] {
        return processors.map(MongoProcessorMapper.fromMongoProcessor);
    }

    static fromMongoProcessorsConcrete<T extends IConnectData>(processors: MongoWithId<IMongoProcessor>[]): IProcessorConcrete<T>[] {
        return processors.map(MongoProcessorMapper.fromMongoProcessorConcrete<T>);
    }
}
