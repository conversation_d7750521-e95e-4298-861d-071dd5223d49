import { ClientSession, Document, Filter, ObjectId } from 'mongodb';
import { Source } from '../shared/enums';
import { WithId } from '../shared/helper-types';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import {
    IProcessorRepository,
    UpdateArnsSyncDataPayload,
} from './IProcessorRepository';
import { MongoProcessorMapper } from './MongoProcessorMapper';
import {
    IConnectData,
    ICreateProcessor,
    IGetManyByARNsLastSyncedAtPayload,
    IManagedConnectionInfo,
    IManagedIntegrationFilter,
    IMongoProcessor,
    IProcessor,
    IProcessorConcrete,
    IProcessorStatus,
} from './Types';

export class MongoProcessorRepository implements IProcessorRepository {
    constructor(
		private readonly getByIdOperation: IGetByIdOperation<IMongoProcessor>,
		private readonly findByQueryOperation: IFindByQueryOperation<IMongoProcessor>,
		private readonly updateOperation: IUpdateOperation<IMongoProcessor>,
		private readonly addOperation: IAddOperation<IMongoProcessor>,
    ) {}

    async create(
        input: ICreateProcessor<IConnectData>,
        session?: ClientSession,
    ): Promise<WithId<IProcessor>> {
        return MongoProcessorMapper.fromMongoProcessor(
            await this.addOperation.add(
                MongoProcessorMapper.toMongoProcessor(input),
                session,
            ),
        );
    }

    async getConcreteByProcessorId<T extends IConnectData>(
        id: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        const result = await this.getByIdOperation.get(new ObjectId(id), session);

        return result === null
            ? null
            : MongoProcessorMapper.fromMongoProcessorConcrete<T>(result);
    }

    async getByProcessorId(
        id: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessor> | null> {
        const result = await this.getByIdOperation.get(new ObjectId(id), session);

        return result === null
            ? null
            : MongoProcessorMapper.fromMongoProcessor(result);
    }

    async getByChargeflowIdAndProcessorId<T extends IConnectData>(
        chargeflowId: string,
        processorId: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }
        if (!processorId) {
            throw new Error('Processor id is required');
        }

        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find(
            {
                _id: new ObjectId(processorId),
                chargeflow_id: new ObjectId(chargeflowId),
            },
            page,
            pageSize,
            session,
        );

        return (
            MongoProcessorMapper.fromMongoProcessorsConcrete<T>(result.items)[0] ??
			null
        );
    }

    // should be removed once all the calls are updated to use getByChargeflowIdAndProcessorId
    async getByChargeflowIdAndProcessor<T extends IConnectData>(
        chargeflowId: string,
        processor: Source,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find(
            { chargeflow_id: new ObjectId(chargeflowId), processor_name: processor },
            page,
            pageSize,
            session,
        );

        return (
            MongoProcessorMapper.fromMongoProcessorsConcrete<T>(result.items)[0] ??
			null
        );
    }

    async getByAccountIdAndProcessor<T extends IConnectData>(
        accountId: string,
        processor: Source,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        if (!accountId) {
            throw new Error('Account id is required');
        }

        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find(
            { accountId: new ObjectId(accountId), processor_name: processor },
            page,
            pageSize,
            session,
        );

        return (
            MongoProcessorMapper.fromMongoProcessorsConcrete<T>(result.items)[0] ??
			null
        );
    }

    async getManyByChargeflowId(
        chargeflowId: string,
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const result = await this.findByQueryOperation.find(
            {
                chargeflow_id: new ObjectId(chargeflowId),
                $or: [ { isManaged: false }, { isManaged: { $exists: false } } ],
            },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getAllByChargeflowId(
        chargeflowId: string,
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const result = await this.findByQueryOperation.find(
            {
                chargeflow_id: new ObjectId(chargeflowId),
            },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getManyByChargeflowIdAndProcessorName(
        chargeflowId: string,
        processorName: Source,
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const filter = {
            chargeflow_id: new ObjectId(chargeflowId),
            processor_name: processorName,
        };
        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getManyByAccountIdAndProcessorName(
        accountId: string,
        processorName: Source,
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        if (!accountId) {
            throw new Error('Account id is required');
        }

        const filter = {
            accountId: new ObjectId(accountId),
            processor_name: processorName,
        };
        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getManyByARNsLastSyncedAt({
        from,
        to,
        limit,
    }: IGetManyByARNsLastSyncedAtPayload): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        const filter = {
            $or: [ { lastSyncedAt: { $gte: from, $lte: to } }, { lastSyncedAt: null } ],
        };

        const result = await this.findByQueryOperation.find(filter, 1, limit);

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getManyProcessorsByIds(
        processorIds: string[],
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        if (!processorIds?.length) {
            throw new Error('Processor Ids array is required and cannot be empty');
        }

        const objectIds = processorIds.map(id => new ObjectId(id));
        const result = await this.findByQueryOperation.find(
            { _id: { $in: objectIds } },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async getManyManaged(
        filters?: IManagedIntegrationFilter,
        session?: ClientSession,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        const { chargeflowId, accountId, processorName, status, type } =
			filters ?? {};
        const filter = { isManaged: true };

        if (chargeflowId) {
            Object.assign(filter, { chargeflow_id: new ObjectId(chargeflowId) });
        }
        if (accountId) {
            Object.assign(filter, { account_id: new ObjectId(accountId) });
        }
        if (processorName) {
            Object.assign(filter, { processor_name: processorName });
        }
        if (status) {
            Object.assign(filter, { 'managedConnection.status': status });
        }
        if (type) {
            Object.assign(filter, { 'managedConnection.managedType': type });
        }
        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async updateAccessTokenByChargeflowIdAndProcessor<T extends IConnectData>(
        chargeflowId: string,
        processor: Source,
        accessToken: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        const filter = {
            chargeflow_id: new ObjectId(chargeflowId),
            processor_name: processor,
        };
        const updateFilter = {
            $set: {
                date_updated: new Date(),
                ...(processor === Source.Stripe
                    ? { 'connect.access_token': accessToken }
                    : { 'connect.accessToken': accessToken }),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoProcessorMapper.fromMongoProcessorConcrete<T>(result) ?? null;
    }

    async updateAccessTokenByChargeflowIdAndProcessorId<T extends IConnectData>(
        chargeflowId: string,
        processorId: string,
        accessToken: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        const filter = {
            _id: new ObjectId(processorId),
            chargeflow_id: new ObjectId(chargeflowId),
        };
        const updateFilter = {
            $set: { date_updated: new Date(), 'connect.access_token': accessToken },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoProcessorMapper.fromMongoProcessorConcrete<T>(result) ?? null;
    }

    async updateAccessTokenByProcessorId<T extends IConnectData>(
        processorId: string,
        accessToken: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessorConcrete<T>> | null> {
        const filter = { _id: new ObjectId(processorId) };
        const updateFilter = {
            $set: { date_updated: new Date(), 'connect.access_token': accessToken },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoProcessorMapper.fromMongoProcessorConcrete<T>(result) ?? null;
    }

    async updateStatus(
        id: string,
        status: IProcessorStatus,
        session?: ClientSession,
    ): Promise<WithId<IProcessor> | null> {
        const result = await this.updateOperation.updateWithFilter(
            {
                _id: new ObjectId(id),
            },
            {
                $set: {
                    status,
                },
                $push: {
                    statusHistory: status,
                },
            },
            session,
        );

        return MongoProcessorMapper.fromMongoProcessor(result) ?? null;
    }

    async updateManagedStatus(
        id: string,
        status: IManagedConnectionInfo,
        session?: ClientSession,
    ): Promise<WithId<IProcessor> | null> {
        const result = await this.updateOperation.updateWithFilter(
            {
                _id: new ObjectId(id),
            },
            {
                $set: {
                    managedConnection: status,
                },
            },
            session,
        );

        return MongoProcessorMapper.fromMongoProcessor(result) ?? null;
    }

    async updateArnsSyncData({
        processorId,
        lastSyncedAt,
        arnsSyncStatus,
        arnsSyncFailureReason,
    }: UpdateArnsSyncDataPayload): Promise<WithId<IProcessor> | null> {
        const result = await this.updateOperation.updateWithFilter(
            {
                _id: new ObjectId(processorId),
            },
            {
                $set: {
                    ...(lastSyncedAt && { lastSyncedAt }),
                    ...(arnsSyncStatus && { arnsSyncStatus }),
                    ...(arnsSyncFailureReason !== undefined && { arnsSyncFailureReason }),
                },
            },
        );

        return MongoProcessorMapper.fromMongoProcessor(result) ?? null;
    }

    async updateKeyValidity(
        id: string,
        isKeyValid: boolean,
        session?: ClientSession,
    ): Promise<WithId<IProcessor> | null> {
        const result = await this.updateOperation.updateWithFilter(
            {
                _id: new ObjectId(id),
            },
            {
                $set: {
                    isKeyValid,
                },
            },
            session,
        );

        return MongoProcessorMapper.fromMongoProcessor(result) ?? null;
    }

    async getAllValidProcessorsForEnabledShops(
        processorName: Source,
        page: number = 1,
        pageSize: number = 10,
        session?: ClientSession,
    ): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}> {
        const aggregationPipeline: Document[] = [
            {
                $match: {
                    processor_name: processorName,
                },
            },
            {
                $lookup: {
                    from: 'shops',
                    localField: 'chargeflow_id',
                    foreignField: 'chargeflow_id',
                    as: 'shop',
                },
            },
            {
                $unwind: '$shop',
            },
            {
                $match: {
                    'shop.chargeflow_status': {
                        $nin: [ 'disabled', 'uninstalled', 'inactive' ],
                    },
                },
            },
            {
                $project: {
                    _id: 1,
                    processor_name: 1,
                    connect: 1,
                    isKeyValid: 1,
                    chargeflow_id: 1,
                    accountId: 1,
                },
            },
        ];
        const result = await this.findByQueryOperation.findWithAggregation<IMongoProcessor>(
            aggregationPipeline,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }

    async updateProcessorConnectData(
        processorId: string,
        connectData: IConnectData,
        session?: ClientSession,
    ) {
        if (!processorId) {
            throw new Error('Processor ID is required');
        }

        const filter = { _id: new ObjectId(processorId) };
        const updateFilter = {
            $set: {
                connect: connectData,
                date_updated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return result
            ? MongoProcessorMapper.fromMongoProcessorConcrete<IConnectData>(result)
            : null;
    }

    async getByStripeUserIdAndChargeflowId(
        stripeInternalAccountId: string,
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<WithId<IProcessor> | null> {
        const result = await this.findByQueryOperation.findOne(
            {
                processor_name: Source.Stripe,
                'connect.stripe_user_id': stripeInternalAccountId,
                chargeflow_id: new ObjectId(chargeflowId),
            },
            session,
        );
        if (result) {
            return MongoProcessorMapper.fromMongoProcessor(result);
        }
        return null;
    }

    async getByProcessor(
        processor: Source,
        page?: number,
        pageSize?: number,
        startFromId?: string,
        session?: ClientSession,
    ): Promise<{
		items: WithId<IProcessor>[] | null;
		pagination?: IPaginationCalculatorResult;
	}> {
        const filter: Filter<IMongoProcessor> = {
            processor_name: processor,
        };

        if (startFromId) {
            filter._id = { $gte: new ObjectId(startFromId) };
        }

        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );
        return {
            items: MongoProcessorMapper.fromMongoProcessors(result.items),
            pagination: result.pagination,
        };
    }
}
