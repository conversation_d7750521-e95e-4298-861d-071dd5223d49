// Generated by ts-to-zod
import { z } from 'zod';

export const iAdyenConnectDataSchema = z.object({
    hmac: z.string(),
    apiKey: z.string(),
    webhookID: z.string().optional(),
    companyID: z.string().optional(),
});

export const iAfterpayConnectDataSchema = z.object({
    username: z.string(),
    password: z.string(),
});

export const iPayPalConnectDataSchema = z.object({
    scope: z.string(),
    access_token: z.string(),
    token_type: z.string(),
    expires_in: z.number(),
    refresh_token: z.string(),
    nonce: z.string(),
    userId: z.string().optional(),
});

export const iStripeConnectDataSchema = z.object({
    /**
     * The unique id of the account we have been granted access to, as a string.
     * The rest of the fields can be ignored
     */
    stripe_user_id: z.string(),
    access_token: z.string(),
    scope: z.string(),
    livemode: z.boolean(),
    token_type: z.literal('bearer'),
    refresh_token: z.string(),
    stripe_publishable_key: z.string(),
    stripe_statement_descriptor: z.string().optional(),
});

export const iKlarnaConnectDataSchema = z.object({
    username: z.string(),
    password: z.string(),
    chosenRegion: z.string().optional(),
});

export const iShopifyPaymentsConnectDataSchema = z.object({
    accessToken: z.string(),
    scope: z.string(),
    shopName: z.string(),
    domain: z.string(),
});

export const iManagedIntegrationDataSchema = z.object({
    username: z.string().optional(),
    password: z.string().optional(),
});

export const iWooPaymentsConnectDataSchema = z.object({
    consumerKey: z.string(),
    consumerSecret: z.string(),
    domain: z.string(),
});

export const iGmailConnectDataSchema = z.object({
    email: z.string(),
    access_token: z.string(),
    refresh_token: z.string().optional(),
    scope: z.string(),
    id_token: z.string(),
    token_type: z.string(),
    expiry_date: z.number(),
});

export const iBraintreeConnectDataSchema = z.object({
    merchant_id: z.string(),
    public_key: z.string(),
    private_key: z.string(),
});

export const iRechargeConnectDataSchema = z.object({
    access_token: z.string(),
    expires_in: z.number(),
    token_type: z.string(),
    scope: z.string(),
    refresh_token: z.string(),
});

export const iConnectDataSchema = z.union([
    iAdyenConnectDataSchema,
    iAfterpayConnectDataSchema,
    iPayPalConnectDataSchema,
    iStripeConnectDataSchema,
    iKlarnaConnectDataSchema,
    iShopifyPaymentsConnectDataSchema,
    iManagedIntegrationDataSchema,
    iWooPaymentsConnectDataSchema,
    iGmailConnectDataSchema,
    iBraintreeConnectDataSchema,
    iRechargeConnectDataSchema,
]);
