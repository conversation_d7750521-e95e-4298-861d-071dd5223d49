import { ClientSession } from "mongodb";
import { Source } from "../shared/enums";
import { WithId } from "../shared/helper-types";
import { IPaginationCalculatorResult } from "../shared/pagination/IPaginationCalculatorOptionsResult";
import {
	IConnectData,
	ICreateProcessor,
	IGetManyByARNsLastSyncedAtPayload,
	IManagedConnectionInfo,
	IManagedIntegrationFilter,
	IProcessor,
	IProcessorConcrete,
	IProcessorStatus,
	ProcessorARNsSyncStatus,
} from "./Types";

export interface UpdateArnsSyncDataPayload {
	processorId: string;
	lastSyncedAt?: Date;
	arnsSyncStatus?: ProcessorARNsSyncStatus;
	arnsSyncFailureReason?: string | null;
}

export interface IProcessorRepository {
	getConcreteByProcessorId: <T extends IConnectData>(
		id: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	getByProcessorId: (
		id: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;
	getByProcessor(
		processor: Source,
		page?: number,
		pageSize?: number,
		startFromId?: string,
		session?: ClientSession,
	): Promise<{
		items: WithId<IProcessor>[] | null;
		pagination?: IPaginationCalculatorResult;
	}>;
	getByChargeflowIdAndProcessorId: <T extends IConnectData>(
		chargeflowId: string,
		processorId: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	getByChargeflowIdAndProcessor: <T extends IConnectData>(
		chargeflowId: string,
		processor: Source,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	getByAccountIdAndProcessor: <T extends IConnectData>(
		accountId: string,
		processor: Source,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	getManyByChargeflowId: (
		chargeflowId: string,
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getManyByChargeflowIdAndProcessorName: (
		chargeflowId: string,
		processorName: Source,
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getManyByAccountIdAndProcessorName: (
		accountId: string,
		processorName: Source,
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getManyManaged: (
		filters?: IManagedIntegrationFilter,
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getManyByARNsLastSyncedAt: (
		payload: IGetManyByARNsLastSyncedAtPayload,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	updateAccessTokenByChargeflowIdAndProcessor: <T extends IConnectData>(
		chargeflowId: string,
		processor: Source,
		accessToken: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	updateAccessTokenByChargeflowIdAndProcessorId: <T extends IConnectData>(
		chargeflowId: string,
		processorId: string,
		accessToken: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	updateAccessTokenByProcessorId: <T extends IConnectData>(
		processorId: string,
		accessToken: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessorConcrete<T>> | null>;

	updateStatus: (
		id: string,
		newStatus: IProcessorStatus,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;

	updateManagedStatus: (
		id: string,
		newStatus: IManagedConnectionInfo,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;

	updateKeyValidity: (
		id: string,
		isKeyValid: boolean,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;

	create: (
		processor: ICreateProcessor<IConnectData>,
		session?: ClientSession,
	) => Promise<WithId<IProcessor>>;

	getAllValidProcessorsForEnabledShops: (
		processorName: Source,
		page?: number,
		pageSize?: number,
		session?: ClientSession,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	updateProcessorConnectData: (
		processorId: string,
		connectData: IConnectData,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;

	updateArnsSyncData: (
		payload: UpdateArnsSyncDataPayload,
	) => Promise<WithId<IProcessor> | null>;

	getAllByChargeflowId(
		chargeflowId: string,
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	): Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getManyProcessorsByIds: (
		processorIds: string[],
		session?: ClientSession,
		page?: number,
		pageSize?: number,
	) => Promise<{
		items: WithId<IProcessor>[];
		pagination?: IPaginationCalculatorResult;
	}>;

	getByStripeUserIdAndChargeflowId: (
		stripeInternalAccountId: string,
		chargeflowId: string,
		session?: ClientSession,
	) => Promise<WithId<IProcessor> | null>;
}
