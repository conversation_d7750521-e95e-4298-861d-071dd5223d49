import { ProcessorRepositoryFactory } from './ProcessorRepositoryFactory';
import { MongoProcessorRepository } from './MongoProcessorRepository';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';

describe(ProcessorRepositoryFactory.name, () => {

    beforeAll(() => {
        process.env.STACK_NAME = faker.word.noun();
    });

    describe(ProcessorRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mockMongoClient = {} as MongoClient;
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = ProcessorRepositoryFactory.create(mockMongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoProcessorRepository);
        });
    });
});
