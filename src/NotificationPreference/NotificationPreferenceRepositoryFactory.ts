import { MongoClient } from 'mongodb';
import { IMongoNotificationPreference } from './INotificationPreference';
import { INotificationPreferenceRepository } from './INotificationPreferenceRepository';
import { MongoNotificationPreferenceRepository } from './MongoNotificationPreferenceRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class NotificationPreferenceRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): INotificationPreferenceRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoNotificationPreference>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoNotificationPreference>(mongoClient, dbName, collectionName);

        return new MongoNotificationPreferenceRepository(findByQueryOperation, updateOperation);
    }
}
