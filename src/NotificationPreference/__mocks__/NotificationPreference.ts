import { ObjectId } from 'mongodb';
import { INotificationPreference } from '../INotificationPreference';
import { faker } from '@faker-js/faker';

const getMockedDoc = (overrides?: Partial<INotificationPreference>): INotificationPreference => ({
    _id: new ObjectId().toHexString(),
    email: faker.internet.email(),
    dateCreated: new Date(),
    dateUpdated: new Date(),
    businessUnitId: faker.string.numeric(12),
    accountId: faker.string.numeric(12),
    settings: {
        wonDispute: true,
    },
    ...overrides,
});

export default {
    getMockedDoc,
};
