import { Document, ObjectId } from 'mongodb';

export interface INotificationPreferenceSettings {
    [key: string]: boolean;
}

export interface INotificationPreferenceBase extends Document {
    dateCreated: Date,
    dateUpdated: Date,
    email: string,
    settings: INotificationPreferenceSettings,
}

export interface IMongoNotificationPreference extends INotificationPreferenceBase {
    _id: ObjectId,
    businessUnitId?: ObjectId,
    accountId?: ObjectId,
}

export interface INotificationPreference extends INotificationPreferenceBase {
    businessUnitId?: string,
    accountId?: string,
}
