import { ObjectId } from 'mongodb';
import { IMongoNotificationPreference } from './INotificationPreference';
import { MongoNotificationPreferenceRepository } from './MongoNotificationPreferenceRepository';
import mocks from './__mocks__/NotificationPreference';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import getUpdateOperation from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';

describe(MongoNotificationPreferenceRepository.name, () => {
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoNotificationPreference>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoNotificationPreference>>;
    let repository: MongoNotificationPreferenceRepository;
    const mockDateString = new Date().toISOString();

    beforeEach(() => {
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoNotificationPreference>();
        mockUpdateOperation = getUpdateOperation.getUpdateOperation<IMongoNotificationPreference>();
        repository = new MongoNotificationPreferenceRepository(
            mockQueryOperation,
            mockUpdateOperation,
        );
        jest.useFakeTimers();
        jest.setSystemTime(new Date(mockDateString));
    });

    describe('findOne', () => {
        it('should return doc', async () => {
            const doc = mocks.getMockedDoc();
            const businessUnitId = new ObjectId();
            const accountId = new ObjectId();
            const id = new ObjectId();
            const mongoDoc = {
                ...doc,
                businessUnitId,
                accountId,
                _id: id,
            };

            mockQueryOperation.findOne.mockResolvedValue(mongoDoc);

            const result = await repository.findOne(
                businessUnitId.toString(), accountId.toString(), doc.email,
            );

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                {
                    businessUnitId,
                    accountId,
                    email: doc.email,
                },
                undefined,
            );
            expect(result).toEqual({
                ...mongoDoc,
                businessUnitId: businessUnitId.toString(),
                accountId: accountId.toString(),
                _id: id.toHexString(),
            });
        });
    });

    describe('findByBusinessUnitId', () => {
        it('should return doc', async () => {
            const doc = mocks.getMockedDoc();
            const businessUnitId = new ObjectId();
            const accountId = new ObjectId();
            const id = new ObjectId();
            const mongoDoc = {
                ...doc,
                businessUnitId,
                accountId,
                _id: id,
            };
            const pagination = {
                totalPages: 1,
                currentPage: 1,
                hasPreviousPage: false,
                hasNextPage: false,
                itemsPerPage: 1,
                totalItems: 1,
            };

            mockQueryOperation.find.mockResolvedValue(
                {
                    items: [ mongoDoc ],
                    pagination,
                });

            const result = await repository.findByBusinessUnitId(
                businessUnitId.toString(),
            );

            expect(mockQueryOperation.find).toHaveBeenCalledWith(
                { businessUnitId },
                undefined,
                undefined,
                undefined,
            );
            expect(result).toEqual({
                items: [ {
                    ...mongoDoc,
                    businessUnitId: businessUnitId.toString(),
                    accountId: accountId.toString(),
                    _id: id,
                } ],
                pagination,
            });
        });
    });

    describe('upsert', () => {
        it('should upsert doc', async () => {
            const doc = mocks.getMockedDoc();
            const businessUnitId = new ObjectId();
            const accountId = new ObjectId();
            const id = new ObjectId();
            const settings = {
                newDisputes: true,
                lostDisputes: true,
            };
            const mongoDoc = {
                ...doc,
                businessUnitId,
                accountId,
                _id: id,
            };

            mockUpdateOperation.upsert.mockResolvedValue(mongoDoc);

            const result = await repository.upsert(
                businessUnitId.toString(), accountId.toString(), doc.email, settings,
            );

            expect(mockUpdateOperation.upsert).toHaveBeenCalledWith(
                { businessUnitId, accountId, email: doc.email },
                {
                    $set: {
                        'settings.newDisputes': true,
                        'settings.lostDisputes': true,
                        dateUpdated: new Date(mockDateString),
                    },
                    $setOnInsert: { dateCreated: new Date(mockDateString) },
                },
                undefined,
            );
            expect(result).toEqual({
                ...mongoDoc,
                businessUnitId: businessUnitId.toString(),
                accountId: accountId.toString(),
                _id: id.toHexString(),
            });
        });
    });

});
