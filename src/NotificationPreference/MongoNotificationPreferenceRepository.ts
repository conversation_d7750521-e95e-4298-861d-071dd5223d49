import { ClientSession, ObjectId } from 'mongodb';
import {
    IMongoNotificationPreference,
    INotificationPreference,
    INotificationPreferenceSettings,
} from './INotificationPreference';
import {
    IFindByQueryOperation,
    IPaginatedFindByQueryResult,
} from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { INotificationPreferenceRepository } from './INotificationPreferenceRepository';
import { WithId } from '../shared/helper-types';

export class MongoNotificationPreferenceRepository implements INotificationPreferenceRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoNotificationPreference>,
        private readonly updateOperation: IUpdateOperation<IMongoNotificationPreference>,
    ) {}

    private mapDoc(doc: IMongoNotificationPreference): WithId<INotificationPreference> {
        return {
            ...doc,
            _id: doc._id.toString(),
            businessUnitId: doc.businessUnitId?.toString(),
            accountId: doc.accountId?.toString(),
        };
    }

    async findOne(businessUnitId: string, accountId: string, email: string, clientSession?: ClientSession | undefined): Promise<WithId<INotificationPreference>|null> {
        const doc = await this.findByQueryOperation.findOne(
            {
                businessUnitId: new ObjectId(businessUnitId),
                accountId: new ObjectId(accountId),
                email,
            },
            clientSession,
        );
        return doc ? this.mapDoc(doc) : null;
    }

    async findByBusinessUnitId(businessUnitId: string, page?: number, pageSize?: number, clientSession?: ClientSession | undefined): Promise<IPaginatedFindByQueryResult<INotificationPreference>> {
        const result = await this.findByQueryOperation.find(
            { businessUnitId: new ObjectId(businessUnitId) },
            page,
            pageSize,
            clientSession,
        );
        return { ...result, items: result.items.map(i => ({ ...this.mapDoc(i), _id: i._id })) };
    }

    async upsert(businessUnitId: string, accountId: string, email: string, settings: INotificationPreferenceSettings, clientSession?: ClientSession): Promise<WithId<INotificationPreference>|null> {
        const settingsUpdate = Object.fromEntries(
            Object.entries(settings).map(([ k,v ]) => ([ 'settings.'+k, v ])),
        );
        const doc = await this.updateOperation.upsert(
            {
                businessUnitId: new ObjectId(businessUnitId),
                accountId: new ObjectId(accountId),
                email,
            },
            {
                $set: {
                    ...settingsUpdate,
                    dateUpdated: new Date(),
                },
                $setOnInsert: {
                    dateCreated: new Date(),
                },
            },
            clientSession,
        );
        return doc ? this.mapDoc(doc) : null;
    }

    async findByPreferenceSettings(preferenceSettings: string[], settingValue: boolean = true, page?: number, pageSize?: number, clientSession?: ClientSession | undefined):
        Promise<IPaginatedFindByQueryResult<INotificationPreference>> {
        const settingsFilter = preferenceSettings.map(settings => {
            return { ['settings.' + settings]: settingValue };
        });
        const result = await this.findByQueryOperation.find(
            { $or: settingsFilter },
            page,
            pageSize,
            clientSession,
        );
        return { ...result, items: result.items.map(i => ({ ...this.mapDoc(i), _id: i._id })) };
    }
}
