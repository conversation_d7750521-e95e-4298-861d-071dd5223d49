import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { NotificationPreferenceRepositoryFactory } from './NotificationPreferenceRepositoryFactory';
import { MongoNotificationPreferenceRepository } from './MongoNotificationPreferenceRepository';

describe(NotificationPreferenceRepositoryFactory.name, () => {
    describe(NotificationPreferenceRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = NotificationPreferenceRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoNotificationPreferenceRepository);
        });
    });
});
