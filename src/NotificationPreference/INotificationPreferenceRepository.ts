import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { INotificationPreference, INotificationPreferenceSettings } from './INotificationPreference';
import {
    IPaginatedFindByQueryResult,
} from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';

export interface INotificationPreferenceRepository {
    findOne(businessUnitId: string, accountId: string, email: string, clientSession?: ClientSession): Promise<WithId<INotificationPreference>|null>
    findByBusinessUnitId(businessUnitId: string, page?: number, pageSize?: number, clientSession?: ClientSession): Promise<IPaginatedFindByQueryResult<INotificationPreference>>
    upsert(businessUnitId: string, accountId: string, email: string, settings: INotificationPreferenceSettings, clientSession?: ClientSession): Promise<WithId<INotificationPreference>|null>
    findByPreferenceSettings(preferenceSettings: string[], settingValue: boolean, page?: number, pageSize?: number, clientSession?: ClientSession | undefined):
        Promise<IPaginatedFindByQueryResult<INotificationPreference>>
}
