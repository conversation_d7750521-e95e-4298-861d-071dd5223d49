import { ClientSession, ObjectId } from 'mongodb';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { OrderSource } from '../shared/enums';
import { WithId } from '../shared/helper-types';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IOrderPlatformRepository } from './IOrderPlatformRepository';
import {
    IMongoOrderPlatform,
    IOrderPlatform,
    IOrderPlatformConcrete,
    IOrderPlatformConfig,
    IOrderPlatformStatus,
    OrderPlatformStatusEnum,
} from './Types';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { MongoOrderPlatformMapper } from './MongoOrderPlatformMapper';
import { IMongoShop } from '../Shop/IShop';

export class MongoOrderPlatformRepository implements IOrderPlatformRepository {
    constructor(
        private readonly getByIdOperation: IGetByIdOperation<IMongoOrderPlatform>,
        private readonly findPlatformsByQueryOperation: IFindByQueryOperation<IMongoOrderPlatform>,
        private readonly updateOperation: IUpdateOperation<IMongoOrderPlatform>,
        private readonly addOperation: IAddOperation<IMongoOrderPlatform>,
        private readonly findShopsByQueryOperation: IFindByQueryOperation<IMongoShop>,
    ) {
    }

    async getConcreteByOrderPlatformId<T extends IOrderPlatformConfig>(id: string, session?: ClientSession): Promise<WithId<IOrderPlatformConcrete<T>> | null> {
        const result = await this.getByIdOperation.get(new ObjectId(id), session);

        return result === null ? null : MongoOrderPlatformMapper.fromMongoOrderPlatformConcrete<T>(result);
    }

    async getByOrderPlatformId(id: string, session?: ClientSession | undefined) : Promise<WithId<IOrderPlatform> | null> {
        const result = await this.getByIdOperation.get(new ObjectId(id), session);

        return result === null ? null : MongoOrderPlatformMapper.fromMongoOrderPlatform(result);
    }

    async getByChargeflowIdAndOrderPlatformId<T extends IOrderPlatformConfig>(
        chargeflowId: string, orderPlatformId: string, session?: ClientSession,
    ) : Promise<WithId<IOrderPlatformConcrete<T>> | null> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }
        if (!orderPlatformId) {
            throw new Error('Order platform id is required');
        }

        const page = 1;
        const pageSize = 1;

        const result = await this.findPlatformsByQueryOperation.find({ '_id': new ObjectId(orderPlatformId), chargeflowId: new ObjectId(chargeflowId) }, page, pageSize, session);

        return MongoOrderPlatformMapper.fromMongoOrderPlatformsConcrete<T>(result.items)[0] ?? null;
    }

    async getByChargeflowIdAndSource<T extends IOrderPlatformConfig>(
        chargeflowId: string, orderSource: OrderSource, session?: ClientSession,
    ): Promise<WithId<IOrderPlatformConcrete<T>> | null> {
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const page = 1;
        const pageSize = 1;

        const result = await this.findPlatformsByQueryOperation.find({ chargeflowId: new ObjectId(chargeflowId), orderSource }, page, pageSize, session);

        return MongoOrderPlatformMapper.fromMongoOrderPlatformsConcrete<T>(result.items)[0] ?? null;
    }

    async getManyByChargeflowId(
        chargeflowId: string, session?: ClientSession, page: number = 1, pageSize: number = 10,
    ): Promise<{
        items: WithId<IOrderPlatform>[];
        pagination?: IPaginationCalculatorResult
    }> {
        console.log(`getManyByChargeflowId: ${chargeflowId}`);
        if (!chargeflowId) {
            throw new Error('Chargeflow id is required');
        }

        const result = await this.findPlatformsByQueryOperation.find({ chargeflowId: new ObjectId(chargeflowId) }, page, pageSize, session);
        console.log('order platforms query %j', result);

        if (result.items.findIndex(op => op.orderSource === 'shopify') === -1) {
            const filter = { chargeflow_id: new ObjectId(chargeflowId), platform: 'shopify' };
            console.log('filter', filter);
            const shopResult = await this.findShopsByQueryOperation.find(filter, page, pageSize, session);
            console.log('shopResult %j', shopResult);

            if (shopResult.items.length !== 0) {
                const shop = shopResult.items[0];
                console.log('shop found %j', shop);

                result.items.push({
                    orderSource: 'shopify',
                    chargeflowId: shop.chargeflow_id!,
                    _id: shop._id,
                    status: 'Connected',
                    isKeyValid: true,
                    integrationData: {
                        config: shop.connect,
                    },
                    hasWooPaymentsProcessor: false,
                });
            }
        }

        const response = { items: MongoOrderPlatformMapper.fromMongoOrderPlatforms(result.items), pagination: result.pagination };
        console.log('response %j', response);
        return response;
    }

    async insert<T extends IOrderPlatformConfig>(orderPlatform: IOrderPlatformConcrete<T>, session?: ClientSession): Promise<WithId<IOrderPlatformConcrete<T>> | null> {
        const result = await this.addOperation.add(MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform), session);

        return MongoOrderPlatformMapper.fromMongoOrderPlatformConcrete<T>(result);
    }

    async updateKeyValidity(id: string, isKeyValid: boolean, session?: ClientSession): Promise<WithId<IOrderPlatform> | null> {
        const result = await this.updateOperation.updateWithFilter({
            _id: new ObjectId(id),
        },
        {
            $set: {
                isKeyValid,
            },
        }, session);

        return MongoOrderPlatformMapper.fromMongoOrderPlatform(result) ?? null;
    }

    async updateStatus(id: string, newStatus: IOrderPlatformStatus, session?: ClientSession): Promise<WithId<IOrderPlatform> | null> {
        const result = await this.updateOperation.updateWithFilter({
            _id: new ObjectId(id),
        },
        {
            $set: {
                status: newStatus,
            },
        }, session);

        return MongoOrderPlatformMapper.fromMongoOrderPlatform(result) ?? null;
    }

    async getConnectedBySource<T extends IOrderPlatformConfig>(
        orderSource: OrderSource, page: number = 1, pageSize: number = 10, session?: ClientSession,
    ): Promise<{
        items: WithId<IOrderPlatformConcrete<T>>[];
        pagination?: IPaginationCalculatorResult
    }> {
        const result = await this.findPlatformsByQueryOperation.find({
            status: OrderPlatformStatusEnum.Connected,
            orderSource,
        }, page, pageSize, session);

        return { items: MongoOrderPlatformMapper.fromMongoOrderPlatformsConcrete<T>(result.items), pagination: result.pagination };
    }

    async updateProcessorFlag(
        id: string,
        hasWooPaymentsProcessor: boolean,
        session?: ClientSession,
    ): Promise<WithId<IOrderPlatform> | null> {
        const result = await this.updateOperation.updateWithFilter(
            { _id: new ObjectId(id) },
            { $set: { hasWooPaymentsProcessor } },
            session,
        );

        return MongoOrderPlatformMapper.fromMongoOrderPlatform(result) ?? null;
    }

    async findAllWithoutProcessor<T extends IOrderPlatformConfig>(
        source: OrderSource,
        page: number = 1,
        pageSize: number = 10,
        session?: ClientSession,
    ): Promise<{ items: WithId<IOrderPlatformConcrete<T>>[]; pagination?: IPaginationCalculatorResult }> {
        const result = await this.findPlatformsByQueryOperation.find(
            {
                orderSource: source,
                $or: [ { hasWooPaymentsProcessor: false }, { hasWooPaymentsProcessor: { $exists: false } } ],
            },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoOrderPlatformMapper.fromMongoOrderPlatformsConcrete<T>(result.items),
            pagination: result.pagination,
        };
    }
}
