import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IOrderPlatform, IOrderPlatformConcrete, IOrderPlatformConfig, IOrderPlatformStatus } from './Types';
import { OrderSource } from '../shared/enums';

export interface IOrderPlatformRepository {
    getConcreteByOrderPlatformId: <T extends IOrderPlatformConfig>(
        id: string, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatformConcrete<T>> | null>,

    getByOrderPlatformId: (
        id: string, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatform> | null>,

    getByChargeflowIdAndOrderPlatformId: <T extends IOrderPlatformConfig>(
        chargeflowId: string, orderPlatformId: string, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatformConcrete<T>> | null>,

    getByChargeflowIdAndSource: <T extends IOrderPlatformConfig>(
        chargeflowId: string, orderSource: OrderSource, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatformConcrete<T>> | null>,

    getManyByChargeflowId: (
        chargeflowId: string, session?: ClientSession, page?: number, pageSize?: number
    ) =>  Promise<{items: WithId<IOrderPlatform>[], pagination?: IPaginationCalculatorResult}>,

    insert: <T extends IOrderPlatformConfig>(
        orderPlatform: IOrderPlatformConcrete<T>, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatformConcrete<T>> | null>,

    updateStatus: (
        id: string, newStatus: IOrderPlatformStatus, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatform> | null>,

    updateKeyValidity: (
        id: string, isKeyValid: boolean, session?: ClientSession,
    ) => Promise<WithId<IOrderPlatform> | null>,

    getConnectedBySource: <T extends IOrderPlatformConfig>(
        orderSource: OrderSource, page?: number, pageSize?: number, session?: ClientSession,
    ) =>  Promise<{items: WithId<IOrderPlatformConcrete<T>>[], pagination?: IPaginationCalculatorResult}>,

    findAllWithoutProcessor: <T extends IOrderPlatformConfig>(
        source: OrderSource,
        page: number,
        pageSize: number,
        session?: ClientSession,
    ) => Promise<{ items: WithId<IOrderPlatformConcrete<T>>[]; pagination?: IPaginationCalculatorResult }>,

    updateProcessorFlag: (
        id: string,
        hasWooPaymentsProcessor: boolean,
        session?: ClientSession,
    ) => Promise<WithId<IOrderPlatform> | null>,
}
