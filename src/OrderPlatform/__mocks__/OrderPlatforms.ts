import { ObjectId } from 'mongodb';
import { OrderSourceEnum } from '../../shared/enums';
import { faker } from '@faker-js/faker';
import { IOrderPlatformConcrete, IWooCommerceOrdersConfig, OrderPlatformStatusEnum } from '../Types';

const getWooCommercePlatform = (overrides: Partial<IOrderPlatformConcrete<IWooCommerceOrdersConfig>> = {}): IOrderPlatformConcrete<IWooCommerceOrdersConfig> => ({
    chargeflowId: new ObjectId().toHexString(),
    _id: new ObjectId().toHexString(),
    status: OrderPlatformStatusEnum.Connected,
    isKeyValid: true,
    orderSource: OrderSourceEnum.wooCommerce,
    integrationData: {
        config: {
            consumer_key: faker.string.alphanumeric(),
            consumer_secret: faker.string.alphanumeric(),
            site_url: faker.internet.url(),
        },
    },
    hasWooPaymentsProcessor: false,
    ...overrides,
});

export default {
    getWooCommercePlatform,
};
