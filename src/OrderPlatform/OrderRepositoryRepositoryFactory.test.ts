import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { OrderPlatformRepositoryFactory } from './OrderPlatformRepositoryFactory';
import { MongoOrderPlatformRepository } from './MongoOrderPlatformRepository';

describe(OrderPlatformRepositoryFactory.name, () => {

    beforeAll(() => {
        process.env.STACK_NAME = faker.word.noun();
    });

    describe(OrderPlatformRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mockMongoClient = {} as MongoClient;
            const dbName = faker.word.noun();
            const orderPlatformCollectionName = faker.word.noun();
            const shopCollectionName = faker.word.noun();

            // WHEN
            const result = OrderPlatformRepositoryFactory.create(mockMongoClient, dbName, orderPlatformCollectionName, shopCollectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoOrderPlatformRepository);
        });
    });
});
