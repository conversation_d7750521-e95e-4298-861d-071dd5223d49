import { MongoClient } from 'mongodb';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';
import { IMongoOrderPlatform } from './Types';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoOrderPlatformRepository } from './MongoOrderPlatformRepository';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { IMongoShop } from '../Shop/IShop';

export class OrderPlatformRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, orderPlatformCollectionName: string, shopCollectionName: string): MongoOrderPlatformRepository {
        const getByIdOperation = new MongoGetByIdOperation<IMongoOrderPlatform>(mongoClient, dbName, orderPlatformCollectionName);
        const findOrderPlatformsByQueryOperation = new MongoFindByQueryOperation<IMongoOrderPlatform>(mongoClient, dbName, orderPlatformCollectionName);
        const updateOperation = new MongoUpdateOperation<IMongoOrderPlatform>(mongoClient, dbName, orderPlatformCollectionName);
        const addOperation = new MongoAddOperation<IMongoOrderPlatform>(mongoClient, dbName, orderPlatformCollectionName);

        const findShopsByQueryOperation = new MongoFindByQueryOperation<IMongoShop>(mongoClient, dbName, shopCollectionName);

        return new MongoOrderPlatformRepository(
            getByIdOperation,
            findOrderPlatformsByQueryOperation,
            updateOperation,
            addOperation,
            findShopsByQueryOperation,
        );
    }
}
