import { orderSourceSchema } from '../shared/enums';
import { ObjectId, Document } from 'mongodb';
import { z } from 'zod';

export const orderPlatformStatusSchema = z.enum([
    'Connected',
]);

export const wooCommerceOrdersConfigSchema = z.object({
    consumer_key: z.string(),
    consumer_secret: z.string(),
    site_url: z.string(),
});

export const orderPlatformConfigSchema = z.union( [ wooCommerceOrdersConfigSchema, z.unknown() ]);

export const orderPlatformBaseSchema = z.object({
    orderSource: orderSourceSchema,
    status: orderPlatformStatusSchema,
    isKeyValid: z.boolean(),
    integrationData: z.object({
        config: orderPlatformConfigSchema,
    }),
    hasWooPaymentsProcessor: z.boolean().optional().default(false),
});

export const orderPlatformSchema = orderPlatformBaseSchema.extend({
    _id: z.string().optional(),
    chargeflowId: z.string(),
});

export const mongoOrderPlatformSchema = orderPlatformBaseSchema.extend({
    _id: z.instanceof(ObjectId).optional(),
    chargeflowId: z.instanceof(ObjectId),
});

export type IOrderPlatformConfig = z.infer<typeof orderPlatformConfigSchema>;

export type IOrderPlatformBase = z.infer<typeof orderPlatformBaseSchema>;
export type IWooCommerceOrdersConfig = z.infer<typeof wooCommerceOrdersConfigSchema>;

export type IOrderPlatformStatus = z.infer<typeof orderPlatformStatusSchema>;
export const OrderPlatformStatusEnum = orderPlatformStatusSchema.enum;

export type IOrderPlatform = z.infer<typeof orderPlatformSchema>;
export type IMongoOrderPlatform = z.infer<typeof mongoOrderPlatformSchema> & Document;

export type IOrderPlatformConcrete<T extends IOrderPlatformConfig> = IOrderPlatform & {
    integrationData: {
        config: T
    }
}
