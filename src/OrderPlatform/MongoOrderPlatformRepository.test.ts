import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import {
    IFindByQueryOperation,
    IPaginatedFindByQueryResult,
} from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { OrderSourceEnum } from '../shared/enums';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import mock from 'jest-mock-extended/lib/Mock';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import updateOperationMocks from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';
import addOperationMocks from '../shared/operation/AddOperation/__mocks__/MongoAddOperation';
import orderPlatformMocks from '../OrderPlatform/__mocks__/OrderPlatforms';
import shopMocks from '../Shop/__mocks__/Shop';
import { MongoOrderPlatformRepository } from './MongoOrderPlatformRepository';
import {
    IMongoOrderPlatform,
    IOrderPlatform,
    IOrderPlatformConcrete,
    IWooCommerceOrdersConfig,
    OrderPlatformStatusEnum,
} from './Types';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { MongoOrderPlatformMapper } from './MongoOrderPlatformMapper';
import { IMongoShop } from '../Shop/IShop';
import { MongoShopMapper } from '../Shop/MongoShopMapper';

describe(MongoOrderPlatformRepository.name, () => {
    let mockGetByIdOperation: jest.Mocked<IGetByIdOperation<IMongoOrderPlatform>>;
    let mockFindByQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoOrderPlatform>>;
    let mockFindShopsByQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoShop>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoOrderPlatform>>;
    let mockAddOperation: jest.Mocked<IAddOperation<IMongoOrderPlatform>>;
    let repository: MongoOrderPlatformRepository;
    const mockDateString = '2024-05-30T14:00:00Z';

    beforeEach(() => {
        mockGetByIdOperation = {
            get: jest.fn(),
        } as jest.Mocked<IGetByIdOperation<IMongoOrderPlatform>>;
        mockFindByQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoOrderPlatform>();
        mockFindShopsByQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoShop>();
        mockUpdateOperation = updateOperationMocks.getUpdateOperation<IMongoOrderPlatform>();
        mockAddOperation = addOperationMocks.getAddOperation<IMongoOrderPlatform>();
        jest.useFakeTimers();
        jest.setSystemTime(new Date(mockDateString));

        repository = new MongoOrderPlatformRepository(
            mockGetByIdOperation,
            mockFindByQueryOperation,
            mockUpdateOperation,
            mockAddOperation,
            mockFindShopsByQueryOperation,
        );
    });

    describe('getConcreteByOrderPlatformId', () => {
        it('should find an order platform by id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
            });

            const getByIdResult: WithId<IMongoOrderPlatform> = {
                ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                _id: id,
            };
            const expectedResult: IOrderPlatformConcrete<IWooCommerceOrdersConfig> = {
                ...orderPlatform,
            };

            mockGetByIdOperation.get.mockResolvedValue(getByIdResult);

            // WHEN
            const result = await repository.getConcreteByOrderPlatformId(id.toHexString());

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(
                id, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByOrderPlatformId', () => {
        it('should find an order platform by id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
            });

            const getByIdResult: WithId<IMongoOrderPlatform> = {
                ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                _id: id,
            };
            const expectedResult: IOrderPlatform = {
                ...orderPlatform,
            };

            mockGetByIdOperation.get.mockResolvedValue(getByIdResult);

            // WHEN
            const result = await repository.getByOrderPlatformId(id.toHexString());

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(
                id, undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByChargeflowIdAndOrderPlatformId', () => {
        it('should find an order platform by chargeflow id and order platform id and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoOrderPlatform> = {
                items: [
                    {
                        ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                        _id: id,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByChargeflowIdAndOrderPlatformId(chargeflowId.toHexString(), id.toHexString());

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { _id: id, chargeflowId: chargeflowId }, expect.anything(), expect.anything(), undefined,
            );
            expect(result).toEqual(orderPlatform);
        });
    });

    describe('getByChargeflowIdAndSource', () => {
        it('should find an order platform by chargeflow id and processor name and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const processor = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoOrderPlatform> = {
                items: [
                    {
                        ...MongoOrderPlatformMapper.toMongoOrderPlatform(processor),
                        _id: id,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByChargeflowIdAndSource(chargeflowId.toHexString(), OrderSourceEnum.wooCommerce);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { chargeflowId: chargeflowId, orderSource: OrderSourceEnum.wooCommerce }, expect.anything(), expect.anything(), undefined,
            );
            expect(result).toEqual(processor);
        });
    });

    describe('insert', () => {
        it('should insert an order platform', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: undefined,
                chargeflowId: chargeflowId.toHexString(),
            });

            const addResult: WithId<IMongoOrderPlatform> = {
                ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                _id: id,
            };

            mockAddOperation.add.mockResolvedValue(addResult);

            // WHEN
            const result = await repository.insert(orderPlatform);

            // THEN
            expect(mockAddOperation.add).toHaveBeenCalledWith(
                MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform), undefined,
            );
            expect(result).toEqual(expect.objectContaining({
                _id: id.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            }));
        });
    });

    describe('updateStatus', () => {
        it('should update status', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
                status: 'Connected',
            });

            const updateResult: WithId<IMongoOrderPlatform> = {
                ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateStatus(id.toHexString(), OrderPlatformStatusEnum.Connected);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id }, {
                    $set: { status: OrderPlatformStatusEnum.Connected },
                }, undefined,
            );
            expect(result).toEqual(orderPlatform);
        });
    });

    describe('updateKeyValidity', () => {
        it('should update key validity field', async () => {
            // GIVEN
            const id = new ObjectId();
            const chargeflowId = new ObjectId();
            const orderPlatform = orderPlatformMocks.getWooCommercePlatform({
                _id: id.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
                isKeyValid: true,
            });

            const updateResult: WithId<IMongoOrderPlatform> = {
                ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform),
                _id: id,
            };

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateKeyValidity(id.toHexString(), false);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id }, {
                    $set: { isKeyValid: false },
                }, undefined,
            );
            expect(result).toEqual(orderPlatform);
        });
    });

    describe('getManyByChargeflowId', () => {
        it('should return a list of order platforms by chargeflow id and return the result', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const session = mock<ClientSession>();
            const orderPlatform1 = orderPlatformMocks.getWooCommercePlatform({
                _id: id1.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const orderPlatform2 = orderPlatformMocks.getWooCommercePlatform({
                _id: id2.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult = {
                items: [ {
                    ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform1),
                    _id: id1,
                },
                {
                    ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const findShopsByQueryResult = {
                items: [],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 0,
                },
            } as IPaginatedFindByQueryResult<IMongoShop>;

            mockFindShopsByQueryOperation.find.mockResolvedValue(findShopsByQueryResult);

            const expectedResult: {items: WithId<IOrderPlatform>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...orderPlatform1,
                    _id: id1.toHexString(),

                },
                {
                    ...orderPlatform2,
                    _id: id2.toHexString(),
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };

            // WHEN
            const result = await repository.getManyByChargeflowId(chargeflowId.toHexString(), session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'chargeflowId': chargeflowId,
                },
                1,
                10,
                session,
            );
            expect(result).toEqual(expectedResult);
        });

        it('should include shopify shop information as an order platform', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const session = mock<ClientSession>();
            const orderPlatform1 = orderPlatformMocks.getWooCommercePlatform({
                _id: id1.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const shop = shopMocks.getShop({
                _id: id2.toHexString(),
                chargeflow_id: chargeflowId.toHexString(),
                platform: 'shopify',
                connect: {
                    access_token: 'test',
                    shop_name: 'test',
                    scope: 'test',
                },
            });

            const findByQueryResult = {
                items: [ {
                    ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform1),
                    _id: id1,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const findShopsByQueryResult = {
                items: [
                    {
                        ...MongoShopMapper.toMongoShop(shop),
                        _id: id2,
                    },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            } as IPaginatedFindByQueryResult<IMongoShop>;

            mockFindShopsByQueryOperation.find.mockResolvedValue(findShopsByQueryResult);

            const expectedResult: {items: WithId<IOrderPlatform>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...orderPlatform1,
                    _id: id1.toHexString(),
                }, expect.objectContaining({
                    _id: id2.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                    status: 'Connected',
                    isKeyValid: true,
                    orderSource: 'shopify',
                } as IOrderPlatform),
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };

            // WHEN
            const result = await repository.getManyByChargeflowId(chargeflowId.toHexString(), session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'chargeflowId': chargeflowId,
                },
                1,
                10,
                session,
            );
            expect(result).toEqual(expectedResult);
        });

        it('should throw error if chargeflowId is not passed', async () => {

            const session = mock<ClientSession>();
            try {
                await repository.getManyByChargeflowId('', session);
            } catch (e) {
                expect((e as unknown as Error).message).toBe('Chargeflow id is required');
            }
        });
    });

    describe('getConnectedBySource', () => {
        it('should return a list connected order platforms from specified source', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const orderPlatform1 = orderPlatformMocks.getWooCommercePlatform({
                _id: id1.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const orderPlatform2 = orderPlatformMocks.getWooCommercePlatform({
                _id: id2.toHexString(),
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult = {
                items: [ {
                    ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform1),
                    _id: id1,
                },
                {
                    ...MongoOrderPlatformMapper.toMongoOrderPlatform(orderPlatform2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 2,
                    totalItems: 2,
                },
            };
            const expectedResult: {items: WithId<IOrderPlatform>[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...orderPlatform1,
                    _id: id1.toHexString(),

                },
                {
                    ...orderPlatform2,
                    _id: id2.toHexString(),
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 2,
                    totalItems: 2,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getConnectedBySource(OrderSourceEnum.wooCommerce);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'orderSource': OrderSourceEnum.wooCommerce,
                    'status': OrderPlatformStatusEnum.Connected,
                },
                1,
                10,
                undefined,
            );
            expect(result).toEqual(expectedResult);
        });
    });
});
