import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoOrderPlatform, IOrderPlatform, IOrderPlatformConcrete, IOrderPlatformConfig } from './Types';

export class MongoOrderPlatformMapper {
    static toMongoOrderPlatform(orderPlatform: IOrderPlatform): IMongoOrderPlatform {
        if (!orderPlatform) {
            throw new Error('Order platform is required for mapping');
        }

        return {
            ...orderPlatform,
            _id: orderPlatform._id ? new ObjectId(orderPlatform._id) : undefined,
            chargeflowId: new ObjectId(orderPlatform.chargeflowId),
        };
    }

    static toMongoOrderPlatforms(orderPlatforms: IOrderPlatform[]): IMongoOrderPlatform[] {
        return orderPlatforms.map(MongoOrderPlatformMapper.toMongoOrderPlatform);
    }

    static fromMongoOrderPlatformConcrete<T extends IOrderPlatformConfig>(orderPlatform: MongoWithId<IMongoOrderPlatform>): WithId<IOrderPlatformConcrete<T>> {
        if (!orderPlatform) {
            throw new Error('Order platform is required for mapping');
        }

        return {
            ...orderPlatform,
            _id: orderPlatform._id.toString(),
            chargeflowId: orderPlatform.chargeflowId?.toString(),
            integrationData: {
                ...orderPlatform.integrationData,
                config: orderPlatform.integrationData.config as T,
            },
        };
    }

    static fromMongoOrderPlatform(orderPlatform: MongoWithId<IMongoOrderPlatform>): WithId<IOrderPlatform> {
        return MongoOrderPlatformMapper.fromMongoOrderPlatformConcrete(orderPlatform);
    }

    static fromMongoOrderPlatforms(orderPlatforms: MongoWithId<IMongoOrderPlatform>[]): WithId<IOrderPlatform>[] {
        return orderPlatforms.map(MongoOrderPlatformMapper.fromMongoOrderPlatform);
    }

    static fromMongoOrderPlatformsConcrete<T extends IOrderPlatformConfig>(orderPlatforms: MongoWithId<IMongoOrderPlatform>[]): WithId<IOrderPlatformConcrete<T>>[] {
        return orderPlatforms.map(MongoOrderPlatformMapper.fromMongoOrderPlatformConcrete<T>);
    }
}
