import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { ActionRepositoryFactory } from './ActionRepositoryFactory';
import { MongoActionRepository } from './MongoActionRepository';

describe(ActionRepositoryFactory.name, () => {
    describe(ActionRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = ActionRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoActionRepository);
        });
    });
});
