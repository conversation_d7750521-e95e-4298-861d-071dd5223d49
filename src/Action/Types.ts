import { ObjectId, Document } from 'mongodb';

export interface IActionBase {
    caseId: string,
    shopName: string,
    processor: string,
    actor: string,
    actorRole: string,
    actionType: string,
    dateCreated: Date,
    dateUpdated?: Date,
}

export interface IAction extends IActionBase {
    _id?: string,
    chargeflowId?: string,
    disputeId?: string,
}

export interface IMongoAction extends IActionBase, Document {
    _id?: ObjectId,
    chargeflowId?: ObjectId,
    disputeId?: ObjectId,
}
