import { MongoClient } from 'mongodb';
import { IActionRepository } from './IActionRepository';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { IMongoAction } from './Types';
import { MongoActionRepository } from './MongoActionRepository';
import { MongoBulkAddOperation } from '../shared/operation/BulkAddOperation/MongoBulkAddOperation';

export class ActionRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IActionRepository {
        const addOperation = new MongoAddOperation<IMongoAction>(mongoClient, dbName, collectionName);
        const bulkAddOperation = new MongoBulkAddOperation<IMongoAction>(mongoClient, dbName, collectionName);

        return new MongoActionRepository(addOperation, bulkAddOperation);
    }
}
