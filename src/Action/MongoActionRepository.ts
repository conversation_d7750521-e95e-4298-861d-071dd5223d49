import { ClientSession } from 'mongodb';
import { IActionRepository } from './IActionRepository';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IAction, IMongoAction } from './Types';
import { MongoActionMapper } from './MongoActionMapper';
import { WithId } from '../shared/helper-types';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';

export class MongoActionRepository implements IActionRepository {
    constructor(
        private readonly addOperation: IAddOperation<IMongoAction>,
        private readonly bulkAddOperation: IBulkAddOperation<IMongoAction>,
    ) {}

    async add(
        action: IAction, session?: ClientSession,
    ): Promise<WithId<IAction>> {
        return MongoActionMapper.fromMongoAction(
            await this.addOperation.add(MongoActionMapper.toMongoAction(action), session),
        );
    }

    async bulkAdd(
        action: IAction[], session?: ClientSession,
    ): Promise<WithId<IAction>[]> {
        return MongoActionMapper.fromMongoActions(
            await this.bulkAddOperation.bulkAdd(MongoActionMapper.toMongoActions(action), session),
        );
    }
}
