import { ObjectId } from 'mongodb';
import { IAction } from '../Types';
import { faker } from '@faker-js/faker';
import { Source } from '../../shared/enums';

const getAction = (overrides?: Partial<IAction>): IAction => ({
    _id: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    disputeId: new ObjectId().toHexString(),
    caseId: faker.internet.email(),
    shopName: faker.word.words(3),
    dateCreated: faker.date.past(),
    dateUpdated: new Date(),
    actor: faker.internet.email(),
    actorRole: faker.word.noun(),
    processor: faker.helpers.enumValue(Source),
    actionType: faker.word.noun(),
    ...overrides,
});

const defaultAction = getAction();

export default {
    getAction,
    defaultAction,
};
