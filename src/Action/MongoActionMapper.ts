import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { IAction, IMongoAction } from './Types';
import { WithId } from '../shared/helper-types';

export class MongoActionMapper {
    static toMongoAction(action: IAction): IMongoAction {
        if (!action) {
            throw new Error('Action is required for mapping');
        }

        return {
            ...action,
            _id: action._id ? new ObjectId(action._id) : undefined,
            disputeId: action.disputeId ? new ObjectId(action.disputeId) : undefined,
            chargeflowId: action.chargeflowId ? new ObjectId(action.chargeflowId) : undefined,
        };
    }

    static toMongoActions(actions: IAction[]): IMongoAction[] {
        return actions.map(MongoActionMapper.toMongoAction);
    }

    static fromMongoAction(action: MongoWithId<IMongoAction>): WithId<IAction> {
        if (!action) {
            throw new Error('Action is required for mapping');
        }

        return {
            ...action,
            _id: action._id?.toHexString(),
            disputeId: action.disputeId?.toHexString(),
            chargeflowId: action.chargeflowId?.toHexString(),
        };
    }

    static fromMongoActions(actions: MongoWithId<IMongoAction>[]): WithId<IAction>[] {
        return actions.map(MongoActionMapper.fromMongoAction);
    }
}
