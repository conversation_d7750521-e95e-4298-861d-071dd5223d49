import { ObjectId, WithId } from 'mongodb';
import { IMongoAction } from './Types';
import { MongoActionRepository } from './MongoActionRepository';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import actionMocks from './__mocks__/Actions';
import { MongoActionMapper } from './MongoActionMapper';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';

describe(MongoActionRepository.name, () => {
    let mockAddOperation: jest.Mocked<IAddOperation<IMongoAction>>;
    let mockAddBulkOperation: jest.Mocked<IBulkAddOperation<IMongoAction>>;
    let repository: MongoActionRepository;

    beforeEach(() => {
        mockAddOperation = {
            add: jest.fn(),
        } as jest.Mocked<IAddOperation<IMongoAction>>;

        mockAddBulkOperation = {
            bulkAdd: jest.fn(),
        } as jest.Mocked<IBulkAddOperation<IMongoAction>>;

        repository = new MongoActionRepository(
            mockAddOperation,
            mockAddBulkOperation,
        );
    });

    describe('add', () => {
        it('should add an action by id and return created action', async () => {
            // GIVEN
            const id = new ObjectId();
            const action = actionMocks.getAction();
            const mongoAction = MongoActionMapper.toMongoAction(action);

            const addResult: WithId<IMongoAction> = {
                ...mongoAction,
                _id: id,
            };

            mockAddOperation.add.mockResolvedValue(addResult);

            // WHEN
            const result = await repository.add(action);

            // THEN
            expect(mockAddOperation.add).toHaveBeenCalledWith(
                mongoAction, undefined,
            );
            expect(result).toEqual({
                ...action,
                _id: id.toHexString(),
            });
        });
    });

    describe('bulkAdd', () => {
        it('should add multiple actions by id and return created action', async () => {
            // GIVEN
            const id = new ObjectId();
            const id2 = new ObjectId();
            const action = actionMocks.getAction();
            const action2 = actionMocks.getAction();
            const mongoAction = MongoActionMapper.toMongoAction(action);
            const mongoAction2 = MongoActionMapper.toMongoAction(action2);

            const addResult: WithId<IMongoAction>[] = [ {
                ...mongoAction,
                _id: id,
            },
            {
                ...mongoAction2,
                _id: id2,
            } ];

            mockAddBulkOperation.bulkAdd.mockResolvedValue(addResult);

            // WHEN
            const result = await repository.bulkAdd([ action, action2 ]);

            // THEN
            expect(mockAddBulkOperation.bulkAdd).toHaveBeenCalledWith(
                [ mongoAction, mongoAction2 ], undefined,
            );
            expect(result).toEqual([ {
                ...action,
                _id: id.toHexString(),
            }, {
                ...action2,
                _id: id2.toHexString(),
            } ]);
        });
    });
});
