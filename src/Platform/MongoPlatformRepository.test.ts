import { mock } from 'jest-mock-extended';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { MongoPlatformRepository } from './MongoPlatformRepository';
import { IMongoPlatform } from './IPlatform';
import platformMocks from './__mocks__/Platform';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import addOperationMocks from '../shared/operation/AddOperation/__mocks__/MongoAddOperation';
import { convertStringToObjectId } from '../shared/helper-functions';

describe(MongoPlatformRepository.name, () => {
    let mongoPlatformRepository: MongoPlatformRepository;
    const updateOperation = mock<IUpdateOperation<IMongoPlatform>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoPlatform>>();
    const getByIdOperation = mock<IGetByIdOperation<IMongoPlatform>>();
    const mockAddOperation = addOperationMocks.getAddOperation<IMongoPlatform>();

    beforeEach(() => {
        mongoPlatformRepository = new MongoPlatformRepository(
            findByQueryOperation,
            getByIdOperation,
            updateOperation,
            mockAddOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoPlatformRepository.prototype.getById.name, () => {
        it('returns a single platform, if found', async () => {
            expect.assertions(3);

            const platform = platformMocks.getPlatform();
            const session = mock<ClientSession>();

            const getByIdResult = convertStringToObjectId(platform) as WithId<IMongoPlatform>;

            getByIdOperation.get.mockResolvedValue(getByIdResult);

            const result = await mongoPlatformRepository.getById(platform._id, session);

            expect(result)
                .toEqual(platform);
            expect(getByIdOperation.get)
                .toHaveBeenCalledTimes(1);
            expect(getByIdOperation.get)
                .toHaveBeenCalledWith(new ObjectId(platform._id), session);
        });

        it('returns null, if platform is not found', async () => {
            expect.assertions(3);

            const session = mock<ClientSession>();
            getByIdOperation.get.mockResolvedValue(null);

            const result = await mongoPlatformRepository.getById(ObjectId.createFromTime(1)
                .toHexString(), session);

            expect(result)
                .toBeNull();
            expect(getByIdOperation.get)
                .toHaveBeenCalledTimes(1);
            expect(getByIdOperation.get)
                .toHaveBeenCalledWith(ObjectId.createFromTime(1), session);
        });
    });
});
