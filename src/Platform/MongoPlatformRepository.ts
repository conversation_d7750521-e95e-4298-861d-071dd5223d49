import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IPlatformRepository } from './IPlatformRepository';
import { IMongoPlatform, IPlatform } from './IPlatform';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { WithId } from '../shared/helper-types';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { convertObjectIdToString, convertStringToObjectId, isMongoObjectId } from '../shared/helper-functions';

export class MongoPlatformRepository implements IPlatformRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoPlatform>,
        private readonly getByIdOperation: IGetByIdOperation<IMongoPlatform>,
        private readonly updateOperation: IUpdateOperation<IMongoPlatform>,
        private readonly addOperation: IAddOperation<IMongoPlatform>,
    ) {
    }

    async insert(platform: IPlatform, session?: ClientSession): Promise<WithId<IPlatform>> {
        const insertData = convertStringToObjectId<IMongoPlatform>(platform);
        insertData.displayName ??= null;
        insertData.description ??= null;
        insertData.logoUrl ??= null;
        insertData.createdAt ??= new Date();
        insertData.updatedAt ??= null;
        const result = await this.addOperation.add(insertData, session);
        return convertObjectIdToString<WithId<IPlatform>>(result);
    }

    async getById(id: string, session?: ClientSession): Promise<WithId<IPlatform> | null> {
        if (!isMongoObjectId(id)) {
            throw new Error('Invalid Mongo ObjectId format');
        }
        const result = await this.getByIdOperation.get(new ObjectId(id), session);
        return result ? convertObjectIdToString<WithId<IPlatform>>(result) : null;
    }

    private async findOneBy(query: Record<string, any>, session?: ClientSession): Promise<IPaginatedFindByQueryResult<IMongoPlatform>> {
        const page = 1;
        const pageSize = 1;
        return await this.findByQueryOperation.find(query, page, pageSize, session);
    }

    async findOneByName(name: string, session?: ClientSession): Promise<WithId<IPlatform> | null> {
        const result = await this.findOneBy({ name }, session);
        return result.items?.length ? convertObjectIdToString<WithId<IPlatform>>(result.items[0]) : null;
    }

    async update(id: string, platform: Partial<IPlatform>, session?: ClientSession): Promise<WithId<IPlatform>> {
        if (!isMongoObjectId(id)) {
            throw new Error('Invalid Mongo ObjectId format');
        }
        const updateData = convertStringToObjectId<IMongoPlatform>(platform);
        updateData.updatedAt = new Date();
        const result = await this.updateOperation.update({ ...updateData, _id: new ObjectId(id) }, session);

        return convertObjectIdToString(result);
    }
}
