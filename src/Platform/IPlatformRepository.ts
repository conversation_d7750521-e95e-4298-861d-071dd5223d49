import { ClientSession } from 'mongodb';
import { IPlatform } from './IPlatform';
import { WithId } from '../shared/helper-types';

export interface IPlatformRepository {
    insert: (platform: IPlatform, session?: ClientSession) => Promise<WithId<IPlatform>>;

    getById: (id: string, session?: ClientSession) => Promise<WithId<IPlatform> | null>;

    findOneByName: (platformName: string, session?: ClientSession) => Promise<WithId<IPlatform> | null>;

    update: (id: string, platform: Partial<IPlatform>, session?: ClientSession) => Promise<WithId<IPlatform> | null>;
}
