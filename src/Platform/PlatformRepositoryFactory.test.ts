import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { PlatformRepositoryFactory } from './PlatformRepositoryFactory';
import { MongoPlatformRepository } from './MongoPlatformRepository';

describe(PlatformRepositoryFactory.name, () => {
    describe(PlatformRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            const result = PlatformRepositoryFactory.create(mongoClient, dbName, collectionName);

            expect(result).toBeInstanceOf(MongoPlatformRepository);
        });
    });
});
