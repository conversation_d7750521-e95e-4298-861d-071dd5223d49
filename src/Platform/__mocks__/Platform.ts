import { IMongoPlatform, IPlatform, IPlatformBase } from '../IPlatform';
import { ObjectId, WithId } from 'mongodb';

const defaultPlatform: IPlatformBase = {
    name: 'Test Platform',
    description: 'Test platform description',
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
};

const getPlatform = (overrides: Partial<IPlatform> = {}): WithId<IPlatform> => ({
    _id: new ObjectId().toHexString(),
    ...defaultPlatform,
    ...overrides,
});

const getMongoPlatform = (overrides: Partial<IMongoPlatform> = {}): WithId<IMongoPlatform> => ({
    _id: new ObjectId(),
    ...defaultPlatform,
    ...overrides,
});

export default {
    getPlatform,
    getMongoPlatform,
};
