import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoPlatformRepository } from './MongoPlatformRepository';
import { IMongoPlatform } from './IPlatform';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { IPlatformRepository } from './IPlatformRepository';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';

export class PlatformRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IPlatformRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoPlatform>(mongoClient, dbName, collectionName);
        const getByIdOperation = new MongoGetByIdOperation<IMongoPlatform>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoPlatform>(mongoClient, dbName, collectionName);
        const addOperation = new MongoAddOperation<IMongoPlatform>(mongoClient, dbName, collectionName);

        return new MongoPlatformRepository(
            findByQueryOperation,
            getByIdOperation,
            updateOperation,
            addOperation,
        );
    }
}
