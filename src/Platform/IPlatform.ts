import { Document, ObjectId } from 'mongodb';

export interface IPlatformBase {
    name: string;
    displayName?: string | null;
    description?: string | null;
    logoUrl?: string | null;
    status?: string;
    createdAt?: Date | string;
    updatedAt?: Date | string | null;
    email?: string | null;
}

export interface IPlatform extends IPlatformBase {
    _id?: string;
}

export interface IMongoPlatform extends IPlatformBase, Document {
    _id?: ObjectId;
}
