import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IAlert, IMongoAlert } from './IAlert';
import { IAlertRepository } from './IAlertRepository';
import { MongoAlertMapper } from './MongoAlertMapper';

export class MongoAlertRepository implements IAlertRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoAlert>,
    ) {}

    public async getByAlertId(alertId: string, session?: ClientSession): Promise<IAlert | null> {
        const filter = {
            alertId,
        };
        const result = await this.findByQueryOperation.findOne(
            filter,
            session,
        );

        return result ? MongoAlertMapper.toAlert(result) : null;
    }

    public async getByChargeflowIdAndAlertId(chargeflowId: string, alertId: string, session?: ClientSession): Promise<IAlert | null> {
        const filter = {
            chargeflowId: new ObjectId(chargeflowId),
            alertId,
        };
        const result = await this.findByQueryOperation.findOne(
            filter,
            session,
        );

        return result ? MongoAlertMapper.toAlert(result) : null;
    }

}
