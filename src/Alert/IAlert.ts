import { Nullable } from '@chargeflow-team/events-infra';
import { ObjectId, Document } from 'mongodb';
import { ILinking } from '../AlertLinking/IAlertLinking';
// The Alert object contains additional data, but only these fields are currently required to be exposed.
// More fields can be added here as needed in the future.
interface IAlertBase {
    alertId: string;
    amount: Nullable<number>;
    currency: Nullable<string>;
    amountInUsd: Nullable<number>;
    linking?: ILinking;
}

export interface IAlert extends IAlertBase {
    _id: string;
    chargeflowId: string;
}

export interface IMongoAlert extends IAlertBase, Document {
    _id: ObjectId;
    chargeflowId: ObjectId;
}
