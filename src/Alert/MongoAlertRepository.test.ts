
import mocks from './__mocks__/Alert';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { MongoAlertRepository } from './MongoAlertRepository';
import { IMongoAlert } from './IAlert';

describe(MongoAlertRepository.name, () => {
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoAlert>>;
    let repository: MongoAlertRepository;

    beforeEach(() => {
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoAlert>();
        repository = new MongoAlertRepository(mockQueryOperation);
    });

    describe('getByAlertId', () => {
        it('should return doc by alertId', async () => {

            const mongoAlertMock = mocks.getMongoAlert();
            const alertId = mongoAlertMock.alertId;
            mockQueryOperation.findOne.mockResolvedValue(mongoAlertMock);

            const result = await repository.getByAlertId(alertId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { alertId },
                undefined,
            );
            expect(result).toEqual(mocks.getAlert());
        });
    });

    describe('getByChargeflowIdAndAlertId', () => {
        it('should return doc by chargeflowId and alertId', async () => {
            const mongoAlertMock = mocks.getMongoAlert();
            const chargeflowId = mongoAlertMock.chargeflowId;
            const alertId = mongoAlertMock.alertId;
            mockQueryOperation.findOne.mockResolvedValue(mongoAlertMock);

            const result = await repository.getByChargeflowIdAndAlertId(chargeflowId.toHexString(), alertId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { chargeflowId, alertId },
                undefined,
            );
            expect(result).toEqual(mocks.getAlert());
        });
    });
});
