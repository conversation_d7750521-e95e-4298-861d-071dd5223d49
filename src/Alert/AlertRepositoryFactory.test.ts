import { AlertRepositoryFactory } from './AlertRepositoryFactory';
import { MongoClient } from 'mongodb';
import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoAlertRepository } from './MongoAlertRepository';

describe(AlertRepositoryFactory.name, () => {

    describe(AlertRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = AlertRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoAlertRepository);
        });
    });

});
