import { LinkingStatusDict } from '@chargeflow-team/common-models';
import { IAlert, IMongoAlert } from '../IAlert';
import { ObjectId } from 'mongodb';

const defaultId = new ObjectId();
const defaultAlertId = '0adc61ff-250f-42b7-8c11-c0f81401d9cf';
const defaultChargeflowId = '639b05c90110ad17f469a432';

const defaultAlert = {
    alertId: defaultAlertId,
    chargeflowId: defaultChargeflowId,
    amount: 100,
    currency: 'USD',
    amountInUsd: 100,
    linking: {
        isAuto: true,
        status: LinkingStatusDict.SUCCEEDED,
        transactionId: 'pi_3L29BER9g8OVaOCW146BoUPF',
        order: {
            orderId: '4778492788952',
            orderName: '#1001',
            orderTransactionId: '5587248382168',
            paymentGateway: 'shopify_payments',
        },
        updatedAt: new Date(),
    },
};

export const getMongoAlert = (overrides?: Partial<IMongoAlert>): IMongoAlert => ({
    ...defaultAlert,
    ...overrides,
    _id: defaultId,
    chargeflowId: new ObjectId(defaultChargeflowId),
});

export const getAlert = (overrides?: Partial<IAlert>): IAlert => ({
    ...defaultAlert,
    ...overrides,
    _id: defaultId.toHexString(),
    chargeflowId: defaultChargeflowId,
});

export default {
    getMongoAlert,
    getAlert,
};
