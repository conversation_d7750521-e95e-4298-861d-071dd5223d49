import { MongoClient } from 'mongodb';
import { IMongoAlert } from './IAlert';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IAlertRepository } from './IAlertRepository';
import { MongoAlertRepository } from './MongoAlertRepository';

export class AlertRepositoryFactory {

    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IAlertRepository {

        const findByQueryOperation = new MongoFindByQueryOperation<IMongoAlert>(mongoClient, dbName, collectionName);
        return new MongoAlertRepository(findByQueryOperation);

    }
}
