import { IAlert, IMongoAlert } from './IAlert';

export class MongoAlertMapper {
    public static toAlert(mongoAlert: IMongoAlert): IAlert {
        return {
            alertId: mongoAlert.alertId,
            amount: mongoAlert.amount,
            currency: mongoAlert.currency,
            amountInUsd: mongoAlert.amountInUsd,
            linking: mongoAlert.linking,
            _id: mongoAlert._id.toHexString(),
            chargeflowId: mongoAlert.chargeflowId.toHexString(),
        };
    }
}
