import { ClientSession, MongoClient } from 'mongodb';
import { ChargeflowDbName } from './shared/constants';
import { AccountCollectionName } from './Account/constants';
import { IAccountRepository } from './Account/IAccountRepository';
import { AccountRepositoryFactory } from './Account/AccountRepositoryFactory';
import { IDisputeRepository } from './Dispute/IDisputeRepository';
import { DisputeRepositoryFactory } from './Dispute/DisputeRepositoryFactory';
import { ChargeflowIdRepositoryFactory } from './ChargeflowId/ChargefowIdRepositoryFactory';
import { ChargeflowIdsCollectionName } from './ChargeflowId/constants';
import { IChargeflowIdRepository } from './ChargeflowId/IChargeflowIdRepository';
import { DisputeCollectionName } from './Dispute/constants';
import { IStripeUserAccountRepository } from './StripeUserAccount/IStripeUserAccountRepository';
import { StripeUserAccountRepositoryFactory } from './StripeUserAccount/StripeUserAccountRepositoryFactory';
import { StripeUserAccountCollectionName } from './StripeUserAccount/constants';
import { IShopRepository } from './Shop/IShopRepository';
import { ShopCollectionName } from './Shop/constants';
import { ShopRepositoryFactory } from './Shop/ShopRepositoryFactory';
import { IProcessorRepository } from './Processor/IProcessorRepository';
import { ProcessorRepositoryFactory } from './Processor/ProcessorRepositoryFactory';
import { ProcessorCollectionName } from './Processor/constants';
import { ActionRepositoryFactory } from './Action/ActionRepositoryFactory';
import { IActionRepository } from './Action/IActionRepository';
import { ActionCollectionName } from './Action/constants';
import { UserCookiesRepositoryFactory } from './UserCookies/UserCookiesRepositoryFactory';
import { UserCookiesCollectionName } from './UserCookies/constants';
import { IUserCookiesRepository } from './UserCookies/IUserCookiesRepository';
import { CustomerIdentityCollectionName } from './CustomerIdentity/constants';
import { ICustomerIdentityRepository } from './CustomerIdentity/ICustomerIdentityRepository';
import { CustomerIdentityRepositoryFactory } from './CustomerIdentity/CustomerIdentityRepositoryFactory';
import { CompanyRepositoryFactory } from './Company/CompanyRepositoryFactory';
import { CompanyCollectionName } from './Company/constants';
import { IMongoCompanyRepository } from './Company/IMongoCompanyRepository';
import { SettingsRepositoryFactory } from './Settings/SettingsRepositoryFactory';
import { SettingsCollectionName } from './Settings/constants';
import { ISettingsRepository } from './Settings/ISettingsRepository';
import { IPastOrderRepository } from './PastOrder/IPastOrderRepository';
import { PastOrderCollectionName } from './PastOrder/constants';
import { PastOrderRepositoryFactory } from './PastOrder/PastOrderRepositoryFactory';
import { IFraudAnalysisRepository } from './FraudAnalysis/IFraudAnalysisRepository';
import { FraudAnalysisRepositoryFactory } from './FraudAnalysis/FraudAnalysisRepositoryFactory';
import { FraudAnalysisCollectionName } from './FraudAnalysis/constants';
import { IEnrichmentsRepository } from './Enrichments/IEnrichmentsRepository';
import { EnrichmentsRepositoryFactory } from './Enrichments/EnrichmentsRepositoryFactory';
import { EnrichmentsCollectionName } from './Enrichments/constants';
import { BusinessUnitRepositoryFactory } from './BusinessUnit/BusinessUnitRepositoryFactory';
import { BusinessUnitCollectionName } from './BusinessUnit/constants';
import { IBusinessUnitRepository } from './BusinessUnit/IBusinessUnitRepository';
import { AccountMappingsRepositoryFactory } from './AccountMappings/AccountMappingsRepositoryFactory';
import { AccountMappingsCollectionName } from './AccountMappings/constants';
import { IAccountMappingsRepository } from './AccountMappings/IAccountMappingsRepository';
import { BillingCollectionName } from './Billing/constants';
import { IMongoBillingRepository } from './Billing/IMongoBillingRepository';
import { BillingRepositoryFactory } from './Billing/BillingRepositoryFactory';
import { ChargeCollectionName } from './Charge/constants';
import { IChargeRepository } from './Charge/IChargeRepository';
import { ChargeRepositoryFactory } from './Charge/ChargeRepositoryFactory';
import { OrderPlatformRepositoryFactory } from './OrderPlatform/OrderPlatformRepositoryFactory';
import { OrderPlatformCollectionName } from './OrderPlatform/constants';
import { IOrderPlatformRepository } from './OrderPlatform/IOrderPlatformRepository';
import { InquiryAutomationRepositoryFactory } from './InquiryAutomation/InquiryAutomationFactory';
import { InquiryAutomationCollectionName } from './InquiryAutomation/constants';
import { IInquiryAutomationRepository } from './InquiryAutomation/IInquiryAutomationRepository';
import { IMerchantProfileRepository } from './MerchantProfile/IMerchantProfileRepository';
import { MerchantProfileRepositoryFactory } from './MerchantProfile/MerchantProfileRepositoryFactory';
import { MerchantProfileCollectionName } from './MerchantProfile/constants';
import { NotificationPreferenceRepositoryFactory } from './NotificationPreference/NotificationPreferenceRepositoryFactory';
import { INotificationPreferenceRepository } from './NotificationPreference/INotificationPreferenceRepository';
import { NotificationPreferenceCollectionName } from './NotificationPreference/constants';
import { CustomersRepositoryFactory } from './Customers/CustomersRepositoryFactory';
import { ICustomersRepository } from './Customers/ICustomersRepository';
import { CustomersCollectionName } from './Customers/constants';
import { IProcessorMetaDataRepository } from './ProcessorsMetaData/IProcessorMetaDataRepository';
import { ProcessorMetaDataRepositoryFactory } from './ProcessorsMetaData/ProcessorsMetaDataRepositoryFactory';
import { ProcessorMetaDataCollectionName } from './ProcessorsMetaData/constants';
import { ICrmRepository } from './CRM/ICrmRepository';
import { CrmRepositoryFactory } from './CRM/CrmRepositoryFactory';
import { CrmCollectionName } from './CRM/constants';
import { ISyncProcessorARNsRepository } from './MonthlySyncProcessorARNs/ISyncProcessorARNsRepository';
import { SyncProcessorARNsRepositoryFactory } from './MonthlySyncProcessorARNs/SyncProcessorARNsRepositoryFactory';
import { SyncProcessorARNsCollection } from './MonthlySyncProcessorARNs/constants';
import { SubscriptionCollectionName } from './Subscription/constants';
import { SubscriptionRepositoryFactory } from './Subscription/SubscriptionRepositoryFactory';
import { ISubscriptionRepository } from './Subscription/ISubscriptionRepository';
import { IInvoiceRepository } from './Invoice/IInvoiceRepository';
import { InvoiceRepositoryFactory } from './Invoice/InvoiceRepositoryFactory';
import { InvoiceCollectionName } from './Invoice/constants';
import { GdprChangeLogCollectionName, GdprRepositoryFactory, IGdprChangeLogRepository } from './GdprChangeLog';
import { IAdyenAuthorisationRepository } from './AdyenAuthorisation/IAdyenAuthorisationRepository';
import { AdyenAuthorisationFactory } from './AdyenAuthorisation/AdyenAuthorisationFactory';
import { AdyenAuthorisationCollectionName } from './AdyenAuthorisation/constants';

import { PlatformRepositoryFactory } from './Platform/PlatformRepositoryFactory';
import { PlatformCollectionName } from './Platform/constants';
import { IPlatformRepository } from './Platform/IPlatformRepository';

import { PlatformDataRepositoryFactory } from './PlatformData/PlatformDataRepositoryFactory';
import { PlatformDataCollectionName } from './PlatformData/constants';
import { IPlatformDataRepository } from './PlatformData/IPlatformDataRepository';

import { PlatformUsersRepositoryFactory } from './PlatformUsers/PlatformUsersRepositoryFactory';
import { PlatformUsersCollectionName } from './PlatformUsers/constants';
import { IPlatformUsersRepository } from './PlatformUsers/IPlatformUsersRepository';

import { PlatformShopsRepositoryFactory } from './PlatformShops/PlatformShopsRepositoryFactory';
import { PlatformShopsCollectionName } from './PlatformShops/constants';
import { IPlatformShopsRepository } from './PlatformShops/IPlatformShopsRepository';
import { AlertLinkingRepositoryFactory } from './AlertLinking/AlertLinkingRepositoryFactory';
import { AlertLinkingCollectionName } from './AlertLinking/constants';
import { IAlertLinkingRepository } from './AlertLinking/IAlertLinkingRepository';
import { AlertRepositoryFactory } from './Alert/AlertRepositoryFactory';
import { AlertCollectionName } from './Alert/constants';
import { IAlertRepository } from './Alert/IAlertRepository';

export class DataAccessUnitOfWork {
    public readonly accountRepository: IAccountRepository;
    public readonly disputeRepository: IDisputeRepository;
    public readonly chargeflowId: IChargeflowIdRepository;
    public readonly stripeUserAccount: IStripeUserAccountRepository;
    public readonly shop: IShopRepository;
    public readonly processor: IProcessorRepository;
    public readonly action: IActionRepository;
    public readonly userCookies: IUserCookiesRepository;
    public readonly customerIdentityRepository: ICustomerIdentityRepository;
    public readonly company: IMongoCompanyRepository;
    public readonly settings: ISettingsRepository;
    public readonly platform: IPlatformRepository;
    public readonly platformData: IPlatformDataRepository;
    public readonly platformUsers: IPlatformUsersRepository;
    public readonly platformShops: IPlatformShopsRepository;
    public readonly pastOrder: IPastOrderRepository;
    public readonly fraudAnalysis: IFraudAnalysisRepository;
    public readonly enrichments: IEnrichmentsRepository;
    public readonly subscriptions: ISubscriptionRepository;
    public readonly invoice: IInvoiceRepository;
    public readonly businessUnits: IBusinessUnitRepository;
    public readonly accountMappings: IAccountMappingsRepository;
    public readonly billing: IMongoBillingRepository;
    public readonly charge: IChargeRepository;
    public readonly orderPlatform: IOrderPlatformRepository;
    public readonly merchantProfile: IMerchantProfileRepository;
    private session: ClientSession | null;
    public readonly inquiryAutomationRepository: IInquiryAutomationRepository;
    public readonly notificationPreference: INotificationPreferenceRepository;
    public readonly customersRepository: ICustomersRepository;
    public readonly processorMetaDataRepository: IProcessorMetaDataRepository;
    public readonly crm: ICrmRepository;
    public readonly syncProcessorARNsRepository: ISyncProcessorARNsRepository;
    public readonly gdprRepository: IGdprChangeLogRepository;
    public readonly adyenAuthorisationRepository: IAdyenAuthorisationRepository;
    public readonly alertRepository: IAlertRepository;
    public readonly alertLinkingRepository: IAlertLinkingRepository;

    constructor(private readonly mongoClient: MongoClient) {
        this.accountRepository = AccountRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            AccountCollectionName,
        );

        this.chargeflowId = ChargeflowIdRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ChargeflowIdsCollectionName,
        );

        this.disputeRepository = DisputeRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            DisputeCollectionName,
        );

        this.stripeUserAccount = StripeUserAccountRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            StripeUserAccountCollectionName,
        );

        this.shop = ShopRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ShopCollectionName,
        );

        this.processor = ProcessorRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ProcessorCollectionName,
        );

        this.action = ActionRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ActionCollectionName,
        );

        this.userCookies = UserCookiesRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            UserCookiesCollectionName,
        );

        this.company = CompanyRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            CompanyCollectionName,
        );

        this.billing = BillingRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            BillingCollectionName,
        );

        this.customerIdentityRepository = CustomerIdentityRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            CustomerIdentityCollectionName,
        );

        this.settings = SettingsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            SettingsCollectionName,
        );

        this.platform = PlatformRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            PlatformCollectionName,
        );

        this.platformData = PlatformDataRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            PlatformDataCollectionName,
        );

        this.platformUsers = PlatformUsersRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            PlatformUsersCollectionName,
        );

        this.platformShops = PlatformShopsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            PlatformShopsCollectionName,
        );

        this.pastOrder = PastOrderRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            PastOrderCollectionName,
        );

        this.fraudAnalysis = FraudAnalysisRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            FraudAnalysisCollectionName,
        );

        this.enrichments = EnrichmentsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            EnrichmentsCollectionName,
        );

        this.subscriptions = SubscriptionRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            SubscriptionCollectionName,
        );

        this.invoice = InvoiceRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            InvoiceCollectionName,
        );

        this.businessUnits = BusinessUnitRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            BusinessUnitCollectionName,
        );

        this.accountMappings = AccountMappingsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            AccountMappingsCollectionName,
        );

        this.charge = ChargeRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ChargeCollectionName,
        );

        this.orderPlatform = OrderPlatformRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            OrderPlatformCollectionName,
            ShopCollectionName,
        );

        this.merchantProfile = MerchantProfileRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            MerchantProfileCollectionName,
        );

        this.session = null;

        this.inquiryAutomationRepository = InquiryAutomationRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            InquiryAutomationCollectionName,
        );

        this.notificationPreference = NotificationPreferenceRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            NotificationPreferenceCollectionName,
        );

        this.customersRepository = CustomersRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            CustomersCollectionName,
        );

        this.processorMetaDataRepository = ProcessorMetaDataRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            ProcessorMetaDataCollectionName,
        );

        this.crm = CrmRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            CrmCollectionName,
        );

        this.syncProcessorARNsRepository = SyncProcessorARNsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            SyncProcessorARNsCollection,
        );

        this.gdprRepository = GdprRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            GdprChangeLogCollectionName,
        );

        this.adyenAuthorisationRepository = AdyenAuthorisationFactory.create(
            mongoClient,
            ChargeflowDbName,
            AdyenAuthorisationCollectionName,
        );

        this.alertRepository = AlertRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            AlertCollectionName,
        );

        this.alertLinkingRepository = AlertLinkingRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            AlertLinkingCollectionName,
        );
    }

    /**
     * Creates a new session and starts a transaction
     * Use the return session to perform operations in a transaction
     * E.g. session.abortTransaction(), session.commitTransaction(), session.endSession(), etc.
     * See https://www.mongodb.com/docs/manual/reference/method/Session/#session-methods-for-transactions
     * @returns ClientSession
     */
    startTransactionV2(): ClientSession {
        const newSession = this.mongoClient.startSession();
        newSession.startTransaction();

        return newSession;
    }

    /**
     * @deprecated Use startTransactionV2 instead
     * @returns
     */
    startTransaction(): ClientSession {
        if (!this.session) {
            this.session = this.mongoClient.startSession();
            this.session.startTransaction();
        }

        return this.session;
    }

    /**
     * @deprecated Use startTransactionV2 instead
     * @returns
     */
    async commitTransaction(): Promise<void> {
        if (!this.session) {
            throw new Error('No transaction started');
        }

        await this.session.commitTransaction();
        await this.endSession();
    }

    /**
     * @deprecated Use startTransactionV2 instead
     * @returns
     */
    async abortTransaction(): Promise<void> {
        if (!this.session) {
            throw new Error('No transaction started');
        }

        await this.session.abortTransaction();
        await this.endSession();
    }

    /**
     * @deprecated Use startTransactionV2 instead
     * @returns
     */
    private async endSession(): Promise<void> {
        if (this.session) {
            await this.session.endSession();
            this.session = null;
        }
    }
}
