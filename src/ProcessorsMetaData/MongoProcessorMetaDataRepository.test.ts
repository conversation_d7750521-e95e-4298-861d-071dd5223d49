import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { MongoProcessorMetaDataRepository } from './MongoProcessorMetaDataRepository';
import { mock } from 'jest-mock-extended';
import { IProcessorARN, IProcessorMetaData } from './Types';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { UpdateARNsForProcessorPayload } from './IProcessorMetaDataRepository';
import { ReferenceObject } from '@chargeflow-team/common-models';
import { WithId, ObjectId } from 'mongodb';

describe('MongoProcessorMetaDataRepository', () => {
    const mockFindByQuery = mock<IFindByQueryOperation<IProcessorMetaData>>();
    const mockUpdate = mock<IUpdateOperation<IProcessorMetaData>>();
    const mockAdd = mock<IAddOperation<IProcessorMetaData>>();
    const mockDate = new Date('2025-02-04T00:00:00.000Z');

    const repository = new MongoProcessorMetaDataRepository(
        mockFindByQuery,
        mockUpdate,
        mockAdd,
    );

    const samplePayload: UpdateARNsForProcessorPayload = {
        processorId: '507f1f77bcf86cd799439011',
        arns: [
            {
                value: 'arn-1',
                referenceType: ReferenceObject.REFUND,
                referenceId: '1',
                createdDate: new Date('2025-02-04'),
            },
            {
                value: 'arn-2',
                referenceType: ReferenceObject.REFUND,
                referenceId: '2',
                createdDate: new Date('2025-02-04'),
            },
        ],
    };

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers().setSystemTime(mockDate);
    });

    describe('updateARNsForProcessor', () => {
        it('should update existing metadata', async () => {
            // Arrange
            const processorObjectId = new ObjectId('507f1f77bcf86cd799439011');
            const existingMeta = {
                _id: 'meta-1',
                processorId: processorObjectId,
                arns: [ {
                    value: 'arn-3',
                    referenceType: ReferenceObject.REFUND,
                    referenceId: '3',
                    createdDate: new Date('2025-02-04'),
                } ],
                updatedAt: new Date('2025-02-04'),
                createdAt: new Date('2025-02-04'),
            } as IProcessorMetaData;

            // @ts-expect-error; mock object
            mockFindByQuery.findOne.mockResolvedValue(existingMeta);

            await repository.updateARNsForProcessor(samplePayload);

            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(mockUpdate.updateWithFilter).toHaveBeenCalledWith(
                { _id: 'meta-1' },
                { $set: {
                    arns:  samplePayload.arns,
                    updatedAt: mockDate,
                } },
            );
            expect(mockAdd.add).not.toHaveBeenCalled();
        });

        it('should create new metadata when none exists', async () => {
            mockFindByQuery.findOne.mockResolvedValue(null);

            await repository.updateARNsForProcessor(samplePayload);

            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(mockAdd.add).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
                arns: samplePayload.arns,
                createdAt: mockDate,
                updatedAt: mockDate,
            });
            expect(mockUpdate.updateWithFilter).not.toHaveBeenCalled();
        });

        it('should handle empty ARNs array', async () => {
            const payloadWithEmptyARNs = {
                ...samplePayload,
                arns: [],
            };
            mockFindByQuery.findOne.mockResolvedValue(null);

            await repository.updateARNsForProcessor(payloadWithEmptyARNs);

            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(mockAdd.add).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
                arns: [],
                createdAt: mockDate,
                updatedAt: mockDate,
            });
        });
    });

    describe('findProcessorARNs', () => {
        const processorId = '507f1f77bcf86cd799439011';
        const sampleARNs: IProcessorARN[] = [
            {
                value: 'arn-1',
                referenceType: ReferenceObject.REFUND,
                referenceId: '1',
                createdDate: new Date('2025-02-04'),
            },
            {
                value: 'arn-2',
                referenceType: ReferenceObject.REFUND,
                referenceId: '2',
                createdDate: new Date('2025-02-05'),
            },
        ];

        it('should return ARNs when metadata exists', async () => {
            // Arrange
            const mockMetaData = {
                _id: 'meta-1',
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
                arns: sampleARNs,
            } as unknown as WithId<IProcessorMetaData>;

            mockFindByQuery.findOne.mockResolvedValue(mockMetaData);

            // Act
            const result = await repository.findProcessorARNs(processorId);

            // Assert
            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(result).toEqual(sampleARNs);
        });

        it('should return empty array when no metadata exists', async () => {
            // Arrange
            mockFindByQuery.findOne.mockResolvedValue(null);

            // Act
            const result = await repository.findProcessorARNs(processorId);

            // Assert
            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(result).toEqual([]);
        });

        it('should return empty array when metadata exists but ARNs are empty', async () => {
            // Arrange
            const mockMetaData = {
                _id: 'meta-1',
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
                arns: [],
            } as unknown as WithId<IProcessorMetaData>;

            mockFindByQuery.findOne.mockResolvedValue(mockMetaData);

            // Act
            const result = await repository.findProcessorARNs(processorId);

            // Assert
            expect(mockFindByQuery.findOne).toHaveBeenCalledWith({
                processorId: new ObjectId('507f1f77bcf86cd799439011'),
            });
            expect(result).toEqual([]);
        });
    });
});
