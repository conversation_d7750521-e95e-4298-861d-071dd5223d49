import { MongoClient } from 'mongodb';
import { ProcessorMetaDataRepositoryFactory } from './ProcessorsMetaDataRepositoryFactory';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { MongoProcessorMetaDataRepository } from './MongoProcessorMetaDataRepository';

// Proper class mock setup
jest.mock('../shared/operation/AddOperation/MongoAddOperation', () => ({
    MongoAddOperation: jest.fn().mockImplementation(() => ({ add: jest.fn() })),
}));

jest.mock('../shared/operation/FindByQueryOperation/MongoFindByQueryOperation', () => ({
    MongoFindByQueryOperation: jest.fn().mockImplementation(() => ({ findOne: jest.fn() })),
}));

jest.mock('../shared/operation/UpdateOperation/MongoUpdateOperation', () => ({
    MongoUpdateOperation: jest.fn().mockImplementation(() => ({ updateWithFilter: jest.fn() })),
}));

jest.mock('./MongoProcessorMetaDataRepository', () => ({
    MongoProcessorMetaDataRepository: jest.fn().mockImplementation(() => ({
        updateARNsForProcessor: jest.fn(),
    })),
}));

describe('ProcessorMetaDataRepositoryFactory', () => {
    const mockMongoClient = new MongoClient('mongodb://localhost:27017');
    const testDbName = 'testDB';
    const testCollectionName = 'testCollection';

    describe('create', () => {
        it('should initialize operations with correct parameters', () => {
            ProcessorMetaDataRepositoryFactory.create(mockMongoClient, testDbName, testCollectionName);

            expect(MongoAddOperation).toHaveBeenCalledWith(
                mockMongoClient,
                testDbName,
                testCollectionName,
            );
        });

        it('should return repository with initialized operations', () => {

            ProcessorMetaDataRepositoryFactory.create(mockMongoClient, testDbName, testCollectionName);

            expect(MongoProcessorMetaDataRepository).toHaveBeenCalled();
        });
    });
});
