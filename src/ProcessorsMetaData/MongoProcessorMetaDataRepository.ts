import { ObjectId } from 'mongodb';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IProcessorMetaDataRepository, UpdateARNsForProcessorPayload } from './IProcessorMetaDataRepository';
import { IProcessorARN, IProcessorMetaData } from './Types';

export class MongoProcessorMetaDataRepository implements IProcessorMetaDataRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IProcessorMetaData>,
        private readonly updateOperation: IUpdateOperation<IProcessorMetaData>,
        private readonly addOperation: IAddOperation<IProcessorMetaData>,
    ) {}

    public async updateARNsForProcessor({
        processorId,
        arns,
    }: UpdateARNsForProcessorPayload): Promise<void> {
        const processorObjectId = new ObjectId(processorId);
        const existingProcessorMetaData = await this.findByQueryOperation.findOne({
            processorId: processorObjectId,
        });

        if (existingProcessorMetaData) {
            await this.updateOperation.updateWithFilter({
                _id: existingProcessorMetaData._id,
            }, {
                $set: {
                    arns,
                    updatedAt: new Date(),
                },
            });

            return;
        }

        await this.addOperation.add({
            processorId: processorObjectId,
            arns,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
    }

    public async findProcessorARNs(processorId: string): Promise<IProcessorARN[]> {
        const processorObjectId = new ObjectId(processorId);
        const processorMetaData = await this.findByQueryOperation.findOne({
            processorId: processorObjectId,
        });

        return processorMetaData ? processorMetaData.arns : [];
    }
}
