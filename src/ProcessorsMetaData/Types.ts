import { ReferenceObject } from '@chargeflow-team/common-models';
import { Document, ObjectId } from 'mongodb';
import { z } from 'zod';

export const processorARNSchema = z.object({
    value: z.string(),
    referenceId: z.string(),
    referenceType: z.nativeEnum(ReferenceObject),
    createdDate: z.date(),
});

export type IProcessorARN = z.infer<typeof processorARNSchema>;

export interface IProcessorMetaData extends Document {
    processorId: ObjectId;
    arns: IProcessorARN[];
    createdAt: Date;
    updatedAt: Date;
}