import { MongoClient } from 'mongodb';
import { IProcessorMetaData } from './Types';
import { MongoProcessorMetaDataRepository } from './MongoProcessorMetaDataRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';

export class ProcessorMetaDataRepositoryFactory {
    public static create(mongoClient: MongoClient, dbName: string, collectionName: string) {
        const addOperation = new MongoAddOperation<IProcessorMetaData>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IProcessorMetaData>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IProcessorMetaData>(mongoClient, dbName, collectionName);

        return new MongoProcessorMetaDataRepository(
            findByQueryOperation,
            updateOperation,
            addOperation,
        );
    }
}
