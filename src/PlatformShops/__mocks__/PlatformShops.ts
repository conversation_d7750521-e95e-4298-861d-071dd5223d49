import { IMongoPlatformShops, IPlatformShops, IPlatformShopsBase } from '../IPlatformShops';
import { ObjectId, WithId } from 'mongodb';

const defaultPlatformShops: IPlatformShopsBase = {
    merchantName: 'test-merchant',
    merchantId: 'test-merchant-id',
};

const getPlatformShops = (overrides: Partial<IPlatformShops> = {}): WithId<IPlatformShops> => ({
    _id: new ObjectId().toHexString(),
    platformId: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    ...defaultPlatformShops,
    ...overrides,
});

const getMongoPlatformShops = (overrides: Partial<IMongoPlatformShops> = {}): WithId<IMongoPlatformShops> => ({
    _id: new ObjectId(),
    platformId: new ObjectId(),
    chargeflowId: new ObjectId(),
    ...defaultPlatformShops,
    ...overrides,
});

export default {
    getPlatformShops,
    getMongoPlatformShops,
};
