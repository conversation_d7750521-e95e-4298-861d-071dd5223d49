import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { Db, MongoClient } from 'mongodb';
import { PlatformShopsRepositoryFactory } from './PlatformShopsRepositoryFactory';
import { MongoPlatformShopsRepository } from './MongoPlatformShopsRepository';

describe(PlatformShopsRepositoryFactory.name, () => {
    describe(PlatformShopsRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            const db = mock<Db>();
            const mongoClient = mock<MongoClient>();
            mongoClient.db.mockReturnValue(db);

            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            const result = PlatformShopsRepositoryFactory.create(mongoClient, dbName, collectionName);

            expect(result)
                .toBeInstanceOf(MongoPlatformShopsRepository);
        });
    });
});
