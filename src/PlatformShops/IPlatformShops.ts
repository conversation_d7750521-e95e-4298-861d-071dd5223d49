import { Document, ObjectId } from 'mongodb';

export interface IPlatformShopsBase {
    createdAt?: Date | string;
    updatedAt?: Date | string | null;
    status?: string;
    merchantId: string;
    merchantName: string;

    [key: string]: any;
}

export interface IPlatformShops extends IPlatformShopsBase {
    _id?: string;
    platformId: string;
    chargeflowId: string;
}

export interface IMongoPlatformShops extends IPlatformShopsBase, Document {
    _id?: ObjectId;
    platformId: ObjectId;
    chargeflowId: ObjectId;
}
