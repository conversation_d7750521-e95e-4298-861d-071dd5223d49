import { mock } from 'jest-mock-extended';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, Collection, ObjectId, WithId } from 'mongodb';
import { MongoPlatformShopsRepository } from './MongoPlatformShopsRepository';
import { IMongoPlatformShops } from './IPlatformShops';
import platformMocks from './__mocks__/PlatformShops';
import addOperationMocks from '../shared/operation/AddOperation/__mocks__/MongoAddOperation';
import updateOperationMocks from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { convertStringToObjectId } from '../shared/helper-functions';

describe(MongoPlatformShopsRepository.name, () => {
    let mongoPlatformShopsRepository: MongoPlatformShopsRepository;
    const mockCollection = mock<Collection<IMongoPlatformShops>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoPlatformShops>>();
    const mockAddOperation = addOperationMocks.getAddOperation<IMongoPlatformShops>();
    const mockUpdateOperation = updateOperationMocks.getUpdateOperation<IMongoPlatformShops>();

    beforeEach(() => {
        mongoPlatformShopsRepository = new MongoPlatformShopsRepository(
            mockCollection,
            findByQueryOperation,
            mockAddOperation,
            mockUpdateOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoPlatformShopsRepository.prototype.getConnectedShops.name, () => {
        it('returns a single platform, if found', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformShops();
            const session = mock<ClientSession>();

            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformShops>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformShopsRepository.getConnectedShops(platform.platformId, undefined, session);

            expect(result.items).toEqual([ {
                id: platform.chargeflowId.toString(),
                status: platform.status,
            } ]);
            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
        });

        it('returns empty array if platform is not found', async () => {
            expect.assertions(2);

            const session = mock<ClientSession>();
            findByQueryOperation.find.mockResolvedValue({
                items: [],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformShopsRepository.getConnectedShops(
                ObjectId.createFromTime(1).toHexString(),
                undefined,
                session,
            );

            expect(result.items).toEqual([]);
            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
        });

        it('handles filtering options correctly', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformShops();
            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformShops>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            // Test with options parameter
            const options = {
                limit: 10,
                offset: 0,
                status: [ 'active' ],
                search: 'test',
            };

            const result = await mongoPlatformShopsRepository.getConnectedShops(
                platform.platformId,
                options,
                undefined,
            );

            expect(result.items).toEqual([ {
                id: platform.chargeflowId.toString(),
                status: platform.status,
            } ]);
            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
        });

        it('handles chargeflowId search correctly', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformShops();
            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformShops>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            // Test with chargeflowId search
            const validObjectId = new ObjectId().toHexString();
            const options = {
                search: validObjectId,
            };

            const result = await mongoPlatformShopsRepository.getConnectedShops(
                platform.platformId,
                options,
                undefined,
            );

            expect(result.items).toEqual([ {
                id: platform.chargeflowId.toString(),
                status: platform.status,
            } ]);
            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
        });

        it('handles invalid ObjectId search gracefully', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformShops();
            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformShops>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            // Test with invalid ObjectId search
            const invalidObjectId = 'not-a-valid-object-id';
            const options = {
                search: invalidObjectId,
            };

            const result = await mongoPlatformShopsRepository.getConnectedShops(
                platform.platformId,
                options,
                undefined,
            );

            expect(result.items).toEqual([ {
                id: platform.chargeflowId.toString(),
                status: platform.status,
            } ]);
            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
        });
    });

    describe(MongoPlatformShopsRepository.prototype.updatePlatformShopStatus.name, () => {
        it('should call the set parameters upon status update', async () => {
            const platformId = new ObjectId().toString();
            const chargeflowId = new ObjectId().toString();
            const status = 'active';
            await mongoPlatformShopsRepository.updatePlatformShopStatus(
                platformId, chargeflowId, status,
            );
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                {
                    platformId: new ObjectId(platformId),
                    chargeflowId: new ObjectId(chargeflowId),
                },
                { $set: { status } },
                undefined,
            );
        });
    });
});
