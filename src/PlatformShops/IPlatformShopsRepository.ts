import { ClientSession } from 'mongodb';
import { IPlatformShops } from './IPlatformShops';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
export interface IPlatformShopsRepository {
    getConnectedShops: (
        platformId: string,
        options?: {
            limit?: number,
            offset?: number,
            status?: string[],
            dateFrom?: string,
            dateTo?: string,
            search?: string,
        },
        session?: ClientSession
    ) => Promise<{
        items: Array<{ id: string, status: string | undefined }>;
        pagination?: IPaginationCalculatorResult;
    }>;
    getPlatformShopsByChargeflowId: (chargeflowId: string, session?: ClientSession) => Promise<IPlatformShops | null>;

    getPlatformShopsByMerchantId: (merchantId: string, session?: ClientSession) => Promise<IPlatformShops[] | null>;

    connectShopToPlatform: (
        shopPlatform: Pick<IPlatformShops, 'platformId' | 'chargeflowId' | 'merchantId' | 'status' | 'merchantName'>,
        session?: ClientSession
    ) => Promise<boolean>;

    disconnectShopToPlatform: (platformId: string, chargeflowId: string, session?: ClientSession) => Promise<boolean>;

    updatePlatformShopStatus: (platformId: string, chargeflowId: string, status: string, session?: ClientSession) => Promise<void>;
}
