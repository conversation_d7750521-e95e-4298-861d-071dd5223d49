import { ClientSession, Collection, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IPlatformShopsRepository } from './IPlatformShopsRepository';
import { IMongoPlatformShops, IPlatformShops } from './IPlatformShops';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';

export class MongoPlatformShopsRepository implements IPlatformShopsRepository {
    constructor(
        private readonly collection: Collection<IMongoPlatformShops>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoPlatformShops>,
        private readonly addOperation: IAddOperation<IMongoPlatformShops>,
        private readonly updateOperation: IUpdateOperation<IMongoPlatformShops>,
    ) {
    }

    async getConnectedShops(
        platformId: string,
        options?: {
            limit?: number,
            offset?: number,
            status?: string[],
            dateFrom?: string,
            dateTo?: string,
            search?: string,
        },
        session?: ClientSession,
    ): Promise<{
        items: Array<{ id: string, status: string | undefined }>;
        pagination?: IPaginationCalculatorResult;
    }> {
        const limit = options?.limit !== undefined ? Math.min(Math.max(options.limit, 1), 100) : 20;
        const offset = options?.offset !== undefined ? Math.max(Math.min(options.offset, 1000), 0) : 0;

        const filter: any = { 'platformId': new ObjectId(platformId) };

        if (options?.status && options.status.length > 0) {
            filter.status = { $in: options.status };
        }

        if (options?.dateFrom || options?.dateTo) {
            filter.createdAt = {};

            if (options?.dateFrom) {
                const dateFrom = new Date(options.dateFrom);
                if (!isNaN(dateFrom.getTime())) {
                    filter.createdAt.$gte = dateFrom;
                }
            }

            if (options?.dateTo) {
                const dateTo = new Date(options.dateTo);
                if (!isNaN(dateTo.getTime())) {
                    const currentDate = new Date();
                    const validDateTo = dateTo <= currentDate ? dateTo : currentDate;
                    filter.createdAt.$lte = validDateTo;
                }
            }

            if (Object.keys(filter.createdAt).length === 0) {
                delete filter.createdAt;
            }
        }

        if (options?.search) {
            const searchQuery = options.search.trim();
            if (searchQuery) {
                filter.$or = [
                    { merchantName: { $regex: searchQuery, $options: 'i' } },
                ];

                if (ObjectId.isValid(searchQuery)) {
                    filter.$or.push({ chargeflowId: new ObjectId(searchQuery) });
                }
            }
        }

        const page = Math.floor(offset / limit) + 1;

        const sort = { createdAt: -1 as const };

        const result = await this.findByQueryOperation
            .find(filter, page, limit, session, undefined, sort);

        return {
            items: Array.isArray(result.items)
                ? result.items.map(e => ({
                    id: e.chargeflowId?.toHexString(),
                    status: e.status,
                }))
                : [],
            pagination: result.pagination,
        };
    }

    async getPlatformShopsByChargeflowId(chargeflowId: string, session?: ClientSession): Promise<IPlatformShops | null> {
        const result = await this.findByQueryOperation
            .find({ 'chargeflowId': new ObjectId(chargeflowId) }, 1, 1, session);
        return result.items.length ? this.mapPlatformShops(result.items[0]) : null;
    }

    async getPlatformShopsByMerchantId(merchantId: string, session?: ClientSession): Promise<IPlatformShops[] | null> {
        const result = await this.findByQueryOperation
            .find({ merchantId }, undefined, undefined, session);
        return result.items.length ? result.items.map(this.mapPlatformShops) : null;
    }

    async connectShopToPlatform(
        shopPlatform: Pick<IPlatformShops, 'platformId' | 'chargeflowId' | 'merchantId' | 'status' | 'merchantName'>,
        session?: ClientSession,
    ): Promise<boolean> {
        await this.addOperation.add({
            platformId: new ObjectId(shopPlatform.platformId),
            chargeflowId: new ObjectId(shopPlatform.chargeflowId),
            merchantId: shopPlatform.merchantId,
            merchantName: shopPlatform.merchantName,
            status: shopPlatform.status,
            createdAt: new Date(),
            updatedAt: new Date(),
        }, session);
        return true;
    }

    async updatePlatformShopStatus(platformId: string, chargeflowId: string, status: string, session?: ClientSession): Promise<void> {
        await this.updateOperation.updateWithFilter(
            {
                platformId: new ObjectId(platformId),
                chargeflowId: new ObjectId(chargeflowId),
            },
            { $set: { status } },
            session,
        );
    }

    async disconnectShopToPlatform(platformId: string, chargeflowId: string, session?: ClientSession): Promise<boolean> {
        await this.collection.deleteMany({ platformId: new ObjectId(platformId), chargeflowId: new ObjectId(chargeflowId) }, { session });
        return true;
    }

    private mapPlatformShops(mongoPlatformShops: IMongoPlatformShops): IPlatformShops {
        return {
            ...mongoPlatformShops,
            _id: mongoPlatformShops._id?.toHexString(),
            platformId: mongoPlatformShops.platformId.toHexString(),
            chargeflowId: mongoPlatformShops.chargeflowId.toHexString(),
        };
    }
}
