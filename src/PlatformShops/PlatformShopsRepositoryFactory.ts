import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoPlatformShopsRepository } from './MongoPlatformShopsRepository';
import { IMongoPlatformShops } from './IPlatformShops';
import { IPlatformShopsRepository } from './IPlatformShopsRepository';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class PlatformShopsRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IPlatformShopsRepository {
        const collection = mongoClient.db(dbName)
            .collection<IMongoPlatformShops>(collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoPlatformShops>(mongoClient, dbName, collectionName);
        const addOperation = new MongoAddOperation<IMongoPlatformShops>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoPlatformShops>(mongoClient, dbName, collectionName);

        return new MongoPlatformShopsRepository(
            collection,
            findByQueryOperation,
            addOperation,
            updateOperation,
        );
    }
}
