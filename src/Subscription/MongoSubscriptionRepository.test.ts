import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import SubscriptionMocks from './__mocks__/Subscription';
import { MongoSubscriptionCacheDocument } from './ISubscription';
import { MongoSubscriptionMapper } from './MongoSubscriptionMapper';
import { MongoSubscriptionRepository } from './MongoSubscriptionRepository';
const MOCK_SYSTEM_TIME = '2024-04-12T00:00:00Z';

describe(MongoSubscriptionRepository.name, () => {
    let mongoSubscriptionRepository: MongoSubscriptionRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<MongoSubscriptionCacheDocument>>();
    const updateOperation = mock<IUpdateOperation<MongoSubscriptionCacheDocument>>();

    beforeEach(() => {
        mongoSubscriptionRepository = new MongoSubscriptionRepository(findByQueryOperation, updateOperation);

        jest.useFakeTimers().setSystemTime(new Date(MOCK_SYSTEM_TIME));
    });

    afterEach(jest.clearAllMocks);

    describe(MongoSubscriptionRepository.prototype.upsertSubscription.name, () => {
        it('should upsert subscription', async () => {
            expect.assertions(2);

            // GIVEN
            const subscription = SubscriptionMocks.getSubscriptionCacheDocument();
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();

            // WHEN
            await mongoSubscriptionRepository.upsertSubscription(disputeId, subscription.chargeflowId, subscription.metadata.source, subscription.metadata.processor, subscription.data, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledTimes(1);
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    cacheKey: `disputeId:${disputeId}`,
                    chargeflowId: new ObjectId(subscription.chargeflowId),
                },
                expect.objectContaining({
                    $setOnInsert: {
                        dateCreated: expect.any(Date),
                        chargeflowId: new ObjectId(subscription.chargeflowId),
                    },
                    $currentDate: {
                        dateUpdated: true,
                    },
                    $set: {
                        data: subscription.data,
                        cacheKey: `disputeId:${disputeId}`,
                        metadata: {
                            processor: subscription.metadata.processor,
                            source: subscription.metadata.source,
                            disputeId: new ObjectId(disputeId),
                        },
                    },
                }),
                session,
            );
        });
    });

    describe(MongoSubscriptionRepository.prototype.bumpSubscriptionLastUpdatedDate.name, () => {
        it('should bump subscription last updated date', async () => {
            expect.assertions(2);

            // GIVEN
            const disputeId = new ObjectId();
            const chargeflowId = new ObjectId();
            const session = mock<ClientSession>();

            // WHEN
            await mongoSubscriptionRepository.bumpSubscriptionLastUpdatedDate(disputeId.toHexString(), chargeflowId.toHexString(), session);

            // THEN
            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                {
                    chargeflowId,
                    'metadata.disputeId': disputeId,
                },
                { $currentDate: { dateUpdated: true } },
                session,
            );
        });
    });

    describe(MongoSubscriptionRepository.prototype.getSubscriptionByDisputeIdWithCacheInfo.name, () => {
        it('should return subscription with cache info object', async () => {
            expect.assertions(3);

            // GIVEN
            const disputeId = new ObjectId();
            const subscription = SubscriptionMocks.getSubscriptionCacheDocument();
            const session = mock<ClientSession>();

            const expectedResult = subscription;

            const findByQueryResult = MongoSubscriptionMapper.toMongoSubscriptionCacheDocument(subscription) as WithId<MongoSubscriptionCacheDocument>;
            findByQueryOperation.findOne.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoSubscriptionRepository.getSubscriptionByDisputeIdWithCacheInfo(disputeId.toHexString(), subscription.chargeflowId, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.findOne).toHaveBeenCalledWith(
                {
                    chargeflowId: new ObjectId(subscription.chargeflowId),
                    'metadata.disputeId': disputeId,
                },
                session,
            );
        });
    });

    describe(MongoSubscriptionRepository.prototype.getSubscriptionByDisputeId.name, () => {
        it('should return subscription', async () => {
            expect.assertions(1);

            // GIVEN
            const disputeId = new ObjectId();
            const subscription = SubscriptionMocks.getSubscriptionCacheDocument();
            const session = mock<ClientSession>();

            const expectedResult = subscription.data;

            const findByQueryResult = MongoSubscriptionMapper.toMongoSubscriptionCacheDocument(subscription) as WithId<MongoSubscriptionCacheDocument>;
            findByQueryOperation.findOne.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoSubscriptionRepository.getSubscriptionByDisputeId(disputeId.toHexString(), subscription.chargeflowId, session);

            // THEN
            expect(result).toEqual(expectedResult);
        });
    });
});
