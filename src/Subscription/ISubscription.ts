import { ObjectId } from 'mongodb';
import {
    IMongoCachedEnrichmentDocument,
    ICachedEnrichmentDocument,
} from '../shared/helper-types';

export enum SubscriptionSource {
  INTEGRATION = 'integration',
  API = 'api',
  IMPORT = 'import',
  MANUAL = 'manual',
}

export type SubscriptionFrequency = 'day' | 'week' | 'month' | 'year';

export type SubscriptionStatus = 'active' | 'inactive' | 'canceled' | 'unknown';

export interface ISubscription {
  // Primary and Foreign Keys
  accountId?: string | null;
  chargeflowId?: string | null;
  businessId?: string | null;
  processorId?: string | null;
  subscriptionId?: string | null;
  extSubscriptionId?: string | null;
  customerId?: string | null;
  paymentIntentId?: string | null;

  // Timestamps
  extCreatedAt?: Date | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;

  // Source Information
  source?: SubscriptionSource | null;

  // Subscription Dates
  startDate?: Date | null;
  endedAt?: Date | null;
  canceledAt?: Date | null;
  scheduledCancellationDate?: Date | null;
  lastRenewedAt?: Date | null;
  renewsAt?: Date | null;

  // Subscription Details
  name?: string | null;
  status?: SubscriptionStatus | null;
  priceModel?: string | null;
  frequency?: SubscriptionFrequency | null;
  frequencyCount?: number | null;
  cancellationComment?: string | null;
  cancellationReason?: string | null;
  collectionMethod?: string | null;

  // Disputed Subscription Details
  disputedPeriodStartDate?: Date | null;
  disputedPeriodEndDate?: Date | null;

  // Trial Information
  trialStart?: Date | null;
  trialEnd?: Date | null;
  trialPeriodDays?: number | null;

  rawData?: Record<string, any> | null;
}

export interface IMongoSubscriptionMetadata {
  processor: string;
  source: string;
  disputeId: ObjectId;
}

export interface ISubscriptionMetadata {
  processor: string;
  source: string;
  disputeId: string;
}

export type MongoSubscriptionCacheDocument = IMongoCachedEnrichmentDocument<
  ISubscription | null,
  IMongoSubscriptionMetadata
>;
export type SubscriptionCacheDocument = ICachedEnrichmentDocument<
  ISubscription | null,
  ISubscriptionMetadata
>;
