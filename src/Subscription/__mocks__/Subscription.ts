import { ObjectId, WithId } from 'mongodb';
import {
    ISubscription,
    SubscriptionCacheDocument,
    SubscriptionSource,
} from '../ISubscription';

const getSubscription = (
    overrides: Partial<ISubscription> = {},
): ISubscription => ({
    ...overrides,
    accountId: 'Test account ID',
    chargeflowId: 'Test chargeflow ID',
    businessId: 'Test business ID',
    processorId: 'Test processor ID',
    subscriptionId: 'Test subscription ID',
    extSubscriptionId: 'Test external subscription ID',
    customerId: 'Test customer ID',
    paymentIntentId: 'Test payment intent ID',
    extCreatedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    source: SubscriptionSource.INTEGRATION,
    startDate: new Date(),
    endedAt: new Date(),
    canceledAt: new Date(),
    scheduledCancellationDate: new Date(),
    lastRenewedAt: new Date(),
    renewsAt: new Date(),
    name: 'Test name',
    status: 'active',
    priceModel: 'Test price model',
    frequency: 'day',
    frequencyCount: 1,
    cancellationComment: 'Test cancellation comment',
    cancellationReason: 'Test cancellation reason',
});

const getSubscriptionCacheDocument = (
    overrides: Partial<SubscriptionCacheDocument> = {},
): WithId<SubscriptionCacheDocument> => ({
    ...getSubscription(overrides),
    _id: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    dateCreated: new Date(),
    cacheKey: 'Test cache key',
    data: getSubscription(overrides),
    metadata: {
        disputeId: new ObjectId().toHexString(),
        processor: 'Test processor',
        source: 'Test source',
    },
});

export default {
    getSubscription,
    getSubscriptionCacheDocument,
};
