import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoSubscriptionCacheDocument } from './ISubscription';
import { ISubscriptionRepository } from './ISubscriptionRepository';
import { MongoSubscriptionRepository } from './MongoSubscriptionRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class SubscriptionRepositoryFactory {
    static create(
        mongoClient: MongoClient,
        dbName: string,
        collectionName: string,
    ): ISubscriptionRepository {
        const findByQueryOperation =
      new MongoFindByQueryOperation<MongoSubscriptionCacheDocument>(
          mongoClient,
          dbName,
          collectionName,
      );
        const updateOperation =
      new MongoUpdateOperation<MongoSubscriptionCacheDocument>(
          mongoClient,
          dbName,
          collectionName,
      );

        return new MongoSubscriptionRepository(
            findByQueryOperation,
            updateOperation,
        );
    }
}
