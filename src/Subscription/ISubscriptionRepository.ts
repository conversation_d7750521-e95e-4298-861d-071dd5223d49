import { ClientSession } from 'mongodb';
import { ISubscription, SubscriptionCacheDocument } from './ISubscription';

export interface ISubscriptionRepository {
  getSubscriptionByDisputeId: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<ISubscription | null>;

  getSubscriptionByDisputeIdWithCacheInfo: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<SubscriptionCacheDocument | null>;

  upsertSubscription: (
    disputeId: string,
    chargeflowId: string,
    source: string,
    processor: string,
    subscription: ISubscription | null,
    session?: ClientSession
  ) => Promise<void>;

  bumpSubscriptionLastUpdatedDate: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<void>;
}
