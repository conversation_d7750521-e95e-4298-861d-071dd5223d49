import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { SubscriptionRepositoryFactory } from './SubscriptionRepositoryFactory';
import { MongoSubscriptionRepository } from './MongoSubscriptionRepository';

describe(SubscriptionRepositoryFactory.name, () => {
    describe(SubscriptionRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = SubscriptionRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoSubscriptionRepository);
        });
    });
});
