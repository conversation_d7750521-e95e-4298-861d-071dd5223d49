import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { ISubscription, MongoSubscriptionCacheDocument, SubscriptionCacheDocument } from './ISubscription';
import { ISubscriptionRepository } from './ISubscriptionRepository';
import { MongoSubscriptionMapper } from './MongoSubscriptionMapper';

export class MongoSubscriptionRepository implements ISubscriptionRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<MongoSubscriptionCacheDocument>,
        private readonly updateOperation: IUpdateOperation<MongoSubscriptionCacheDocument>,
    ) {}

    async upsertSubscription(disputeId: string, chargeflowId: string, source: string, processor: string, subscription: ISubscription | null, session?: ClientSession) {
        const cacheKey = `disputeId:${disputeId}`;
        await this.updateOperation.upsert(
            { cacheKey, chargeflowId: new ObjectId(chargeflowId) },
            {
                $set: {
                    data: subscription,
                    cacheKey,
                    metadata: {
                        processor,
                        source,
                        disputeId: new ObjectId(disputeId),
                    },
                },
                $currentDate: {
                    dateUpdated: true,
                },
                $setOnInsert: {
                    chargeflowId: new ObjectId(chargeflowId),
                    dateCreated: new Date(),
                },
            },
            session,
        );
    }

    async bumpSubscriptionLastUpdatedDate(disputeId: string, chargeflowId: string, session?: ClientSession): Promise<void> {
        await this.updateOperation.updateWithFilter(
            {
                chargeflowId: new ObjectId(chargeflowId),
                'metadata.disputeId': new ObjectId(disputeId),
            },
            { $currentDate: { dateUpdated: true } },
            session,
        );
    }

    async getSubscriptionByDisputeIdWithCacheInfo(disputeId: string, chargeflowId: string, session?: ClientSession): Promise<SubscriptionCacheDocument | null> {
        const result = await this.findByQueryOperation.findOne(
            {
                chargeflowId: new ObjectId(chargeflowId),
                'metadata.disputeId': new ObjectId(disputeId),
            },
            session,
        );

        return result ? MongoSubscriptionMapper.fromMongoSubscriptionCacheDocument(result) : null;
    }

    async getSubscriptionByDisputeId(disputeId: string, chargeflowId: string, session?: ClientSession): Promise<ISubscription | null> {
        const result = await this.getSubscriptionByDisputeIdWithCacheInfo(disputeId, chargeflowId, session);
        return result?.data ?? null;
    }
}
