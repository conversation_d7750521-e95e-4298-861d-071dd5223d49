import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import {
    MongoSubscriptionCacheDocument,
    SubscriptionCacheDocument,
} from './ISubscription';

export class MongoSubscriptionMapper {
    static toMongoSubscriptionCacheDocument(
        subscriptionCacheDocument: SubscriptionCacheDocument,
    ): MongoSubscriptionCacheDocument {
        if (!subscriptionCacheDocument) {
            throw new Error('Subscription cache document is required for mapping');
        }

        return {
            ...subscriptionCacheDocument,
            _id: subscriptionCacheDocument._id
                ? new ObjectId(subscriptionCacheDocument._id)
                : undefined,
            chargeflowId: new ObjectId(subscriptionCacheDocument.chargeflowId),
            metadata: {
                ...subscriptionCacheDocument.metadata,
                disputeId: new ObjectId(subscriptionCacheDocument.metadata.disputeId),
            },
        };
    }

    static fromMongoSubscriptionCacheDocument(
        mongoSubscriptionCacheDocument: MongoWithId<MongoSubscriptionCacheDocument>,
    ): WithId<SubscriptionCacheDocument> {
        if (!mongoSubscriptionCacheDocument) {
            throw new Error('Subscription cache document not found');
        }

        return {
            ...mongoSubscriptionCacheDocument,
            _id: mongoSubscriptionCacheDocument._id.toHexString(),
            chargeflowId: mongoSubscriptionCacheDocument.chargeflowId.toHexString(),
            metadata: {
                ...mongoSubscriptionCacheDocument.metadata,
                disputeId:
          mongoSubscriptionCacheDocument.metadata.disputeId.toHexString(),
            },
        };
    }
}
