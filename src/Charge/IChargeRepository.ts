import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { ICharge } from './ICharge';

export interface IChargeRepository {
  findChargeByCaseId: (
    caseId: string,
    session?: ClientSession
  ) => Promise<WithId<ICharge> | null>;
  findChargesByDateRange: (
    chargeflowId: string,
    from: Date,
    to: Date,
    session?: ClientSession
  ) => Promise<WithId<ICharge>[] | null>;
}
