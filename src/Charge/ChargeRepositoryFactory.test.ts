import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { ChargeRepositoryFactory } from './ChargeRepositoryFactory';
import { MongoChargeRepository  } from './MongoChargeRepository';

describe(ChargeRepositoryFactory.name, () => {
    describe(ChargeRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = ChargeRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoChargeRepository);
        });
    });
});
