import { mock } from 'jest-mock-extended';
import { ClientSession, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { MongoChargeRepository } from './MongoChargeRepository';
import SettingsMocks from './__mocks__/Charge';
import { IMongoCharge } from './ICharge';
import { MongoChargeMapper } from './MongoChargeMapper';
describe(MongoChargeRepository.name, () => {
    let mongoSettingsRepository: MongoChargeRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoCharge>>();

    beforeEach(() => {
        mongoSettingsRepository = new MongoChargeRepository(
            findByQueryOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoChargeRepository.prototype.findChargeByCaseId.name, () => {
        it('returns shop settings object', async () => {
            expect.assertions(3);

            // GIVEN
            const caseId = 'caseId123';
            const Settings = SettingsMocks.getSettings();
            const session = mock<ClientSession>();

            const findByOneResult = MongoChargeMapper.toMongoCharge(Settings) as WithId<IMongoCharge>;

            findByQueryOperation.findOne.mockResolvedValue(findByOneResult);

            // WHEN
            const result = await mongoSettingsRepository.findChargeByCaseId(caseId, session);

            // THEN
            expect(result).toEqual(Settings);

            expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.findOne).toHaveBeenCalledWith({ caseId }, session);
        });
    });
});
