
import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoCharge, ICharge } from './ICharge';

export class MongoChargeMapper {
    static toMongoCharge(Charge: ICharge): IMongoCharge {
        if (!Charge) {
            throw new Error('Charge is required for mapping');
        }

        return {
            ...Charge,
            _id: Charge._id ? new ObjectId(Charge._id) : undefined,
            accountId: Charge.accountId ? new ObjectId(Charge.accountId) : undefined,
            chargeflow_id: Charge.chargeflow_id ? new ObjectId(Charge.chargeflow_id) : undefined,
        };
    }

    static fromMongoCharge(mongoCharge: MongoWithId<IMongoCharge>): WithId<ICharge> {
        if (!mongoCharge) {
            throw new Error('charge not found');
        }

        return {
            ...mongoCharge,
            _id: mongoCharge._id.toHexString(),
            accountId: mongoCharge.accountId?.toHexString(),
            chargeflow_id: mongoCharge.chargeflow_id?.toHexString(),
        };
    }

    static fromMongoCharges(mongoCharges: MongoWithId<IMongoCharge>[]): WithId<ICharge>[] {
        return mongoCharges.map(mongoCharge => ({
            ...mongoCharge,
            _id: mongoCharge._id.toHexString(),
            accountId: mongoCharge.accountId?.toHexString(),
            chargeflow_id: mongoCharge.chargeflow_id?.toHexString(),
        }));
    }
}
