import { ObjectId, WithId } from 'mongodb';
import { IMongoCharge, ICharge } from '../ICharge';

const defaultRawData = {
    id: 1,
    description: 'Raw Data Description',
    price: '10.00',
    created_at: '2022-01-01',
    currency: 'USD',
    balance_used: '5.00',
    balance_remaining: '5.00',
    risk_level: 1,
};

const defaultCharge = {
    dateCreated: new Date(),
    billingId: '123456789',
    billingDate: new Date(),
    billingStatus: 'paid',
    disputeId: '987654321',
    caseId: '987654321',
    disputeProcessor: 'processor',
    shopId: 'shop123',
    shopName: 'Shop Name',
    shopPlatform: 'platform',
    disputeRecoveredAmount: 100,
    successAmount: 200,
    chargedAmount: 300,
    promotionalDiscount: 50,
    promotionCode: 'ABC123',
    currency: 'USD',
    resolvedDate: new Date(),
    chargeSource: 'source',
    chargeType: 'type',
    chargeId: 123,
    recurringAppChargeId: 456,
    chargeDescription: 'Charge Description',
    rawData: defaultRawData,
};

const getSettings = (overrides: Partial<ICharge> = {}): WithId<ICharge> => ({
    _id: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    chargeflow_id: new ObjectId().toHexString(),
    ...defaultCharge,
    ...overrides,
});

const getMongoSettings = (overrides: Partial<IMongoCharge> = {}): WithId<IMongoCharge> => ({
    _id: new ObjectId(),
    accountId: new ObjectId(),
    chargeflow_id: new ObjectId(),
    ...defaultCharge,
    ...overrides,
});

export default {
    getSettings,
    getMongoSettings,
};
