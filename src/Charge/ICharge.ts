import { ObjectId, Document } from 'mongodb';

export interface RawData {
  id: number;
  description: string;
  price: string;
  created_at: string;
  currency: string;
  balance_used: string;
  balance_remaining: string;
  risk_level: number;
}

export interface IChargeBase {
  dateCreated: Date;
  billingId: string;
  billingDate: Date;
  billingStatus: string;
  disputeId: string;
  caseId: string;
  disputeProcessor: string;
  shopId: string;
  shopName: string;
  shopPlatform: string | null;
  disputeRecoveredAmount: number;
  successAmount: number;
  chargedAmount: number;
  promotionalDiscount: number | null;
  promotionCode: string | null;
  currency: string;
  resolvedDate: Date;
  chargeSource: string | null;
  chargeType: string;
  chargeId: number;
  recurringAppChargeId: number;
  chargeDescription: string;
  rawData: RawData;
}

export interface ICharge extends IChargeBase {
  _id?: string;
  chargeflow_id?: string;
  accountId?: string;
}

export interface IMongoCharge extends IChargeBase, Document {
  _id?: ObjectId;
  chargeflow_id?: ObjectId;
  accountId?: ObjectId;
}
