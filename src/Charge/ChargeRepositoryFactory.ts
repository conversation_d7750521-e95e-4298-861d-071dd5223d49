import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoCharge } from './ICharge';
import { IChargeRepository } from './IChargeRepository';
import { MongoChargeRepository } from './MongoChargeRepository';

export class ChargeRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IChargeRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoCharge>(mongoClient, dbName, collectionName);

        return new MongoChargeRepository(
            findByQueryOperation,
        );
    }
}
