import { ClientSession, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ICharge, IMongoCharge } from './ICharge';
import { IChargeRepository } from './IChargeRepository';
import { MongoChargeMapper } from './MongoChargeMapper';

export class MongoChargeRepository implements IChargeRepository {
    constructor(
    private readonly findByQueryOperation: IFindByQueryOperation<IMongoCharge>,
    ) {}

    async findChargeByCaseId(
        caseId: string,
        session?: ClientSession,
    ): Promise<WithId<ICharge> | null> {
        const result = await this.findByQueryOperation.findOne(
            { caseId },
            session,
        );

        return result ? MongoChargeMapper.fromMongoCharge(result) : null;
    }

    async findChargesByDateRange(
        chargeflowId: string,
        from: Date,
        to: Date,
    ): Promise<WithId<ICharge>[] | null> {
        const filter = { dateCreated: { $gte: from, $lte: to }, chargeflowId: new ObjectId(chargeflowId) };
        const result = await this.findByQueryOperation.find(
            filter,
            0,
            0,
        );

        return result ? MongoChargeMapper.fromMongoCharges(result.items) : null;
    }
}
