import { IMongoPlatformUser, IPlatformUser, IPlatformUsersBase } from '../IPlatformUser';
import { ObjectId, WithId } from 'mongodb';

const defaultPlatformUsers: IPlatformUsersBase = {
    email: '<EMAIL>',
    name: 'Test User',
    permission: 'admin',
};

const getPlatformUsers = (overrides: Partial<IPlatformUser> = {}): WithId<IPlatformUser> => ({
    _id: new ObjectId().toHexString(),
    platformId: new ObjectId().toHexString(),
    ...defaultPlatformUsers,
    ...overrides,
});

const getMongoPlatformUsers = (overrides: Partial<IMongoPlatformUser> = {}): WithId<IMongoPlatformUser> => ({
    _id: new ObjectId(),
    platformId: new ObjectId(),
    ...defaultPlatformUsers,
    ...overrides,
});

export default {
    getPlatformUsers,
    getMongoPlatformUsers,
};
