import { Document, ObjectId } from 'mongodb';

export interface IPlatformUsersBase {
    email: string;
    name?: string;
    permission?: string;
    createdAt?: Date | string;
    updatedAt?: Date | string | null;
}

export interface IPlatformUser extends IPlatformUsersBase {
    _id?: string;
    platformId?: string;
}

export interface IMongoPlatformUser extends IPlatformUsersBase, Document {
    _id?: ObjectId;
    platformId?: ObjectId;
}
