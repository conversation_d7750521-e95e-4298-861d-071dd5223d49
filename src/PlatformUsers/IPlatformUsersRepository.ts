import { ClientSession } from 'mongodb';
import { IPlatformUser } from './IPlatformUser';
import { WithId } from '../shared/helper-types';

export interface IPlatformUsersRepository {
    create: (platformUser: IPlatformUser, session?: ClientSession) => Promise<WithId<IPlatformUser>>;
    getUserByEmail: (email: string, session?: ClientSession) => Promise<WithId<IPlatformUser> | null>;
    getPlatformId: (email: string, session?: ClientSession) => Promise<string | null>;
    getByPlatformId: (platformId: string, session?: ClientSession) => Promise<WithId<IPlatformUser>[]>;
}
