import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IPlatformUsersRepository } from './IPlatformUsersRepository';
import { IMongoPlatformUser, IPlatformUser } from './IPlatformUser';
import { WithId } from '../shared/helper-types';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { convertObjectIdToString, convertStringToObjectId } from '../shared/helper-functions';

export class MongoPlatformUsersRepository implements IPlatformUsersRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoPlatformUser>,
        private readonly addOperation: IAddOperation<IMongoPlatformUser>,
    ) {
    }

    async create(platformUser: IPlatformUser, session?: ClientSession): Promise<WithId<IPlatformUser>> {
        const insertData = convertStringToObjectId<IMongoPlatformUser>(platformUser);
        insertData.createdAt ??= new Date();
        insertData.updatedAt ??= null;
        const result = await this.addOperation.add(insertData, session);
        return convertObjectIdToString<WithId<IPlatformUser>>(result);
    }

    async getUserByEmail(email: string, session?: ClientSession): Promise<WithId<IPlatformUser> | null> {
        const res = await this.findByQueryOperation.findOne({ email }, session);
        return res ? convertObjectIdToString<WithId<IPlatformUser>>(res) : null;
    }

    async getPlatformId(email: string, session?: ClientSession): Promise<string | null> {
        const res = await this.getUserByEmail(email, session);
        return res ? res?.platformId as string : null;
    }

    async getByPlatformId(platformId: string, session?: ClientSession): Promise<WithId<IPlatformUser>[]> {
        const res = await this.findByQueryOperation.find(
            { platformId: new ObjectId(platformId) },
            undefined,
            undefined,
            session,
        );
        return res.items.map(convertObjectIdToString<WithId<IPlatformUser>>);
    }
}
