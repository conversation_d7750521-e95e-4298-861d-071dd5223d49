import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoPlatformUsersRepository } from './MongoPlatformUsersRepository';
import { IMongoPlatformUser } from './IPlatformUser';
import { IPlatformUsersRepository } from './IPlatformUsersRepository';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';

export class PlatformUsersRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IPlatformUsersRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoPlatformUser>(mongoClient, dbName, collectionName);
        const addOperation = new MongoAddOperation<IMongoPlatformUser>(mongoClient, dbName, collectionName);

        return new MongoPlatformUsersRepository(
            findByQueryOperation,
            addOperation,
        );
    }
}
