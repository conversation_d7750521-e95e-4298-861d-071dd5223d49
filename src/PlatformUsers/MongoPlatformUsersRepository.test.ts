import { mock } from 'jest-mock-extended';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, ObjectId } from 'mongodb';
import { MongoPlatformUsersRepository } from './MongoPlatformUsersRepository';
import { IMongoPlatformUser } from './IPlatformUser';
import platformMocks from './__mocks__/PlatformUsers';
import addOperationMocks from '../shared/operation/AddOperation/__mocks__/MongoAddOperation';

describe(MongoPlatformUsersRepository.name, () => {
    let mongoPlatformUsersRepository: MongoPlatformUsersRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoPlatformUser>>();
    const mockAddOperation = addOperationMocks.getAddOperation<IMongoPlatformUser>();

    beforeEach(() => {
        mongoPlatformUsersRepository = new MongoPlatformUsersRepository(
            findByQueryOperation,
            mockAddOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoPlatformUsersRepository.prototype.getUserByEmail.name, () => {
        it('returns a single platform, if found', async () => {
            expect.assertions(2);

            const mockData = platformMocks.getPlatformUsers();
            const session = mock<ClientSession>();

            const platformId = new ObjectId();
            findByQueryOperation.findOne.mockResolvedValue({ _id: new ObjectId(), email: mockData.email, platformId });

            const result = await mongoPlatformUsersRepository.getUserByEmail(mockData.email, session);

            expect(result?.email)
                .toEqual(mockData.email);
            expect(findByQueryOperation.findOne)
                .toHaveBeenCalledTimes(1);
        });

        it('returns null, if platform is not found', async () => {
            expect.assertions(2);

            const session = mock<ClientSession>();
            findByQueryOperation.findOne.mockResolvedValue(null);

            const result = await mongoPlatformUsersRepository.getUserByEmail('', session);

            expect(result)
                .toBeNull();
            expect(findByQueryOperation.findOne)
                .toHaveBeenCalledTimes(1);
        });
    });
});
