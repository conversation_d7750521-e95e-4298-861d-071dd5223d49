import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { PlatformUsersRepositoryFactory } from './PlatformUsersRepositoryFactory';
import { MongoPlatformUsersRepository } from './MongoPlatformUsersRepository';

describe(PlatformUsersRepositoryFactory.name, () => {
    describe(PlatformUsersRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            const result = PlatformUsersRepositoryFactory.create(mongoClient, dbName, collectionName);

            expect(result).toBeInstanceOf(MongoPlatformUsersRepository);
        });
    });
});
