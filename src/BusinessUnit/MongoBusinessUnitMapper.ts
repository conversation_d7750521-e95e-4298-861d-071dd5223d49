import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { Id } from '../shared/helper-types';
import { IBusinessUnit, IMongoBusinessUnit } from './IBusinessUnit';

export class MongoBusinessUnitMapper {
    static toMongoBusinessUnit(businessUnit: IBusinessUnit): IMongoBusinessUnit {
        if (!businessUnit) {
            throw new Error('BusinessUnit is required for mapping');
        }

        return {
            ...businessUnit,
            _id: new ObjectId(),
            chargeflowId: new ObjectId(businessUnit.chargeflowId),
            accountId: new ObjectId(businessUnit.accountId),
            dateCreated: new Date(),
        };
    }

    static fromMongoBusinessUnit(businessUnit: MongoWithId<IMongoBusinessUnit>): Id {
        if (!businessUnit) {
            throw new Error('BusinessUnit is required for mapping');
        }
        return {
            _id: businessUnit._id.toHexString(),
        };
    }

    static mapMongoBusinessUnit(businessUnit: MongoWithId<IMongoBusinessUnit>): IBusinessUnit {
        if (!businessUnit) {
            throw new Error('BusinessUnit is required for mapping');
        }
        return {
            ...businessUnit,
            _id: businessUnit._id.toHexString(),
            accountId: businessUnit.accountId.toHexString(),
            chargeflowId: businessUnit.chargeflowId.toHexString(),
        };
    }
}
