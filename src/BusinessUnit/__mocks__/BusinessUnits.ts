import { ObjectId } from 'mongodb';
import { IBusinessUnit, IMongoBusinessUnit } from '../IBusinessUnit';

const getBusinessUnit = (overrides?: Partial<IBusinessUnit>): IBusinessUnit => ({
    chargeflowId: new ObjectId('66187980cacc5e60aacf4130').toHexString(),
    accountId: new ObjectId('66187980cacc5e60aacf4131').toHexString(),
    name: 'Test Business Unit',
    url: 'tbu.io',
    country: 'Israel',
    ...overrides,
});

const getMongoBusinessUnit = (overrides?: Partial<IMongoBusinessUnit>): IMongoBusinessUnit => ({
    _id: new ObjectId(),
    chargeflowId: new ObjectId('66187980cacc5e60aacf4130'),
    accountId: new ObjectId('66187980cacc5e60aacf4131'),
    name: 'Test Business Unit',
    url: 'tbu.io',
    country: 'Israel',
    dateCreated: new Date(),
    dateUpdated: new Date(),
    ...overrides,
});

const defaultBusinessUnit = getBusinessUnit();
const defaultMongoBusinessUnit = getMongoBusinessUnit();

export default {
    getBusinessUnit,
    getMongoBusinessUnit,
    defaultBusinessUnit,
    defaultMongoBusinessUnit,
};
