import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { BusinessUnitRepositoryFactory } from './BusinessUnitRepositoryFactory';
import { MongoBusinessUnitRepository } from './MongoBusinessUnitRepository';

describe(BusinessUnitRepositoryFactory.name, () => {
    describe(BusinessUnitRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = BusinessUnitRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoBusinessUnitRepository);
        });
    });
});
