import { Document, ObjectId } from 'mongodb';
import { IAuditableFields } from '../shared/helper-types';

export interface IBusinessUnitBase extends Document {
    name?: string,
    url?: string,
    country?: string,
}

export interface IBusinessUnit extends IBusinessUnitBase {
    chargeflowId: string,
    accountId: string,
}

export interface IMongoBusinessUnit extends IBusinessUnitBase, IAuditableFields, Document {
    _id: ObjectId,
    chargeflowId: ObjectId,
    accountId: ObjectId,
}
