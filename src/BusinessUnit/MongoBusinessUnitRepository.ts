import { ClientSession, ObjectId } from 'mongodb';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { Id } from '../shared/helper-types';
import { IBusinessUnitRepository } from './IBusinessUnitRepository';
import { IBusinessUnit, IBusinessUnitBase, IMongoBusinessUnit } from './IBusinessUnit';
import { MongoBusinessUnitMapper } from './MongoBusinessUnitMapper';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';

export class MongoBusinessUnitRepository implements IBusinessUnitRepository {
    constructor(
        private readonly addOperation: IAddOperation<IMongoBusinessUnit>,
        private readonly updateOperation: IUpdateOperation<IMongoBusinessUnit>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoBusinessUnit>,
    ) {}

    async add(
        businessUnit: IBusinessUnit, session?: ClientSession,
    ): Promise<Id> {
        return MongoBusinessUnitMapper.fromMongoBusinessUnit(
            await this.addOperation.add(MongoBusinessUnitMapper.toMongoBusinessUnit(businessUnit), session),
        );
    }

    async update(
        id: string, businessUnitData: IBusinessUnitBase, session?: ClientSession,
    ): Promise<Id | null> {
        const update = {
            ...businessUnitData,
            dateUpdated: new Date(),
        };
        return MongoBusinessUnitMapper.fromMongoBusinessUnit(
            await this.updateOperation.update({ _id: new ObjectId(id), ...update }, session));
    }

    async findOneIdByChargeflowIdOrThrow(
        chargeflowId: string, session?: ClientSession,
    ): Promise<Id> {
        const result = await this.findByQueryOperation
            .findOne({ 'chargeflowId': new ObjectId(chargeflowId) }, session);
        if (!result) {
            throw new Error(`BusinessUnit not found for chargeflowId: ${chargeflowId}`);
        }
        return MongoBusinessUnitMapper.fromMongoBusinessUnit(result);
    }

    async findByChargeflowId(
        chargeflowId: string, session?: ClientSession, page: number = 1, pageSize: number = 10,
    ): Promise<IBusinessUnit[]> {
        const result = await this.findByQueryOperation
            .find({ 'chargeflowId': new ObjectId(chargeflowId) }, page, pageSize, session);
        return result.items.map(MongoBusinessUnitMapper.mapMongoBusinessUnit);
    }

    async findOneIdByChargeflowIdAndAccountId(
        chargeflowId: string, accountId: string, session?: ClientSession,
    ): Promise<Id> {
        const result = await this.findByQueryOperation
            .findOne({
                'chargeflowId': new ObjectId(chargeflowId),
                'accountId': new ObjectId(accountId),
            }, session);
        if (!result) {
            throw new Error(`BusinessUnit not found for chargeflowId: ${chargeflowId} and ${accountId}`);
        }
        return MongoBusinessUnitMapper.fromMongoBusinessUnit(result);
    }

    async findOneById(id: string, session?: ClientSession): Promise<IBusinessUnit> {
        const result = await this.findByQueryOperation
            .findOne({ _id: new ObjectId(id) }, session);
        if (!result) {
            throw new Error(`BusinessUnit not found with id: ${id}`);
        }
        return MongoBusinessUnitMapper.mapMongoBusinessUnit(result);
    }
}
