import { MongoClient } from 'mongodb';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { IBusinessUnitRepository } from './IBusinessUnitRepository';
import { IMongoBusinessUnit } from './IBusinessUnit';
import { MongoBusinessUnitRepository } from './MongoBusinessUnitRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';

export class BusinessUnitRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IBusinessUnitRepository {
        const addOperation = new MongoAddOperation<IMongoBusinessUnit>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoBusinessUnit>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoBusinessUnit>(mongoClient, dbName, collectionName);
        return new MongoBusinessUnitRepository(addOperation, updateOperation, findByQueryOperation);
    }
}
