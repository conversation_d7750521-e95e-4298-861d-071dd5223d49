import { MongoClient, ObjectId } from 'mongodb';
import { MongoBusinessUnitRepository } from './MongoBusinessUnitRepository';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { BusinessUnitCollectionName } from './constants';
import { BusinessUnitRepositoryFactory } from './BusinessUnitRepositoryFactory';
import { IBusinessUnitRepository } from './IBusinessUnitRepository';
import businessUnits from './__mocks__/BusinessUnits';
import { IMongoBusinessUnit } from './IBusinessUnit';

describe(MongoBusinessUnitRepository.name, () => {
    let repository: IBusinessUnitRepository;
    let mongoServer: MongoMemoryServer;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repository = BusinessUnitRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            BusinessUnitCollectionName,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName).collection<IMongoBusinessUnit>(BusinessUnitCollectionName).deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    it('should add a businessUnit and return created businessUnitId', async () => {
        // GIVEN
        const businessUnit = businessUnits.getBusinessUnit();
        const expectedMongoBusinessUnit = businessUnits.getMongoBusinessUnit({
            dateCreated: stubbedDate,
            dateUpdated: undefined,
        });

        // WHEN
        const businessUnitId = await repository.add(businessUnit);

        // THEN
        expect(businessUnitId).not.toBeNull();
        const mongoBusinessUnit =  await mongoClient.db(ChargeflowDbName)
            .collection<IMongoBusinessUnit>(BusinessUnitCollectionName)
            .findOne({ _id: new ObjectId(businessUnitId._id) });
        expect(mongoBusinessUnit).toEqual({
            ...expectedMongoBusinessUnit,
            _id: new ObjectId(businessUnitId._id),
        });
    });

    it('should update a businessUnit and return businessUnitId', async () => {
        // GIVEN
        const businessUnit = businessUnits.getBusinessUnit();
        const updatedBusinessUnit = {
            name: 'updatedName',
            url: 'updatedUrl.io',
            country: 'updatedCountry',
        };
        const expectedMongoBusinessUnit = businessUnits.getMongoBusinessUnit({
            name: updatedBusinessUnit.name,
            url: updatedBusinessUnit.url,
            country: updatedBusinessUnit.country,
            dateCreated: stubbedDate,
            dateUpdated: new Date('2024-04-12T00:00:00Z'),
        });
        const businessUnitId = await repository.add(businessUnit);

        // WHEN
        await repository.update(businessUnitId._id.toString(), updatedBusinessUnit);

        // THEN
        const mongoBusinessUnit =  await mongoClient.db(ChargeflowDbName)
            .collection<IMongoBusinessUnit>(BusinessUnitCollectionName)
            .findOne({ _id: new ObjectId(businessUnitId._id) });
        expect(mongoBusinessUnit).toEqual({
            ...expectedMongoBusinessUnit,
            _id: new ObjectId(businessUnitId._id),
        });
    });

    it('should return a businessUnitId', async () => {
        // GIVEN
        const businessUnit = businessUnits.getBusinessUnit();
        await repository.add(businessUnit);

        // WHEN
        const businessUnitId = await repository.findOneIdByChargeflowIdOrThrow(businessUnit.chargeflowId.toString());

        // THEN
        expect(businessUnitId).not.toBeNull();
        expect(businessUnitId?._id).not.toBeNull();
    });

    it('should throw exception when a businessUnit not found', async () => {
        // GIVEN
        const businessUnit = businessUnits.getBusinessUnit();

        // THEN
        await expect(repository.findOneIdByChargeflowIdOrThrow(businessUnit.chargeflowId.toString()))
            .rejects
            .toThrow(`BusinessUnit not found for chargeflowId: ${businessUnit.chargeflowId}`);
    });

    it('should return created business units', async () => {
        // GIVEN
        const businessUnit = businessUnits.getBusinessUnit();
        const addedBusinessUnitId = await repository.add(businessUnit);

        // AND
        const expectedBusinessUnit = {
            _id: addedBusinessUnitId._id,
            ...businessUnits.getBusinessUnit(),
            dateCreated: stubbedDate,
        };

        // WHEN
        const result = await repository.findByChargeflowId(businessUnit.chargeflowId.toString());

        // THEN
        expect(result).not.toEqual([]);
        expect(result[0]).toEqual(expectedBusinessUnit);
    });

    it('should return an empty array', async () => {
        // WHEN
        const result = await repository.findByChargeflowId('66187980cacc5e60aacf4130');

        // THEN
        expect(result).toEqual([]);
    });
});
