import { ClientSession } from 'mongodb';
import { Id } from '../shared/helper-types';
import { IBusinessUnit, IBusinessUnitBase } from './IBusinessUnit';
export interface IBusinessUnitRepository {
    add:(
        businessUnit: IBusinessUnit, clientSession?: ClientSession,
    ) => Promise<Id>

    update: (
        id: string, businessUnitData: IBusinessUnitBase, session?: ClientSession,
    ) => Promise<Id | null>

    /** Introduced for legacy purposes.
     * The chargeflow survey onboarding process first creates a business unit and then populates the data.
     * In such a scenario, we can assume that initially, there is only one default business unit.
     * This method will be used to fetch the default business unit.
     */
    findOneIdByChargeflowIdOrThrow: (chargeflowId: string, clientSession?: ClientSession,
    ) => Promise<Id>

    findByChargeflowId: (chargeflowId: string, clientSession?: ClientSession, page?: number, pageSize?: number,
    ) => Promise<IBusinessUnit[]>

    findOneIdByChargeflowIdAndAccountId: (chargeflowId: string, accountId:string, clientSession?: ClientSession,
    ) => Promise<Id>

    findOneById: (id: string, clientSession?: ClientSession,
    ) => Promise<IBusinessUnit>
}
