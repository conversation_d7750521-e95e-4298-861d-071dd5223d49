import { ClientSession, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IMongoEnrichments, IEnrichments, IEnrichmentDisputeEnahncements } from './IEnrichments';
import { IEnrichmentsRepository } from './IEnrichmentsRepository';
import { MongoEnrichmentsMapper } from './MongoEnrichmentsMapper';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';

export class MongoEnrichmentsRepository implements IEnrichmentsRepository {
    constructor(
        private readonly getByIdOperation: IGetByIdOperation<IMongoEnrichments>,
        private readonly updateOperation: IUpdateOperation<IMongoEnrichments>,
    ) {}

    async updateEnhancementsByDisputeId(
        disputeId: string,
        disputeEnhancements: IEnrichmentDisputeEnahncements,
        clientSession?: ClientSession,
    ): Promise<WithId<IEnrichments> | null> {
        const id = new ObjectId(disputeId);
        const result = this.updateOperation.upsert(
            { _id: id },
            {
                $set: { 'date_updated': new Date(), disputeEnhancements },
                $setOnInsert: { 'date_created': new Date(), _id: id },
            },
            clientSession,
        );

        return result ? MongoEnrichmentsMapper.fromMongoEnrichments(await result) : null;
    }

    async getByDisputeId(
        disputeId: string,
        clientSession?: ClientSession,
    ): Promise<WithId<IEnrichments> | null> {
        const id = new ObjectId(disputeId);
        const result = await this.getByIdOperation.get(id, clientSession);

        return result ? MongoEnrichmentsMapper.fromMongoEnrichments(result) : null;
    }
}
