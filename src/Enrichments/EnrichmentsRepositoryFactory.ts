import { MongoClient } from 'mongodb';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { IMongoEnrichments } from './IEnrichments';
import { IEnrichmentsRepository } from './IEnrichmentsRepository';
import { MongoEnrichmentsRepository } from './MongoEnrichmentsRepository';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';

export class EnrichmentsRepositoryFactory {
    static create(
        mongoClient: MongoClient,
        dbName: string,
        collectionName: string,
    ): IEnrichmentsRepository {

        const getByIdOperation = new MongoGetByIdOperation<IMongoEnrichments>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoEnrichments>( mongoClient, dbName, collectionName);

        return new MongoEnrichmentsRepository(getByIdOperation, updateOperation);
    }
}
