import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoEnrichments, IEnrichments } from './IEnrichments';

export class MongoEnrichmentsMapper {
    static toMongoEnrichments(Enrichments: IEnrichments): IMongoEnrichments {
        if (!Enrichments) {
            throw new Error('Enrichments is required for mapping');
        }

        return {
            ...Enrichments,
            _id: Enrichments._id ? new ObjectId(Enrichments._id) : undefined,
        };
    }

    static fromMongoEnrichments(
        mongoEnrichments: MongoWithId<IMongoEnrichments>,
    ): WithId<IEnrichments> {
        if (!mongoEnrichments) {
            throw new Error('Enrichments not found');
        }

        return {
            ...mongoEnrichments,
            _id: mongoEnrichments._id.toHexString(),
        };
    }
}
