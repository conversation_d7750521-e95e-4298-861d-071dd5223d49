import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IEnrichmentDisputeEnahncements, IEnrichments, IMongoEnrichments } from './IEnrichments';
import { MongoEnrichmentsRepository } from './MongoEnrichmentsRepository';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import Enrichments from './__mocks__/Enrichments';
import { MongoEnrichmentsMapper } from './MongoEnrichmentsMapper';

jest
    .useFakeTimers()
    .setSystemTime(new Date('2020-01-01'));

describe(MongoEnrichmentsRepository.name, () => {
    let repository: MongoEnrichmentsRepository;
    const mockGetByIdOperation = mock<IGetByIdOperation<IMongoEnrichments>>();
    const mockUpdateOperation = mock<IUpdateOperation<IMongoEnrichments>>();
    const session = mock<ClientSession>();

    beforeEach(() => {
        repository = new MongoEnrichmentsRepository(mockGetByIdOperation, mockUpdateOperation);
    });

    afterEach(jest.clearAllMocks);

    describe(MongoEnrichmentsRepository.prototype.getByDisputeId.name, () => {
        it('should get dispute enrichments', async () => {
            // GIVEN
            const id = new ObjectId();
            const enrichments = Enrichments.getEnrichments({ _id: id.toHexString() });
            const session = mock<ClientSession>();
            const getResult: WithId<IMongoEnrichments> =
                MongoEnrichmentsMapper.toMongoEnrichments(enrichments) as WithId<IMongoEnrichments>;
            const expectedResult: IEnrichments = {
                ...enrichments,
                _id: id.toHexString(),
            };

            mockGetByIdOperation.get.mockResolvedValue(getResult);

            // WHEN
            const result = await repository.getByDisputeId(id.toHexString(), session);

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(id, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe(MongoEnrichmentsRepository.prototype.updateEnhancementsByDisputeId.name, () => {
        it('should save enrichments and return the result', async () => {
            const disputeId = new ObjectId().toHexString();
            const disputeEnhancements: IEnrichmentDisputeEnahncements = { };
            const withId: WithId<IMongoEnrichments> = { disputeEnhancements, _id: new ObjectId(disputeId) };

            mockUpdateOperation.upsert.mockResolvedValue(withId);

            const result = await repository.updateEnhancementsByDisputeId(disputeId, disputeEnhancements, session);

            expect(mockUpdateOperation.upsert).toHaveBeenCalledWith(
                { _id: new ObjectId(disputeId) },
                {
                    $set: { date_updated: new Date(), disputeEnhancements },
                    $setOnInsert: { date_created: new Date(), _id: new ObjectId(disputeId) },
                },
                session,
            );
            expect(result).toEqual({ disputeEnhancements, _id: withId._id.toHexString() });
        });

        it('should return null if upsert fails', async () => {
            const disputeId = new ObjectId().toHexString();
            const mongoEnrichments = { };
            const disputeEnhancements: IEnrichmentDisputeEnahncements = { };
            const withId: WithId<IMongoEnrichments> = { disputeEnhancements, _id: new ObjectId(disputeId) };

            mockUpdateOperation.upsert.mockResolvedValue(withId);

            const result = await repository.updateEnhancementsByDisputeId(disputeId, mongoEnrichments, session);
            expect(result).toEqual({ disputeEnhancements, _id: withId._id.toHexString() });
        });
    });
});
