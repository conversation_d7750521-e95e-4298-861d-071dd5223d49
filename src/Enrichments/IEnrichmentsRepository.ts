import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IEnrichmentDisputeEnahncements, IEnrichments } from './IEnrichments';

export interface IEnrichmentsRepository {
  updateEnhancementsByDisputeId: (
      disputeId: string,
      disputeEnhancements: IEnrichmentDisputeEnahncements,
      clientSession?: ClientSession,
  ) => Promise<WithId<IEnrichments> | null>;

  getByDisputeId: (
      disputeId: string,
      clientSession?: ClientSession,
  ) => Promise<WithId<IEnrichments> | null>;
}
