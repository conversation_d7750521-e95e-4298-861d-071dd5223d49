import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { EnrichmentsRepositoryFactory } from './EnrichmentsRepositoryFactory';
import { MongoEnrichmentsRepository } from './MongoEnrichmentsRepository';

describe(EnrichmentsRepositoryFactory.name, () => {
    describe(EnrichmentsRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = EnrichmentsRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoEnrichmentsRepository);
        });
    });
});
