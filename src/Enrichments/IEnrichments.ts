import { ObjectId, Document } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IUnifiedDisputeObject } from '../Dispute/Types';
import { CustomerIdentityResult, GeoflowEnrichmentOutputData, ShippingEnrichmentOutputData, SubscriptionDataResponse } from '@chargeflow-team/events-infra';
import { IFraudAnalysis } from '../FraudAnalysis/IFraudAnalysis';
import { IPastOrder } from '../PastOrder/IPastOrder';
import { IShop } from '../Shop/IShop';
import { ISettings } from '../Settings/ISettings';

export interface IEnrichedDisputeData {
  dispute: IUnifiedDisputeObject;
  geoflow?: GeoflowEnrichmentOutputData;
  subscriptions?: SubscriptionDataResponse;
  customerIdentity?: CustomerIdentityResult;
  fraudAnalysis?: WithId<IFraudAnalysis>;
  shipping?: ShippingEnrichmentOutputData;
  pastOrders?: WithId<IPastOrder>[];
  shop?: WithId<IShop>;
  settings?: WithId<ISettings>;
}

export interface IFraudPositiveIndicators {
  cvcClassificationGood: boolean;
  avsClassification: boolean;
  doesBillingMatchShipping: boolean;
  doesBillingAddress1MatchShipping: boolean;
  doesBillingZipMatchShipping: boolean;
  singleTransactionAttempt: boolean;
  ipToShippingDistance: number;
  ipToShippingUnit: string;
  isIpToShippingDistanceWithin15Km: boolean;
  isIpToShippingDistanceAvailable: boolean;
  isIpCountryMatchBillingCountry: boolean;
  isInternetConnectionLowRisk: boolean;
  hasIpAddress: boolean;
  ipAddress: string;
  customerNameMatchEmail: boolean;
  passed3DSecure: boolean;
  isAnyIndicatorPositive: boolean;
}

export interface IEnrichmentDisputeEnahncements {
  disputeId?: string;
  daysToResponseDue?: number;
  orderMetadata?: {
    hasPastRefunds: boolean;
  };
  companyNameNormalized?: string;
  usdAmount?: number;
  companyName?: string;
  pastOrders?: IPastOrder[];
  shop?: IShop;
  supportEmail?: string;
  settings?: ISettings;
  fraudAnalysis?: {
    goodIpClassification: boolean;
  };
  fraudPositiveIndicators?: IFraudPositiveIndicators;
  enrichments?: {
    subscriptions?: SubscriptionDataResponse;
    geoLocation?: GeoflowEnrichmentOutputData;
    customerIdentity?: CustomerIdentityResult;
    shipping?: ShippingEnrichmentOutputData;
  };
  lastCheckpoint?: [];
  lastCheckpointSubtag?: string;
}

export interface IEnrichmentsBase {
  disputeEnhancements: IEnrichmentDisputeEnahncements;
}

export interface IEnrichments extends IEnrichmentsBase {
  _id?: string;
}

export interface IMongoEnrichments extends IEnrichmentsBase, Document {
  _id?: ObjectId;
}
