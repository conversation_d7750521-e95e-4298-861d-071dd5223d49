import { ObjectId } from 'mongodb';
import { Customer, ICustomers, IMongoCustomers } from '../ICustomers';
import { WithId } from '../../shared/helper-types';

const getCustomer = (overrides?: Partial<WithId<ICustomers>>): WithId<ICustomers> => {
    const customer = {
        _id: new ObjectId().toHexString(),
        name: '<PERSON>',
        email: '<EMAIL>',
        status: true,
        dateCreated: new Date(),
        dateUpdated: new Date(),
        ...overrides,
    };
    return new Customer(
        customer._id,
        customer.name,
        customer.email,
        customer.status,
        customer.dateCreated,
        customer.dateUpdated,
    );
};

const getMongoCustomer = (overrides?: Partial<IMongoCustomers>): IMongoCustomers => ({
    _id: new ObjectId(),
    name: '<PERSON>',
    email: '<EMAIL>',
    status: true,
    date_created: new Date(),
    date_updated: new Date(),
    ...overrides,
});

const defaultCustomer = getCustomer();
const defaultMongoCustomer = getMongoCustomer();

export default {
    getCustomer,
    getMongoCustomer,
    defaultCustomer,
    defaultMongoCustomer,
};
