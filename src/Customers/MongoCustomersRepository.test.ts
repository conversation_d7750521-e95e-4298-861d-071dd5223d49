import { MongoClient, ObjectId } from 'mongodb';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { CustomersCollectionName } from './constants';
import customers from './__mocks__/Customers';
import { ICustomersRepository } from './ICustomersRepository';
import { CustomersRepositoryFactory } from './CustomersRepositoryFactory';
import { IMongoCustomers } from './ICustomers';
import { MongoCustomersRepository } from './MongoCustomersRepository';

describe(MongoCustomersRepository.name, () => {
    let repository: ICustomersRepository;
    let mongoServer: MongoMemoryServer;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repository = CustomersRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            CustomersCollectionName,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName).collection<IMongoCustomers>(CustomersCollectionName).deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    it('should return existing customer data', async () => {
        // GIVEN
        const customerDataToCreate = customers.getMongoCustomer();
        const createdCustomer = await mongoClient.db(ChargeflowDbName).collection<IMongoCustomers>(CustomersCollectionName).insertOne(customerDataToCreate);

        // WHEN
        const customer = await repository.getCustomerDataByEmail(customerDataToCreate.email);

        // THEN
        expect(customer).not.toBeNull();
        expect(customer).toEqual({
            ...customers.getCustomer({ _id: createdCustomer.insertedId.toHexString() }),
        });
    });

    it('should return null for not existing customer', async () => {
        // WHEN
        const customer = await repository.getCustomerDataByEmail('<EMAIL>');

        // THEN
        expect(customer).toBeNull();
    });

    it('should return customer data by ID', async () => {
        const customerDataToCreate = customers.getMongoCustomer();
        const createdCustomer = await mongoClient.db(ChargeflowDbName).collection<IMongoCustomers>(CustomersCollectionName).insertOne(customerDataToCreate);
        const customerId = createdCustomer.insertedId.toHexString();

        const customer = await repository.getCustomerById(customerId);

        expect(customer).not.toBeNull();
        expect(customer).toEqual({
            ...customers.getCustomer({ _id: customerId }),
        });
    });

    it('should return null when customer ID does not exist', async () => {
        const nonExistentId = new ObjectId().toHexString();

        const customer = await repository.getCustomerById(nonExistentId);

        expect(customer).toBeNull();
    });

    it('should throw an error when invalid ID format is provided', async () => {
        const invalidId = 'invalid-id-format';

        await expect(repository.getCustomerById(invalidId)).rejects.toThrow();
    });
});
