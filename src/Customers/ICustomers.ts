import { ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';

export interface ICustomersBase {
    name: string,
    email: string,
    status: boolean,
}
export class Customer implements WithId<ICustomers> {
    constructor(
        public _id: string,
        public name: string,
        public email: string,
        public status: boolean,
        public dateCreated: Date,
        public dateUpdated?: Date,
    ) {}

    getFirstName(): string {
        const trimmedName = this.name.trim();
        if (!trimmedName) {
            return '';
        }
        const parts = trimmedName.split(/\s+/);
        return parts.slice(0, -1).join(' ').trim() || this.name;
    }

    getLastName(): string {
        const parts = this.name.trim().split(' ');
        return parts.length > 1 ? parts[parts.length - 1] : '';
    }
}

export interface ICustomers extends ICustomersBase {
    dateCreated: Date,
    dateUpdated?: Date,
    getFirstName(): string,
    getLastName(): string,
}

export interface IMongoCustomers extends ICustomersBase {
    _id: ObjectId;
    date_created: Date,
    date_updated?: Date,
}
