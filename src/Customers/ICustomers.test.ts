import { Customer } from './ICustomers';

describe('ICustomers', () => {
    it.each([
        [ '<PERSON>', '<PERSON>' ],
        [ '<PERSON>', '<PERSON>' ],
        [ '<PERSON>', '<PERSON>' ],
        [ '', '' ],
        [ '', '    ' ],
        [ '<PERSON>', '  <PERSON>  ' ],
    ])('should return firstName %s for given name %s', (expectedFirstName:string, name: string) => {
        const customer = new Customer('1', name, '<EMAIL>', true, new Date(), new Date());
        expect(customer.getFirstName()).toEqual(expectedFirstName);
    });

    it.each([
        [ '<PERSON><PERSON>', '<PERSON>' ],
        [ '<PERSON><PERSON>', '<PERSON>' ],
        [ '', '<PERSON>' ],
        [ '', '' ],
        [ '', '    ' ],
        [ '<PERSON><PERSON>', '  <PERSON>  ' ],
    ])('should return lastName %s for given name %s', (expectedLastName:string, name: string) => {
        const customer = new Customer('1', name, '<EMAIL>', true, new Date(), new Date());
        expect(customer.getLastName()).toEqual(expectedLastName);
    });
});
