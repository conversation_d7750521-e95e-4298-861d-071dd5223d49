import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoCustomers } from './ICustomers';
import { MongoCustomersRepository } from './MongoCustomersRepository';
import { ICustomersRepository } from './ICustomersRepository';

export class CustomersRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): ICustomersRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoCustomers>(mongoClient, dbName, collectionName);

        return new MongoCustomersRepository(findByQueryOperation);
    }
}
