import {  WithId as MongoWithId } from 'mongodb';
import { Customer, ICustomers, IMongoCustomers } from './ICustomers';
import { WithId } from '../shared/helper-types';

export class MongoCustomersMapper {

    static fromMongoCustomers(customer: MongoWithId<IMongoCustomers> | null): WithId<ICustomers> | null {
        if (!customer) {
            return null;
        }
        return new Customer(
            customer._id.toHexString(),
            customer.name,
            customer.email,
            customer.status,
            customer.date_created,
            customer.date_updated);
    }
}
