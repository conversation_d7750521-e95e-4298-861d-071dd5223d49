import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ICustomersRepository } from './ICustomersRepository';
import { ICustomers, IMongoCustomers } from './ICustomers';
import { MongoCustomersMapper } from './MongoCustomersMapper';

export class MongoCustomersRepository implements ICustomersRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoCustomers>,
    ) {}

    async getCustomerDataByEmail(email: string, clientSession?: ClientSession): Promise<ICustomers | null> {
        return MongoCustomersMapper.fromMongoCustomers(
            await this.findByQueryOperation.findOne({ email }, clientSession),
        );
    }

    async getCustomerById(id: string, clientSession?: ClientSession): Promise<ICustomers | null> {
        return MongoCustomersMapper.fromMongoCustomers(
            await this.findByQueryOperation.findOne({ _id: new ObjectId(id) }, clientSession),
        );
    }
}
