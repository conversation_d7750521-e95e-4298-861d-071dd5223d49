import { MongoDisputeRepository } from './MongoDisputeRepository';
import { MongoClient } from 'mongodb';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoBulkAddOperation } from '../shared/operation/BulkAddOperation/MongoBulkAddOperation';
import { IMongoUnifiedDisputeObject } from './Types';
import { IDisputeRepository } from './IDisputeRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';

export class DisputeRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IDisputeRepository {
        const addOperation = new MongoAddOperation<IMongoUnifiedDisputeObject>(mongoClient, dbName, collectionName);
        const bulkAddOperation = new MongoBulkAddOperation<IMongoUnifiedDisputeObject>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoUnifiedDisputeObject>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoUnifiedDisputeObject>(mongoClient, dbName, collectionName);
        const getByIdOperation = new MongoGetByIdOperation<IMongoUnifiedDisputeObject>(mongoClient, dbName, collectionName);

        return new MongoDisputeRepository(
            addOperation,
            bulkAddOperation,
            findByQueryOperation,
            getByIdOperation,
            updateOperation,
        );
    }
}
