import { ObjectId } from 'mongodb';
import { IUnifiedDisputeObject } from '../Types';
import { ChargeFlowDisputeStatus, DisputeReason, DisputeStage, DisputeStatusSource } from '../enums';
import { Source } from '../../shared/enums';

const getUDO = (overrides: Partial<IUnifiedDisputeObject> = {}): IUnifiedDisputeObject => ({
    extensions: {},
    isBillingActive: true,
    chargeflowId: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    dateCreated: new Date(),
    dateUpdated: new Date(),
    liveMode: true,
    shopName: 'Shop Name',
    lastDisputeStatus: {
        status: ChargeFlowDisputeStatus.NeedsResponse,
        dateCreated: new Date(),
        source: DisputeStatusSource.Fetch,
        statusDate: new Date(),
        processorStatus: null,
    },
    dispute: {
        id: new ObjectId().toHexString(),
        dateReceived: new Date(),
        dateCreated: new Date(),
        processor: Source.PayPal,
        source: Source.PayPal,
        stage: DisputeStage.Inquiry,
        amount: 66,
        currency: 'USD',
        reason: DisputeReason.Fraud,
        processorReason: 'Some reason',
        disputeStatuses: [],
        actions: [],
        rawData: {
            dispute: {},
            webhooks: [],
        },
        dateUpdated: null, // Modify this line to explicitly allow null values
        transactionId: null,
        reasonCode: null,
        responseDue: null,
        closedDate: null,
        submittedAt: null,
        submittedCount: null,
        disputeFee: null,
        statementDescriptor: null,
        mid: null,
        isRefundable: null,
        intentionalFraud: null,
    },
    chargeflow: {
        templates: [],
        evidences: [],
        cfActions: {
            handleByChargeflow: true,
            chargeflowPerformedAction: true,
        },
        resolved: null,
        chargeScore: null,
        submitted: {},
    },
    shopId: null,
    transaction: null,
    shippingData: [],
    intentionalFraud: false,
    order: null,
    orderDate: null,
    orderName: null,
    ...overrides,
});

const defaultUDO = getUDO();

export default {
    getUDO,
    defaultUDO,
};
