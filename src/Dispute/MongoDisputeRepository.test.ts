import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { MongoDisputeRepository } from './MongoDisputeRepository';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IBulkUpdateResponse, IChargeflowResolved, IDisputeStatus, ILinkingTypeMetadata, IMongoUnifiedDisputeObject, IUnifiedDisputeObject } from './Types';
import disputeMocks from './__mocks__/Disputes';
import mock from 'jest-mock-extended/lib/Mock';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { MongoDisputeMapper } from './MongoDisputeMapper';
import { faker } from '@faker-js/faker';
import { Source } from '../shared/enums';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import updateOperationMocks from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';
import { UpdateDisputeStatusToPreventedPayload } from '@chargeflow-team/events-infra';
import { ChargeFlowDisputeStatus, DisputeStatusSource } from './enums';

const MOCK_DATE = new Date('2030-01-01');
jest
    .useFakeTimers()
    .setSystemTime(MOCK_DATE);

describe(MongoDisputeRepository.name, () => {
    let mockAddOperation: jest.Mocked<IAddOperation<IMongoUnifiedDisputeObject>>;
    let mockBulkAddOperation: jest.Mocked<IBulkAddOperation<IMongoUnifiedDisputeObject>>;
    let mockFindByQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoUnifiedDisputeObject>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoUnifiedDisputeObject>>;
    let mockGetByIdOperation: jest.Mocked<IGetByIdOperation<IMongoUnifiedDisputeObject>>;
    let repository: MongoDisputeRepository;

    beforeEach(() => {
        mockAddOperation = {
            add: jest.fn(),
        } as jest.Mocked<IAddOperation<IMongoUnifiedDisputeObject>>;

        mockBulkAddOperation = {
            bulkAdd: jest.fn(),
        } as jest.Mocked<IBulkAddOperation<IMongoUnifiedDisputeObject>>;

        mockFindByQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoUnifiedDisputeObject>();
        mockUpdateOperation = updateOperationMocks.getUpdateOperation<IMongoUnifiedDisputeObject>();

        mockGetByIdOperation = {
            get: jest.fn(),
        } as jest.Mocked<IGetByIdOperation<IMongoUnifiedDisputeObject>>;

        mockGetByIdOperation = {
            get: jest.fn(),
        } as jest.Mocked<IGetByIdOperation<IMongoUnifiedDisputeObject>>;

        repository = new MongoDisputeRepository(
            mockAddOperation,
            mockBulkAddOperation,
            mockFindByQueryOperation,
            mockGetByIdOperation,
            mockUpdateOperation,
        );
    });

    describe('add', () => {
        it('should add a dispute and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const session = mock<ClientSession>();
            const addResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
                shopId: id.toHexString(),
            };
            mockAddOperation.add.mockResolvedValue(addResult);

            // WHEN
            const result = await repository.add(dispute, session);

            // THEN
            expect(mockAddOperation.add).toHaveBeenCalledWith(expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('get', () => {
        it('should get a dispute', async () => {
            // GIVEN
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const session = mock<ClientSession>();
            const getResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
                shopId: dispute.shopId,
            };

            mockGetByIdOperation.get.mockResolvedValue(getResult);

            // WHEN
            const result = await repository.get(id.toHexString(), session);

            // THEN
            expect(mockGetByIdOperation.get).toHaveBeenCalledWith(id, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('update', () => {
        it('should update a dispute and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            }) as WithId<IUnifiedDisputeObject>;
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
                shopId: id.toHexString(),
            };
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateAndPushHistoricalData({
                ...dispute,
                chargeflow: undefined,
            },{ }, session);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('update evidence', () => {
        it('should update a dispute evidence status and return the dispute', async () => {
            // GIVEN
            const id = new ObjectId();
            const evidenceId = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = dispute;

            mockUpdateOperation.updateWithFilterAndArrayFilters.mockResolvedValue(updateResult);
            // WHEN
            const result = await repository.updateEvidence(id.toHexString(), evidenceId.toHexString(), { isCompelling: false }, session);

            // THEN
            expect(
                mockUpdateOperation.updateWithFilterAndArrayFilters,
            ).toHaveBeenCalledWith(
                { _id: id },
                {
                    $set: {
                        'chargeflow.evidences.$[evidence].isCompelling': false,
                        dateUpdated: MOCK_DATE,
                    },
                },
                [ { 'evidence.evidenceId': evidenceId } ],
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('add evidence', () => {
        it('should add a dispute evidence status and return the dispute', async () => {
            // GIVEN
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = dispute;

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);
            const update = { isCompelling: false };
            const updateQuery = {
                '$addToSet': {
                    'chargeflow.evidences': {
                        'isCompelling': false,
                    },
                },
                '$set': {
                    dateUpdated: MOCK_DATE,
                },
            };

            // WHEN
            const result = await repository.addEvidence(id.toHexString(), update, session);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: id },
                updateQuery,
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('update action status', () => {
        it('should update a dispute action status and return the dispute', async () => {
            // GIVEN
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = dispute;
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            // WHEN
            const result = await repository.updateActionStatus(id.toHexString(), 'test', 'test', session);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('bulkAdd', () => {
        it('should bulk add disputes and return the results', async () => {
            // GIVEN
            const id = new ObjectId();
            const disputes: IUnifiedDisputeObject[] = [ disputeMocks.getUDO({
                _id: new ObjectId(id).toHexString(),
                shopId: new ObjectId(id).toHexString(),
            }) ];
            const session = mock<ClientSession>();
            const bulkAddResult: WithId<IMongoUnifiedDisputeObject>[] = MongoDisputeMapper.toMongoDisputes(disputes) as WithId<IMongoUnifiedDisputeObject>[];
            const expectedResult: IUnifiedDisputeObject[] = disputes;
            mockBulkAddOperation.bulkAdd.mockResolvedValue(bulkAddResult);

            // WHEN
            const result = await repository.bulkAdd(disputes, session);

            // THEN
            expect(mockBulkAddOperation.bulkAdd).toHaveBeenCalledWith(expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });

        it('should fail due to future date in object dateCreated', async () => {
            const id = new ObjectId();
            const maxDate = new Date('12/31/99999');
            const disputes: IUnifiedDisputeObject[] = [ disputeMocks.getUDO({
                _id: new ObjectId(id).toHexString(),
                shopId: new ObjectId(id).toHexString(),
                dateCreated: maxDate,
            }) ];
            console.log(`disputes: ${JSON.stringify(disputes)}`);
            const session = mock<ClientSession>();

            await expect(repository.bulkAdd(disputes, session)).rejects.toThrow('Dispute dateCreated cannot be in the future');
        });

        it('should fail due to future date in dispute dateCreated', async () => {
            const id = new ObjectId();
            const maxDate = new Date('12/31/99999');
            const disputes: IUnifiedDisputeObject[] = [ disputeMocks.getUDO({
                _id: new ObjectId(id).toHexString(),
                shopId: new ObjectId(id).toHexString(),
            }) ];
            disputes[0].dispute.dateCreated = maxDate;
            console.log(`disputes: ${JSON.stringify(disputes)}`);
            const session = mock<ClientSession>();

            await expect(repository.bulkAdd(disputes, session)).rejects.toThrow('Dispute dateCreated cannot be in the future');
        });
    });

    describe('getByChargeflowId', () => {
        it('should find a disputes by chargeflowId and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const cfId = new ObjectId();
            const session = mock<ClientSession>();
            const dispute = MongoDisputeMapper.toMongoDispute(disputeMocks.getUDO({
                chargeflowId: cfId.toHexString(),
                shopId: id.toHexString(),
                _id: id.toHexString(),
            })) as WithId<IMongoUnifiedDisputeObject>;

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...dispute,
                    _id: id,
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };
            const expectedResult: IUnifiedDisputeObject[] = [ MongoDisputeMapper.fromMongoDispute(dispute) ] ;
            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByChargeflowId(cfId.toHexString(), session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { 'chargeflowId': expect.anything() },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getByCaseIdAndProcessorName', () => {
        it('should find a dispute by case id and processor name and return the result', async () => {
            // GIVEN
            const id = new ObjectId();
            const caseId = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId,
                    processor: processor,
                },
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute),
                    _id: id,
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
            };
            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getByCaseIdAndProcessorName(caseId, processor, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                { 'dispute.id': caseId, 'dispute.processor': processor },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });
    describe('addDisputeOrder', () => {
        it('should add a dispute order and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = dispute;
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.addDisputeOrder(id.toHexString(), {
                name: 'test',
                amount: 0,
                dateReceived: new Date(),
                dateCreated: new Date(),
                source: Source.ShopifyPayments,
                products: [],
            }, session);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });
        it('should add a dispute order name when order is null', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute({ ...dispute, orderName: 'N/A' }) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = { ...dispute, orderName: 'N/A' };
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.addDisputeOrder(id.toHexString(), null, session);
            const expectedQuery = { '$set': { orderName: 'N/A', orderDate: null, order: null, dateUpdated: MOCK_DATE } };

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expectedQuery, session);
            expect(result).toEqual(expectedResult);
        });
        it('should throw an error if disputeId is missing', async () => {
            const session = mock<ClientSession>();

            await expect(repository.addDisputeOrder('', {
                name: 'test',
                amount: 0,
                dateReceived: new Date(),
                dateCreated: new Date(),
                source: Source.ShopifyPayments,
                products: [],
            }, session)).rejects.toThrow('Missing disputeId');
        });
    });

    describe('addDisputeTransaction', () => {
        it('should add a dispute transaction and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = dispute;
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.addDisputeTransaction(id.toHexString(), {
                id: 'test',
                dateReceived: new Date(),
                dateCreated: new Date(),
                dateUpdated: new Date(),
                amount: 0,
                currency: 'USD',
                source: Source.ShopifyPayments,
                receiptUrl: 'test',
                checks: null,
                billing: null,
                customerName: 'test',
                customerEmail: 'test',
                orderId: 'test',
                risk: null,
                statementDescriptor: 'test',
                paymentDetails: null,
                rawData: {
                    transaction: null,
                    webhooks: [],
                },
                type: 'test',
            }, session);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expect.anything(), session);
            expect(result).toEqual(expectedResult);
        });
        it('should throw an error if disputeId is missing', async () => {
            const session = mock<ClientSession>();

            await expect(repository.addDisputeTransaction('', {
                id: 'test',
                dateReceived: new Date(),
                dateCreated: new Date(),
                dateUpdated: new Date(),
                amount: 0,
                currency: 'USD',
                source: Source.ShopifyPayments,
                receiptUrl: 'test',
                checks: null,
                billing: null,
                customerName: 'test',
                customerEmail: 'test',
                orderId: 'test',
                risk: null,
                statementDescriptor: 'test',
                paymentDetails: null,
                rawData: {
                    transaction: null,
                    webhooks: [],
                },
                type: 'test',
            }, session)).rejects.toThrow('Missing disputeId');
        });
    });

    describe('updateExtensionResponseByDisputeId', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.updateExtensionResponseByDisputeId('', {}, Source.AfterpayOrClearpay)).rejects.toThrow('Missing disputeId');
        });

        it('should update the extension response for a dispute and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute({ ...dispute, extensions: { shopify: { response: 'test' } } }) as WithId<IMongoUnifiedDisputeObject>;

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.updateExtensionResponseByDisputeId(id.toHexString(), 'test', Source.ShopifyPayments, session);
            const expectedResult = {
                ...dispute,
                extensions: {
                    shopify: {
                        response: 'test',
                    },
                },
            };

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, { $set: { 'extensions.shopify.response': 'test', dateUpdated: MOCK_DATE } }, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateExtensionAttachmentsByDisputeId', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.updateExtensionAttachmentsByDisputeId('', {
                id: 'test',
                fileKey: 'fileKey',
                fileName: 'fileName',
                evidenceId: 'evidenceId',
            }, Source.AfterpayOrClearpay)).rejects.toThrow('Missing disputeId');
        });

        it('should update the extension attachments for a dispute and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const attachmentObject = {
                id: 'test',
                fileKey: 'fileKey',
                fileName: 'fileName',
                evidenceId: 'evidenceId',
            };
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute({ ...dispute, extensions: { shopify:{ attachments: attachmentObject } } }) as WithId<IMongoUnifiedDisputeObject>;

            const expectedResult: IUnifiedDisputeObject = { ...dispute, extensions: { shopify: { attachments: attachmentObject } } };
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.updateExtensionAttachmentsByDisputeId(id.toHexString(), attachmentObject, Source.ShopifyPayments, session);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, { $set: { 'extensions.shopify.attachments': attachmentObject, dateUpdated: MOCK_DATE } }, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateTransaction', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.updateTransaction('', {})).rejects.toThrow('Missing disputeId');
        });

        it('updates transaction for a given disputeId and returns the updated dispute', async () => {
            // GIVEN
            const disputeId = new ObjectId();
            const transaction = { paymentDetails: { cardHolderName: 'test' } };

            const dispute = disputeMocks.getUDO( {
                _id: disputeId.toHexString(),
            }) as WithId<IUnifiedDisputeObject>;

            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            mockUpdateOperation.updateWithFilter.mockResolvedValueOnce(updateResult);
            const session = mock<ClientSession>();

            // WHEN
            const result = await repository.updateTransaction(disputeId.toHexString(), transaction, session);

            // THEN
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: new ObjectId(disputeId) },
                { $set: { 'transaction.paymentDetails':  { cardHolderName: 'test' }, dateUpdated: MOCK_DATE } },
                session,
            );
            expect(result).toEqual(dispute);
        });
    });

    describe('getExtensionsByDisputeId', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.getExtensionsByDisputeId('')).rejects.toThrow('Missing disputeId');
        });

        it('returns opening note attachments for a given disputeId', async () => {
            // GIVEN
            const disputeId = 'someDisputeId';
            const expectedAttachments = { 'extensions': [] };
            const findWithAggregation = jest.fn().mockResolvedValueOnce({ items: [ expectedAttachments ] });
            mockFindByQueryOperation.findWithAggregation = findWithAggregation;

            // WHEN
            const result = await repository.getExtensionsByDisputeId(disputeId);

            // THEN
            expect(findWithAggregation).toHaveBeenCalledWith(
                [ { $match: { 'dispute.id': disputeId } },
                    { $project: { 'extensions': 1, _id: 1 } } ],
                1,
                1,
                undefined,
            );
            expect(result).toEqual(expectedAttachments);
        });
    });

    describe('updateChargeflowChecks', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.updateChargeflowChecks('', {})).rejects.toThrow('Missing disputeId');
        });

        it('updates chargeflow checks for a given disputeId and returns the updated dispute', async () => {
            // GIVEN
            const disputeId = new ObjectId();
            const orderShippingAddress = { street: 'someStreet', zipcode: 'someZip', address1: 'someAddress1' };
            const billing = { items: [ { transaction: { billing: { street: 'someStreet', zip: 'someZip', address1: 'someAddress1' } } } ] };
            const expectedDispute: IMongoUnifiedDisputeObject = {
                ...disputeMocks.getUDO(),
                _id: disputeId,
                shopId: new ObjectId(),
                chargeflowId: new ObjectId(),
                accountId: new ObjectId(),
                dateCreated: new Date(),
                dateUpdated: new Date(),
                chargeflow: {
                    templates: [],
                    cfActions: null,
                    resolved:  null,
                    chargeScore:  null,
                    checks: {
                        doesBillingMatchShipping: false,
                        doesBillingAddress1MatchShipping: false,
                        doesBillingZipMatchShipping: true,
                    },
                    evidences: [],
                },
            };
            mockFindByQueryOperation.findWithAggregation = jest.fn().mockResolvedValueOnce(billing);
            mockUpdateOperation.updateWithFilter = jest.fn().mockResolvedValueOnce(expectedDispute);
            const session = mock<ClientSession>();

            const result = await repository.updateChargeflowChecks(disputeId.toHexString(), orderShippingAddress, session);

            // THEN
            expect(mockFindByQueryOperation.findWithAggregation).toHaveBeenCalledWith(
                [ { $match: { _id: disputeId } },
                    { $project: { 'transaction.billing': 1, _id: 1 } } ],
            );
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: disputeId },
                {
                    $set: {
                        'chargeflow.checks.doesBillingMatchShipping': false,
                        'chargeflow.checks.doesBillingAddress1MatchShipping': false,
                        'chargeflow.checks.doesBillingZipMatchShipping': true,
                        dateUpdated: MOCK_DATE,
                    },
                },
                session,
            );
            const expectedResult = MongoDisputeMapper.fromMongoDispute({ ...expectedDispute, _id: disputeId });
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getManyByCaseIdAndProcessorName', () => {
        it('should find a list of disputes by case id and processor name and return the result', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const caseId1 = faker.string.alphanumeric(10);
            const caseId2 = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute1 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId1,
                    processor: processor,
                },
                chargeflowId: chargeflowId.toHexString(),
            });
            const dispute2 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId2,
                    processor: processor,
                },
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute1),
                    _id: id1,
                },
                {
                    ...MongoDisputeMapper.toMongoDispute(dispute2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...dispute1,
                    _id: id1.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                },
                {
                    ...dispute2,
                    _id: id2.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getManyByCaseIdChargeflowIdProcessor([ caseId1, caseId2 ], chargeflowId.toHexString(), processor, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'dispute.id' : { $in: [ caseId1, caseId2 ] },
                    'dispute.processor': processor,
                    'chargeflowId': chargeflowId,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getManyByChargeflowIdAndProcessorIdFromLast180Days', () => {
        it('should return a list of disputes with chargeflowId and processorId', async () => {
            const chargeflowId = new ObjectId();
            const processorId = new ObjectId();
            const disputeId = new ObjectId();
            const session = mock<ClientSession>();
            const currentDate = new Date();
            const daysAgo180 = new Date(currentDate.setDate(currentDate.getDate() - 180));

            const dispute = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: disputeId.toHexString(),
                    dateCreated: new Date(currentDate.setDate(currentDate.getDate() - 10)),
                    processorId: processorId.toHexString(),
                },
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute),
                    _id: disputeId,
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...dispute,
                    _id: disputeId.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 1,
                    totalItems: 1,
                } };
            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);
            const result = await repository.getManyByChargeflowIdAndProcessorIdFromLast180Days(chargeflowId.toHexString(), processorId.toHexString(), session);

            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'dispute.processorId': processorId.toHexString(),
                    'dispute.dateCreated': { $gte: daysAgo180 },
                    'chargeflowId': chargeflowId,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateIsActionRequired', () => {
        it('should update the dispute and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();

            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
            };
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.updateIsActionRequired(id.toHexString(), session);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, expect.anything(), session);
            expect(result).toEqual(expectedResult);
        } );
    });
    describe('update order products image storage path', () => {
        it('should update a dispute order product image storage path', async () => {
            // GIVEN
            const id = new ObjectId();
            const imageUrl = faker.internet.url();
            const orderProductImageStoragePath = 's3://some-bucket/some-path/image.jpg';
            const session = mock<ClientSession>();
            const dispute = disputeMocks.getUDO({
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;
            const expectedResult: IUnifiedDisputeObject = dispute;

            mockUpdateOperation.updateWithFilterAndArrayFilters.mockResolvedValue(updateResult);
            // WHEN
            const result = await repository.updateOrderProductsImageStoragePath(id.toHexString(), imageUrl, orderProductImageStoragePath, session);

            // THEN
            expect(mockUpdateOperation.updateWithFilterAndArrayFilters)
                .toHaveBeenCalledWith(
                    { _id: id },
                    { $set: { 'order.products.$[product].imageStoragePath': orderProductImageStoragePath, dateUpdated: MOCK_DATE } },
                    [ { 'product.imageUrl': imageUrl } ],
                    session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateResolvedObject', () => {
        it('throws an error when no disputeId is provided', async () => {
            await expect(repository.updateResolvedObject('', {} as IChargeflowResolved)).rejects.toThrow('Missing disputeId');
        });

        it('should update the resolved object for a dispute and return the result', async () => {
            const id = new ObjectId();
            const dispute = {
                ...disputeMocks.getUDO(),
                _id: id.toHexString(),
            };
            const session = mock<ClientSession>();
            const resolvedDate = new Date();
            const resolvedObject = {
                resolvedDate: resolvedDate,
                resolveId: '123',
                isResolved: true,
                isWon: true,
                amountWon: 123,
                successFee: 0,
            };
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute({ ...dispute, chargeflow: { ...dispute.chargeflow, resolved: resolvedObject } }) as WithId<IMongoUnifiedDisputeObject>;

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.updateResolvedObject(id.toHexString(), resolvedObject, session);
            const expectedResult = {
                ...dispute,
                chargeflow: {
                    ...dispute.chargeflow,
                    resolved: resolvedObject,
                },
            };

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith({ _id: id }, { $set: { 'chargeflow.resolved': resolvedObject, dateUpdated: MOCK_DATE } }, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getOpenDisputesByChargeflowIdAndProcessor', () => {

        it('throws an error when no chargeflowId is provided', async () => {
            await expect(repository.getOpenDisputesByChargeflowIdAndProcessor('', faker.helpers.enumValue(Source))).rejects.toThrow('Missing chargeflowId');
        });

        it('throws an error when no processor is provided', async () => {
            await expect(repository.getOpenDisputesByChargeflowIdAndProcessor('chargeflowId', '' as Source)).rejects.toThrow('Missing processor');
        });

        it('should find a list of open disputes by chargeflow id and processor name and return the result', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const caseId1 = faker.string.alphanumeric(10);
            const caseId2 = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute1 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId1,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    evidences: [],
                    cfActions: null,
                    templates: [],
                    chargeScore: null,
                },
                chargeflowId: chargeflowId.toHexString(),
            });
            const dispute2 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId2,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    evidences: [],
                    cfActions: null,
                    templates: [],
                    chargeScore: null,
                },
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute1),
                    _id: id1,
                },
                {
                    ...MongoDisputeMapper.toMongoDispute(dispute2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...dispute1,
                    _id: id1.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                },
                {
                    ...dispute2,
                    _id: id2.toHexString(),
                    chargeflowId: chargeflowId.toHexString(),
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getOpenDisputesByChargeflowIdAndProcessor(chargeflowId.toHexString(), processor, 1, 10, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'dispute.processor': processor,
                    'chargeflowId': chargeflowId,
                    'chargeflow.resolved.isResolved': false,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });

        it('should return empty list when there are no open disputes found by chargeflow id and processor name, but returns', async () => {
            // GIVEN
            const chargeflowId = new ObjectId();
            const caseId1 = faker.string.alphanumeric(10);
            const caseId2 = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute1 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId1,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: true,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    evidences: [],
                    cfActions: null,
                    templates: [],
                    chargeScore: null,
                },
                chargeflowId: chargeflowId.toHexString(),
            });
            const dispute2 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId2,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: true,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    evidences: [],
                    cfActions: null,
                    templates: [],
                    chargeScore: null,
                },
                chargeflowId: chargeflowId.toHexString(),
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await repository.getOpenDisputesByChargeflowIdAndProcessor(chargeflowId.toHexString(), processor, 1, 10, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'dispute.processor': processor,
                    'chargeflowId': chargeflowId,
                    'chargeflow.resolved.isResolved': false,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
            expect(result.items).toHaveLength(0);
            expect(result.items).not.toContain(dispute1);
            expect(result.items).not.toContain(dispute2);
        });
    });
    describe('stampSuccessRateOnDispute', () => {
        it('should update the rate on the dispute', async () => {
            // Arrange
            const id = new ObjectId();
            const fee = 10;

            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
                shopId: id.toHexString(),
            };
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            // Mock the updateWithFilter method
            mockUpdateOperation.updateWithFilter = jest.fn().mockResolvedValue(updateResult);

            // Act
            const updatedDispute = await repository.stampSuccessRateOnDispute(id.toHexString(), fee);

            // Assert
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: expect.any(ObjectId) },
                { $set: { 'chargeflow.submitted.successRate': fee, dateUpdated: MOCK_DATE } },
                undefined,
            );
            expect(updatedDispute).toEqual(expectedResult);
        });

        it('should throw an error if disputeId is missing', async () => {
            // Arrange
            const fee = 10;

            // Act and Assert
            await expect(repository.stampSuccessRateOnDispute('', fee)).rejects.toThrow('Missing disputeId');
        });
    });

    describe('get disputes for deactivation', () => {
        it('should throw an error processorId is missing', async () => {
            await expect(repository.getDisputesForDeactivation('' ))
                .rejects.toThrow('Missing processorId');
        });

        it('should find disputes in processing state that should be handled', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const processorId = new ObjectId();
            const caseId1 = faker.string.alphanumeric(10);
            const caseId2 = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute1 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId1,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    cfActions: {
                        handleByChargeflow: true,
                        chargeflowPerformedAction: false,
                    },
                    evidences: [],
                    templates: [],
                    chargeScore: null,
                },
            });
            const dispute2 = disputeMocks.getUDO({
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId2,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    cfActions: {
                        handleByChargeflow: true,
                        chargeflowPerformedAction: false,
                    },
                    evidences: [],
                    templates: [],
                    chargeScore: null,
                },
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute1),
                    _id: id1,
                },
                {
                    ...MongoDisputeMapper.toMongoDispute(dispute2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...dispute1,
                    _id: id1.toHexString(),
                },
                {
                    ...dispute2,
                    _id: id2.toHexString(),
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result =
                await repository.getDisputesForDeactivation(processorId.toHexString(), 1, 10, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'dispute.processorId': processorId.toHexString(),
                    'lastDisputeStatus.status': { $in: [
                        'needs_response',
                        'warning_needs_response',
                        'under_review',
                        'warning_under_review',
                        'warning_awaiting_response',
                    ] },
                    'chargeflow.cfActions.handleByChargeflow': true,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('get disputes for reactivation', () => {
        it('should throw an error chargeflowId is missing', async () => {
            await expect(repository.getDisputesForReactivation('', ''))
                .rejects.toThrow('Missing chargeflowId');
        });

        it('should throw an error processorId is missing', async () => {
            await expect(repository.getDisputesForReactivation(new ObjectId().toHexString(), ''))
                .rejects.toThrow('Missing processorId');
        });

        it('should find disputes in processing state that should be handled', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const chargeflowId = new ObjectId();
            const processorId = new ObjectId();
            const caseId1 = faker.string.alphanumeric(10);
            const caseId2 = faker.string.alphanumeric(10);
            const processor = faker.helpers.enumValue(Source);
            const session = mock<ClientSession>();
            const dispute1 = disputeMocks.getUDO({
                chargeflowId: chargeflowId.toHexString(),
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId1,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    cfActions: {
                        handleByChargeflow: true,
                        chargeflowPerformedAction: false,
                    },
                    evidences: [],
                    templates: [],
                    chargeScore: null,
                },
            });
            const dispute2 = disputeMocks.getUDO({
                chargeflowId: chargeflowId.toHexString(),
                dispute: {
                    ...disputeMocks.defaultUDO.dispute,
                    id: caseId2,
                    processor: processor,
                },
                chargeflow: {
                    resolved: {
                        isResolved: false,
                        isWon: false,
                        resolvedDate: null,
                        amountWon: 0,
                        successFee: 0,
                        resolveId: null,
                    },
                    cfActions: {
                        handleByChargeflow: true,
                        chargeflowPerformedAction: false,
                    },
                    evidences: [],
                    templates: [],
                    chargeScore: null,
                },
            });

            const findByQueryResult: IPaginatedFindByQueryResult<IMongoUnifiedDisputeObject> = {
                items: [ {
                    ...MongoDisputeMapper.toMongoDispute(dispute1),
                    _id: id1,
                },
                {
                    ...MongoDisputeMapper.toMongoDispute(dispute2),
                    _id: id2,
                },
                ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                },
            };
            const expectedResult: {items: IUnifiedDisputeObject[], pagination: IPaginationCalculatorResult} = {
                items: [ {
                    ...dispute1,
                    _id: id1.toHexString(),
                },
                {
                    ...dispute2,
                    _id: id2.toHexString(),
                } ],
                pagination: {
                    totalPages: 1,
                    currentPage: 1,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    itemsPerPage: 10,
                    totalItems: 1,
                } };

            mockFindByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result =
                await repository.getDisputesForReactivation(chargeflowId.toHexString(), processorId.toHexString(), 1, 10, session);

            // THEN
            expect(mockFindByQueryOperation.find).toHaveBeenCalledWith(
                {
                    'chargeflowId': chargeflowId,
                    'dispute.processorId': processorId.toHexString(),
                    'lastDisputeStatus.status': { $in: [
                        'needs_response',
                        'warning_needs_response',
                        'under_review',
                        'warning_under_review',
                        'warning_awaiting_response',
                    ] },
                    'chargeflow.cfActions.handleByChargeflow': false,
                },
                expect.any(Number),
                expect.any(Number),
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('update handleByChargeflow flag on disputes', () => {
        it('should throw an error disputeIds are missing', async () => {
            await expect(repository.updateHandleByChargeflow([], true ))
                .rejects.toThrow('Missing dispute ids to update');
        });

        it('should set handleByChargeflow field to false on disputes', async () => {
            // GIVEN
            const id1 = new ObjectId();
            const id2 = new ObjectId();
            const session = mock<ClientSession>();

            const disputeIds = [ id1.toHexString(), id2.toHexString() ];
            const bulkOperation = disputeIds.map( id => {
                return {
                    updateOne: {
                        filter: { _id: new ObjectId(id) },
                        update: { $set: { 'chargeflow.cfActions.handleByChargeflow': false,
                            'dateUpdated': expect.any(Date)  } },
                        upsert: true,
                    },
                };
            });

            const expectedResult = { result: { modifiedCount: 2 }, notUpdatedDisputes: [] } as unknown as IBulkUpdateResponse;

            mockUpdateOperation.bulkUpdate.mockResolvedValue(expectedResult);

            // WHEN
            const result =
            await repository.updateHandleByChargeflow([ id1.toHexString(), id2.toHexString() ], false, session);

            // THEN
            expect(mockUpdateOperation.bulkUpdate).toHaveBeenCalledWith(bulkOperation, session);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updatePromotion', () => {
        it('should update the promotional fields on the dispute', async () => {
            // Arrange
            const id = new ObjectId();
            const dispute = disputeMocks.getUDO( {
                _id: id.toHexString(),
                shopId: id.toHexString(),
            });
            const expectedResult: IUnifiedDisputeObject = {
                ...dispute,
                _id: id.toHexString(),
                shopId: id.toHexString(),
            };
            const updateResult: WithId<IMongoUnifiedDisputeObject> =
                MongoDisputeMapper.toMongoDispute(dispute) as WithId<IMongoUnifiedDisputeObject>;

            // Mock the updateWithFilter method
            mockUpdateOperation.updateWithFilter = jest.fn().mockResolvedValue(updateResult);

            const updatedDispute = await repository.updatePromotion(id.toHexString(), 'PROMO');

            // Assert
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: expect.any(ObjectId) },
                { $set: { 'promotional': true, 'promotionCode': 'PROMO', dateUpdated: MOCK_DATE } },
                undefined,
            );
            expect(updatedDispute).toEqual(expectedResult);
        });

        it('should throw an error if disputeId is missing', async () => {
            // Act and Assert
            await expect(repository.updatePromotion('', '')).rejects.toThrow('Missing required parameters');
        });
    });

    describe('updateDisputeStatusToPrevented', () => {
        it('throws an error when any required parameter is missing', async () => {
            const data: Partial<UpdateDisputeStatusToPreventedPayload> = {
                chargeflowId: 'chargeflowId',
                transactionId: 'transactionId',
                // Missing alertId
            };
            await expect(repository.updateDisputeStatusToPrevented(data as UpdateDisputeStatusToPreventedPayload)).rejects.toThrow('Missing required parameters');

            const data2: Partial<UpdateDisputeStatusToPreventedPayload> = {
                chargeflowId: 'chargeflowId',
                alertId: 'alertId',
                // Missing transactionId
            };
            await expect(repository.updateDisputeStatusToPrevented(data2 as UpdateDisputeStatusToPreventedPayload)).rejects.toThrow('Missing required parameters');
        });

        it('should update the dispute status to prevented and return the result', async () => {
            const chargeflowId = new ObjectId().toHexString();
            const transactionId = 'transactionId';
            const alertId = 'alertId';
            const data: UpdateDisputeStatusToPreventedPayload = { chargeflowId, transactionId, alertId };

            const dispute = {
                ...disputeMocks.getUDO(),
            };

            const session = mock<ClientSession>();
            const disputeStatus: IDisputeStatus = {
                dateCreated: new Date(),
                statusDate: new Date(),
                status: ChargeFlowDisputeStatus.Prevented,
                processorStatus: null,
                source: DisputeStatusSource.AlertsLinking,
            };

            const updateResult: WithId<IMongoUnifiedDisputeObject> = MongoDisputeMapper.toMongoDispute({
                ...dispute,
                lastDisputeStatus: disputeStatus,
                alertId: alertId,
                dispute: {
                    ...dispute.dispute,
                    disputeStatuses: [
                        ...dispute.dispute.disputeStatuses,
                        disputeStatus,
                    ],
                },
            }) as WithId<IMongoUnifiedDisputeObject>;

            mockUpdateOperation.updateWithFilter.mockResolvedValue(updateResult);

            const result = await repository.updateDisputeStatusToPrevented(data, session);
            const expectedResult = {
                ...dispute,
                lastDisputeStatus: disputeStatus,
                alertId: alertId,
                dispute: {
                    ...dispute.dispute,
                    disputeStatuses: [
                        ...dispute.dispute.disputeStatuses,
                        disputeStatus,
                    ],
                },
            };

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                {
                    chargeflowId: new ObjectId(chargeflowId),
                    'transaction.id': transactionId,
                    'lastDisputeStatus.status': ChargeFlowDisputeStatus.Lost,
                },
                {
                    $set: { lastDisputeStatus: disputeStatus, alertId: alertId, dateUpdated: MOCK_DATE },
                    $push: { 'dispute.disputeStatuses': disputeStatus },
                },
                session,
            );
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateAllDisputesBillingStatus', () => {
        describe('updateAllDisputesBillingStatus', () => {
            it('should throw an error if chargeflowId is missing', async () => {
                await expect(repository.updateAllDisputesBillingStatus('', true)).rejects.toThrow('Missing chargeflowId');
            });

            it('should update the billing status on all disputes with the given chargeflowId', async () => {
                // Arrange
                const chargeflowId = new ObjectId().toHexString();
                const isBillingActive = true;
                const session = mock<ClientSession>();

                const bulkOperation = [
                    {
                        updateMany: {
                            filter: { chargeflowId: new ObjectId(chargeflowId) },
                            update: { $set: { isBillingActive, dateUpdated: MOCK_DATE } },
                        },
                    },
                ];

                const expectedResult = { result: { modifiedCount: 3 }, notUpdatedDisputes: [] } as unknown as IBulkUpdateResponse;

                mockUpdateOperation.bulkUpdate.mockResolvedValue(expectedResult);

                const result = await repository.updateAllDisputesBillingStatus(chargeflowId, isBillingActive, session);

                expect(mockUpdateOperation.bulkUpdate).toHaveBeenCalledWith(bulkOperation, session);
                expect(result).toEqual(expectedResult);
            });
        });
    });

    describe('updateLinkingTypeMetadata', () => {
        it('should throw an error if chargeflowId is missing', async () => {
            await expect(repository.updateLinkingTypeMetadata('','', { orderLinkType: 'auto', orderLinkMethod:'regular' })).rejects.toThrow('Missing chargeflowId');
        });

        it('should throw an error if disputeId is missing', async () => {
            await expect(repository.updateLinkingTypeMetadata('chargeflowId','', { orderLinkType: 'auto', orderLinkMethod:'regular' })).rejects.toThrow('Missing disputeId');
        });

        it('should update linking type metadata', async () => {
            // Arrange
            const chargeflowId = new ObjectId().toHexString();
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();
            const linkingTypeMetadata:ILinkingTypeMetadata = {
                orderLinkType: 'auto',
                orderLinkMethod: 'regular',
            };
            const filter = {
                chargeflowId: new ObjectId(chargeflowId),
                _id: new ObjectId(disputeId),
            };
            const updateFilter = {
                $set: {
                    'metadata.orderLinkMethod':linkingTypeMetadata.orderLinkMethod,
                    'metadata.orderLinkType': linkingTypeMetadata.orderLinkType,
                    dateUpdated: new Date(),
                },
            };

            await repository.updateLinkingTypeMetadata(chargeflowId, disputeId, linkingTypeMetadata, session);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(filter, updateFilter, session);
        });
    });

    describe('getDistinctProcessorsFromDisputes', () => {
        it('should throw an error if chargeflowId is missing', async () => {
            await expect(repository.getDistinctProcessorsFromDisputes('')).rejects.toThrow('Missing chargeflowId');
        });

        it('should return distinct processors for a given chargeflowId', async () => {
            const chargeflowId = new ObjectId().toHexString();
            const session = mock<ClientSession>();
            const distinctProcessors = [ Source.Stripe, Source.ShopifyPayments ];
            mockFindByQueryOperation.findDistinct = jest.fn().mockResolvedValue(distinctProcessors);

            const result = await repository.getDistinctProcessorsFromDisputes(chargeflowId, session);

            expect(mockFindByQueryOperation.findDistinct).toHaveBeenCalledWith(
                'dispute.processor',
                { chargeflowId: new ObjectId(chargeflowId) },
                session,
            );
            expect(result).toEqual(distinctProcessors);
        });
    });

    describe('updatePerformedAction', () => {
        it('should update chargeflowPerformedAction', async () => {
            const id = new ObjectId();

            await repository.updatePerformedAction(id.toString(), true);

            // Assert
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: expect.any(ObjectId) },
                { $set: { 'chargeflow.cfActions.chargeflowPerformedAction': true } },
                undefined,
            );
        });

        it('should throw an error if disputeId is missing', async () => {
            // Act and Assert
            await expect(repository.updatePerformedAction('')).rejects.toThrow('Missing disputeId');
        });
    });
});
