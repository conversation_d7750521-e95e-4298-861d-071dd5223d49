import { ClientSession, Document } from 'mongodb';
import {
    IDisputeStatus,
    IEvidence,
    IUnifiedDisputeObject,
    IOrder,
    IUpdatableUnifiedDisputeObject,
    IBulkUpdateResponse,
    IUpdatableTransaction,
    IAddress,
    IEvidenceAttachment,
    IChargeflowResolved,
    IMongoEvidence, ILinkingTypeMetadata,
    ITransaction,
} from './Types';
import { Source } from '../shared/enums';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { WithId } from '../shared/helper-types';
import { UpdateDisputeStatusToPreventedPayload } from '@chargeflow-team/events-infra';

export interface IDisputeRepository {
    add: (
        dispute: IUnifiedDisputeObject, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>,

    bulkAdd: (
        disputes: IUnifiedDisputeObject[], session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>[]>,

    getByChargeflowId: (
        chargeflowId: string, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>[]>,

    getByCaseIdAndProcessorName: (
        caseId: string, processor: Source, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>,

    getManyByCaseIdChargeflowIdProcessor: (
        caseIds: string[], chargeflowId: string, processor: Source, session?: ClientSession, page?: number, pageSize?: number
    ) => Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination?: IPaginationCalculatorResult }>,

    getManyByChargeflowIdAndProcessorIdFromLast180Days: (
        chargeflowId: string, processorId: string, session?: ClientSession, page?: number, pageSize?: number
    ) => Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination?: IPaginationCalculatorResult }>

    updateAndPushHistoricalData: (
        dispute: Readonly<WithId<Partial<IUnifiedDisputeObject>>>,
        historicalData: DisputeHistoricalData,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>

    updateEvidence: (
        disputeId: string,
        evidenceId: string,
        evidence: Readonly<Partial<IEvidence>>,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    addEvidence: (
        disputeId: string,
        evidence: Readonly<IMongoEvidence>,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    updateActionStatus: (
        disputeId: string,
        actionName: string,
        status: unknown,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    updatePartialDisputesBulk: (
        dispute: Readonly<WithId<Partial<IUpdatableUnifiedDisputeObject>>>[],
        session?: ClientSession
    ) => Promise<IBulkUpdateResponse>;

    getExtensionsByDisputeId: (
        disputeId: string,
        session?: ClientSession
    ) => Promise<Partial<IUnifiedDisputeObject>>;

    updateExtensionResponseByDisputeId: (
        disputeId: string,
        response: unknown,
        processor: Source,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    updateExtensionAttachmentsByDisputeId: (
        disputeId: string,
        attachment: IEvidenceAttachment,
        processor: Source,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    get: (
        id: string, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject> | null>;

    addDisputeOrder: (
        disputeId: string, order: IOrder | null, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    addDisputeTransaction: (
        disputeId: string, transaction: ITransaction, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    updateTransaction: (
        disputeId: string, transaction: Partial<IUpdatableTransaction>, session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject>>;

    updateIsActionRequired: (
        disputeId: string, session?: ClientSession
    ) => Promise<IUnifiedDisputeObject>;

    updateChargeflowChecks(
        disputeId: string, orderShippingAddress?: IAddress, session?: ClientSession
    ): Promise<WithId<IUnifiedDisputeObject>>;

    updateOrderProductsImageStoragePath(
        disputeId: string, imageUrl: string, orderProductImageStoragePath: string, session?: ClientSession
    ): Promise<WithId<IUnifiedDisputeObject>>;

    updateResolvedObject(
        disputeId: string, resolvedObject: IChargeflowResolved, session?: ClientSession
    ): Promise<WithId<IUnifiedDisputeObject>>;

    getOpenDisputesByChargeflowIdAndProcessor(
        chargeflowId: string, processor: Source, page?: number, pageSize?: number, session?: ClientSession
    ): Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination?: IPaginationCalculatorResult }>

    stampSuccessRateOnDispute(
        disputeId: string, fee: number, session?: ClientSession
    ): Promise<WithId<IUnifiedDisputeObject>>;

    getDisputesForDeactivation: (
        processorId: string,
        page?: number, pageSize?: number, session?: ClientSession
    ) => Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination?: IPaginationCalculatorResult }>;

    getDisputesForReactivation: (
        chargeflowId: string,
        processorId: string,
        page?: number, pageSize?: number, session?: ClientSession
    ) => Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination?: IPaginationCalculatorResult }>;

    updateHandleByChargeflow: (disputeIds: string[], handleByChargeflow: boolean, session?: ClientSession
    ) => Promise<IBulkUpdateResponse>;

    getDisputeIds: (session: ClientSession, page?: number, pageSize?: number) =>
        Promise<{
            ids: string[];
            pagination?: IPaginationCalculatorResult;
        }>;

    updatePromotion: (disputeIds: string, promotionCode: string, session?: ClientSession) => Promise<WithId<IUnifiedDisputeObject>>;
    getPromotionalDisputes(
        chargeflowId: string,
        promotionCode: string,
        projection?: Document,
        page?: number,
        pageSize?: number,
        session?: ClientSession): Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination: IPaginationCalculatorResult }>;
    updateDisputeStatusToPrevented: (data: UpdateDisputeStatusToPreventedPayload, session?: ClientSession) => Promise<WithId<IUnifiedDisputeObject> | undefined>;

    updateAllDisputesBillingStatus: (chargeflowId: string, billingStatus: boolean, session?: ClientSession) => Promise<IBulkUpdateResponse>;
    updateDisputeWonEventPublished(disputeId: string, session?: ClientSession): Promise<void>;
    updateLinkingTypeMetadata(chargeflowId: string, disputeId: string, linkingTypeMetadata: ILinkingTypeMetadata, session?: ClientSession): Promise<void>;
    getDistinctProcessorsFromDisputes(chargeflowId: string, session?: ClientSession): Promise<string[] | number[]>;
    updatePerformedAction: (disputeId: string, performedAction: boolean, session?: ClientSession) => Promise<void>;

    getDisputesWithPagination: (
        chargeflowIds: string[],
        page: number,
        pageSize: number,
        session?: ClientSession,
    ) => Promise<{ items: WithId<IUnifiedDisputeObject>[], pagination: IPaginationCalculatorResult }>;

    getByChargeflowIdAndProcessorIdAndCaseId: (
        chargeflowId: string,
        processorId: string,
        caseId: string,
        session?: ClientSession
    ) => Promise<WithId<IUnifiedDisputeObject> | null>;

    updateOrderFinancialStatus: (disputeId: string, financialStatus: string, session?: ClientSession) => Promise<void>;
}

export type DisputeHistoricalData = {
    'dispute.disputeStatuses'?: IDisputeStatus,
    'dispute.rawData.webhooks'?: unknown,
    'transaction.rawData.webhooks'?: unknown,
}
