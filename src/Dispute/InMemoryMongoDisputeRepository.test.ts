import { MongoClient, ObjectId } from 'mongodb';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { DisputeCollectionName } from './constants';
import disputeMocks from './__mocks__/Disputes';
import { IMongoUnifiedDisputeObject } from './Types';
import { IDisputeRepository } from './IDisputeRepository';
import { DisputeRepositoryFactory } from './DisputeRepositoryFactory';

describe('InMemoryDisputeRepositoryTest', () => {
    let repository: IDisputeRepository;
    let mongoServer: MongoMemoryServer;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repository = DisputeRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            DisputeCollectionName,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName)
            .collection<IMongoUnifiedDisputeObject>(DisputeCollectionName)
            .deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    it('should return UDO by chargeflowId, processorId and caseId', async () => {
        // GIVEN
        const chargeflowId = new ObjectId().toHexString();
        const processorId = new ObjectId().toHexString();
        const defaultUDO = disputeMocks.defaultUDO;
        const caseId = '123456789';
        const dispute = disputeMocks.getUDO({
            ...defaultUDO,
            dateCreated: new Date('2024-04-11T00:00:00Z'),
            chargeflowId,
            dispute: {
                ...defaultUDO.dispute,
                processorId: processorId,
                id: caseId,
                dateCreated: new Date('2024-04-11T00:00:00Z'),
            },
        });
        await repository.add(dispute);

        // WHEN
        const foundDispute = await repository.getByChargeflowIdAndProcessorIdAndCaseId(
            chargeflowId, processorId, caseId,
        );

        // THEN
        expect(foundDispute)
            .not
            .toBeNull();
    });

    it('should return null when not found by chargeflowId, processorId and caseId', async () => {
        // GIVEN
        const chargeflowId = new ObjectId().toHexString();
        const processorId = new ObjectId().toHexString();
        const caseId = '123456789';

        // WHEN
        const foundDispute = await repository.getByChargeflowIdAndProcessorIdAndCaseId(
            chargeflowId, processorId, caseId,
        );

        // THEN
        expect(foundDispute)
            .toBeNull();
    });

    it('should update UDO order financial status', async () => {
        // GIVEN
        const chargeflowId = new ObjectId().toHexString();
        const processorId = new ObjectId().toHexString();
        const defaultUDO = disputeMocks.defaultUDO;
        const caseId = '123456789';
        const dispute = disputeMocks.getUDO({
            ...defaultUDO,
            dateCreated: new Date('2024-04-11T00:00:00Z'),
            chargeflowId,
            dispute: {
                ...defaultUDO.dispute,
                processorId: processorId,
                id: caseId,
                dateCreated: new Date('2024-04-11T00:00:00Z'),
            },
        });
        const savedDispute = await repository.add(dispute);

        // WHEN
        await repository.updateOrderFinancialStatus(savedDispute._id, 'refunded');

        // THEN
        const modifiedDispute = await repository.get(savedDispute._id);
        expect(modifiedDispute)
            .not
            .toBeNull();
        expect(modifiedDispute!.order?.financialStatus)
            .toBe('refunded');
    });

    it('should throw exception when dispute not found', async () => {
        // GIVEN
        const disputeId = new ObjectId().toHexString();

        // expect
        await expect(repository.updateOrderFinancialStatus(disputeId, 'refunded')).rejects.toThrow('Failed to update order financial status');
    });
});
