import { Bulk<PERSON><PERSON><PERSON><PERSON>ult, Document, ObjectId } from 'mongodb';
import {
    ChargeFlowDisputeStatus,
    DisputeAction,
    DisputeReason,
    DisputeStage,
    DisputeStatusSource,
    EvidenceType,
} from './enums';
import { Source } from '../shared/enums';

/**
 * This interface is not meant to be used by itself.
 * It is used as a base for the main UDO and DB specific interface.
 */
interface IUnifiedDisputeObjectBase {
  dateCreated: Date;
  dateUpdated: Date | null;
  shopName: string | null;
  shopPlatform?: string;
  liveMode: boolean;
  lastDisputeStatus: IDisputeStatus;
  dispute: IDispute;
  transaction: ITransaction | null;
  shippingData: IShippingData[] | null;
  extensions: unknown | null;
  isBillingActive?: boolean;
  intentionalFraud: boolean | null;
  metadata?: unknown;
  order: IOrder | null;
  orderDate: Date | null;
  orderName: string | null;
  promotional?: boolean;
  promotionCode?: string;
  geoflow?: unknown;
  isDisputeWonEventPublished?: boolean;
}

export interface ILinkingTypeMetadata {
  orderLinkMethod?: string | null;
  orderLinkType?: 'auto' | 'manual' | null;
}

/**
 * Main interface/contract for UDO. This is the interface that should be used in the application layer.
 */
export interface IUnifiedDisputeObject extends IUnifiedDisputeObjectBase {
  _id?: string;
  shopId: string | null;
  chargeflowId: string;
  accountId?: string | null;
  chargeflow: IChargeflowObject;
  alertId?: string;
}

// This is breaking single responsibility principle but eliminates multiple DB calls
export interface IUpdatableUnifiedDisputeObject {
  _id?: string;
  lastDisputeStatus?: IDisputeStatus;
  dateUpdated?: Date | null;
  closingNote?: string;
  closingReason?: string;
  closedDate?: Date | null;
  responseDue?: Date | null;
  reasonCode?: string | null;
  reason?: DisputeReason | null;
  stage?: DisputeStage;
  chargeflow?: {
    submitted?: IChargeflowSubmitted;
    resolved?: IChargeflowResolved;
    cfActions?: IcfActions;
  };
  actions?: IChargeflowAction[];
  amount?: number;
  processorReason?: string | null;
  extensions?: unknown | null;
}

export interface IUpdatableTransaction {
  checks: Partial<IChecks>;
  paymentDetails: Partial<IPaymentDetails>;
}

export interface IBulkUpdateResponse {
  result: BulkWriteResult;
  notUpdatedIds: string[];
}

/**
 * Mongo specific UDO interface. For use only in data-access layer.
 */
export interface IMongoUnifiedDisputeObject
  extends IUnifiedDisputeObjectBase,
  Document {
  shopId: ObjectId | null;
  chargeflowId: ObjectId;
  accountId?: ObjectId | null;
  chargeflow: IMongoChargeflowObject;
  alertId?: string;
}

export interface IAmount {
  value?: number; // The amount value
  currency?: string; // The three-letter ISO 4217 currency code represents the currency used for the amount
}

export interface IDeviceFingerprint {
  userAgent?: string;
  ipAddress?: string;
  MAC?: string;
}

export interface IOrderActivity {
  id?: string;
  activityDate?: string;
  type?:
  | 'signup'
  | 'login'
  | 'logout'
  | 'download'
  | 'export'
  | 'edit'
  | 'password_changed'
  | 'profile_updated'
  | 'general_action'
  | 'payment';
  description?: string;
  fingerprintType?: IDeviceFingerprint;
  fingerprintValue?: string;
  source?: string;
}

export interface IOrderTransactions {
  id?: string;
  transactionDate?: string;
  amount?: IAmount;
}

export interface IOrder {
  amount?: number;
  billing?: {
    billingAddress: IAddress;
    billingDetails?: {
      bin?: string | null;
      category?: string | null;
      gateway?: string | null;
    };
  };
  checkoutId?: string | null;
  currency?: string | null;
  customer?: {
    dateCreated?: Date | null;
    email?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    id?: string | number | null;
    ipAddress?: string | null;
    name?: string | null;
    ordersCount?: number;
    phone?: string | null;
    verifiedEmail?: boolean | null;
    activityLogs?: IOrderActivity[];
  };
  dateCreated: Date | null;
  dateReceived?: Date;
  dateUpdated?: Date | null;
  financialStatus?: string | null;
  fingerprint?: string | null;
  id?: number | string | null;
  name?: string | null;
  orderStatusUrl?: string | null;
  products?: IOrderProduct[];
  refunds?: IOrderRefund[];
  shipping?: IOrderShipping;
  shopDomain?: string | null;
  source?: Source;
  storeUrl?: string | null;
  totalDiscounts?: {
    amount: string;
    currency: string;
  };
  totalTax?: {
    amount: string;
    currency: string;
  };
  orderDate?: Date;
  rawData?: {
    order: unknown;
    webhooks: unknown[];
  };
}

export interface IOrderProductDiscount {
  amount?: string | null;
  currency?: string | null;
}

export interface IOrderProduct {
  id?: number | null;
  variantId?: number | null;
  name?: string | null;
  description?: string | null;
  price?: number | null;
  currency?: string | null;
  imageUrl?: string | null;
  sku?: string | null;
  url?: string | null;
  quantity?: number | null;
  type?: string | null;
  productId?: number | null;
  discounts?: IOrderProductDiscount;
  isRefunded?: boolean | null;
}

export interface IOrderShipping {
  shippingAddress?: IAddress;
  fulfillments?: IFulfillment[];
  shippingLines?: IOrderShippingLine;
}

export interface IFulfillment {
  shipmentId?: string | null;
  shippingCompany?: string | null;
  trackingNumber?: string | null;
  shippedDate?: Date | null;
}

export interface IEvidenceBase {
  dateCreated?: Date;
  evidenceType: EvidenceType;
  evidenceUploadCategory?: string;
  dateUpdated?: Date;
  postedBy?: string;
  role?: string;
  content?: string;
  isDeleted?: false;
  sentDate?: Date | null;
  sent?: boolean | null;
  attemptedSend?: boolean;
  sentBy?: string | null;
  fileKey: string;
  fileName: string;
  actionName: string;
  isCompelling?: boolean;
  shouldSendEvidence?: boolean | null;
}

export interface IEvidence extends IEvidenceBase {
  evidenceId: string;
}

export interface IMongoEvidence extends IEvidenceBase {
  evidenceId: ObjectId;
}

export interface IAddress {
  address1?: string | null;
  address2?: string | null;
  city?: string | null;
  country?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  firstName?: string | null;
  lastName?: string | null;
  phone?: string | null;
  state?: string | null;
  zipcode?: string | null;
  name?: string | null;
}

export interface IOrderShippingLine {
  amount?: string | null;
  currency?: string | null;
  title?: string | null;
}

export interface IOrderRefund {
  amount?: number;
  currency?: string;
  arn?: string | null;
  authorization?: string | null;
  dateProcessed?: Date;
}

export interface IChargeflowObjectBase {
  templates: unknown[];
  cfActions: IcfActions | null;
  merchantActions?: IChargeflowMerchantActions | null;
  resolved: IChargeflowResolved | null;
  chargeScore: {
    score: number;
    parameters: unknown[];
    history: unknown[];
  } | null;
  submitted?: IChargeflowSubmitted;
  imported?: boolean | null;
  importTaskId?: string | null;
  checks?: {
    doesBillingMatchShipping: boolean;
    doesBillingAddress1MatchShipping: boolean;
    doesBillingZipMatchShipping: boolean;
  };
}

export interface IChargeflowObject extends IChargeflowObjectBase {
  evidences: IEvidence[];
}

export interface IMongoChargeflowObject extends IChargeflowObjectBase {
  evidences: IMongoEvidence[];
}

export interface IChargeflowSubmitted {
  /**
   * The date when the dispute was first submitted to the processor. Will not update once set, except when nullified.
   */
  submittedAt?: Date | null;
  /**
   * The date when the dispute was last submitted to the processor.
   */
  lastSubmittedAt?: Date;
  /**
   * The amount of the dispute in USD.
   */
  amountInUSD?: number | null;
  /**
   * The percentage of the disputed amount that Chargeflow will charge as a service fee if the dispute is won.
   */
  successRate?: number;
}

export interface IChargeflowResolved {
  resolvedDate: Date | null;
  resolveId: string | null;
  isResolved: boolean;
  isWon: boolean;
  amountWon: number | null;
  successFee: number | null;
  resolvedDataSource?: string | null;
}

export interface IcfActions {
  handleByChargeflow: boolean;
  chargeflowPerformedAction: boolean;
}

export type IChargeflowMerchantAction = string;

export interface IChargeflowMerchantActions {
  performedActions: IChargeflowMerchantAction[];
  isActionRequired: boolean;
}

export interface ITransaction {
  id: string | null;
  dateReceived: Date | null;
  dateCreated: Date | null;
  dateUpdated: Date | null;
  amount: number | null;
  currency: string | null;
  source: string | null;
  receiptUrl: string | null;
  checks: IChecks | null;
  billing: IBillingDetails | null;
  customerName: string | null;
  customerEmail: string | null;
  orderId: string | null;
  risk: {
    riskLevel: string | null;
    riskScore: number | null;
  } | null;
  statementDescriptor: string | null;
  paymentDetails: IPaymentDetails | null;
  rawData: {
    transaction: Record<string, unknown> | null;
    webhooks: unknown[];
  };
  type?: string | null;
}

export interface IShippingData {
  id?: string;
  trackingNumber?: string;
  dateCreated?: Date;
  dateUpdated?: Date;
  source?: string;
  courier?: string;
  status?: string;
  statusDescription?: string;
  deliveryTime?: Date;
  destinationCountry?: string;
  originCountry?: string;
  signedBy?: string;
  subtagMessage?: string;
  deliveryType?: string;
  firstAttemptedAt?: Date;
  courierTrackingLink?: string;
  onTimeStatus?: string;
  checkpoints: IShippingCheckpoint[];
  reconciliation: IShippingReconciliation[];
  rawData: {
    shippingData: unknown;
    webhooks: unknown[];
  };
}

export interface IShippingReconciliation {
  lastUpdatedDate: Date;
  newStatus: string;
  normalizedStatus: string;
  oldStatus?: string;
  reconciliationDate: Date;
}

export interface IShippingCheckpoint {
  checkpointTime: Date;
  city?: string;
  countryIso3?: string;
  countryName?: string;
  dateCreated: Date;
  location?: string;
  message: string;
  rawTag?: string;
  slug: string;
  state?: string;
  status: string;
  statusDescription: string;
  subtag: string;
  subtagMessage: string;
  zip?: string;
}

export interface IPaymentDetails {
  cardBrand: string | null;
  country: string | null;
  expMonth: number | null;
  expYear: number | null;
  installments: number | null;
  network: string | null;
  '3dSecure': unknown | null;
  last4: string | null;
  cardHolderName?: string | null;
  issuer?: string | null;
}

export interface IChecks {
  cvcCheck: ICVCCheck | null;
  avsCheck: IAVSCheck | null;
  addressLine1Check: string | null;
  addressZipCheck: string | null;
}

export interface ICVCCheck {
  code: string | null;
  description: string | null;
}

export interface IAVSCheck {
  code: string | null;
  description: string | null;
}

export interface IBillingDetails {
  name: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  line1: string | null;
  line2: string | null;
  zip: string | null;
  phone: string | null;
}

export interface IChargeflowAction {
  dateCreated: Date;
  actionName: DisputeAction;
}

export interface IDispute {
  id: string;
  processorId?: string;
  dateReceived: Date | null;
  dateCreated: Date;
  dateUpdated: Date | null;
  processor: Source;
  source: Source;
  stage: DisputeStage;
  transactionId: string | null;
  amount: number;
  currency: string;
  reason: DisputeReason | null;
  processorReason: string | null;
  reasonDescription?: string;
  processorComments?: string;
  reasonCode: string | null;
  disputeStatuses: IDisputeStatus[];
  closingReason?: string; // Undentified in 96% of cases
  closingNote?: string;
  responseDue: Date | null;
  closedDate: Date | null;
  submittedAt: Date | null;
  submittedCount: number | null;
  disputeFee: number | null;
  statementDescriptor: string | null;
  mid: string | null;
  isRefundable: boolean | null;
  intentionalFraud: boolean | null;
  actions: IChargeflowAction[];
  merchantReference?: string; // Undentified in 96% of cases
  merchantOrderId?: string;
  amountInUsd?: number;
  rawData: {
    dispute: unknown;
    webhooks: unknown[];
  };
}

export interface IDisputeStatus {
  dateCreated: Date | null;
  statusDate: Date | null;
  status: ChargeFlowDisputeStatus | null;
  processorStatus: string | null;
  source: DisputeStatusSource;
}

export interface IEvidenceAttachment {
  id: string;
  fileName?: string;
  fileKey?: string;
  evidenceId: string;
}
