
import { ObjectId, WithId as MongoWithId } from 'mongodb';
import {
    IUnifiedDisputeObject,
    IMongoUnifiedDisputeObject,
    IEvidence,
    IChargeflowObject,
    IMongoChargeflowObject, IMongoEvidence,
} from './Types';
import { WithId } from '../shared/helper-types';
import { deleteUndefinedFields } from '../shared/helper-functions';

export class MongoDisputeMapper {
    private static toMongoEvidence(evidence: IEvidence): IMongoEvidence {
        if (!evidence) {
            throw new Error('Evidence is required for mapping');
        }

        return {
            ...evidence,
            evidenceId: new ObjectId(evidence.evidenceId),
        };
    }

    private static toMongoChargeflowObject(chargeflow: IChargeflowObject): IMongoChargeflowObject {
        if (!chargeflow) {
            throw new Error('Chargeflow object is required for mapping');
        }

        return {
            ...chargeflow,
            evidences: chargeflow.evidences.map(MongoDisputeMapper.toMongoEvidence),
        };
    }

    static toMongoDispute(dispute: IUnifiedDisputeObject): IMongoUnifiedDisputeObject {
        if (!dispute) {
            throw new Error('Dispute is required for mapping');
        }

        const currentDateTime = new Date();
        if (dispute.dateCreated > currentDateTime || dispute.dispute.dateCreated > currentDateTime) {
            throw new Error('Dispute dateCreated cannot be in the future');
        }

        return {
            ...dispute,
            _id: dispute._id ? new ObjectId(dispute._id) : undefined,
            chargeflowId: new ObjectId(dispute.chargeflowId),
            accountId: dispute.accountId ? new ObjectId(dispute.accountId) : null,
            shopId: dispute.shopId ? new ObjectId(dispute.shopId) : null,
            chargeflow: dispute.chargeflow && MongoDisputeMapper.toMongoChargeflowObject(dispute.chargeflow),
        };
    }
    static toPartialMongoDispute(dispute: Partial<IUnifiedDisputeObject>): Partial<IMongoUnifiedDisputeObject> {
        if (!dispute) {
            throw new Error('Dispute is required for mapping');
        }

        return deleteUndefinedFields({
            ...dispute,
            _id: dispute._id ? new ObjectId(dispute._id) : undefined,
            chargeflowId: dispute.chargeflowId ? new ObjectId(dispute.chargeflowId) : undefined,
            accountId: dispute.accountId ? new ObjectId(dispute.accountId) : undefined,
            shopId: dispute.shopId ? new ObjectId(dispute.shopId) : undefined,
            chargeflow: dispute.chargeflow ? MongoDisputeMapper.toMongoChargeflowObject(dispute.chargeflow) : undefined,
        });
    }

    static toMongoDisputes(disputes: IUnifiedDisputeObject[]): IMongoUnifiedDisputeObject[] {
        return disputes.map(MongoDisputeMapper.toMongoDispute);
    }

    private static fromMongoEvidence(evidence: IMongoEvidence): IEvidence {
        if (!evidence) {
            throw new Error('Evidence is required for mapping');
        }

        return {
            ...evidence,
            evidenceId: evidence.evidenceId.toHexString(),
        };
    }

    private static fromMongoChargeflowObject(chargeflow: IMongoChargeflowObject): IChargeflowObject {
        if (!chargeflow) {
            throw new Error('Chargeflow object is required for mapping');
        }

        return {
            ...chargeflow,
            evidences: chargeflow.evidences.map(MongoDisputeMapper.fromMongoEvidence),
        };
    }

    static fromMongoDispute(dispute: MongoWithId<IMongoUnifiedDisputeObject>): WithId<IUnifiedDisputeObject> {
        if (!dispute) {
            throw new Error('Dispute is required for mapping');
        }

        return {
            ...dispute,
            _id: dispute._id?.toString(),
            chargeflowId: dispute.chargeflowId?.toString(),
            accountId: dispute.accountId?.toString() ?? null,
            shopId: dispute.shopId?.toString() ?? null,
            chargeflow: dispute.chargeflow && MongoDisputeMapper.fromMongoChargeflowObject(dispute.chargeflow),
        };
    }

    static fromMongoDisputes(disputes: MongoWithId<IMongoUnifiedDisputeObject>[]): WithId<IUnifiedDisputeObject>[] {
        return disputes.map(MongoDisputeMapper.fromMongoDispute);
    }

}
