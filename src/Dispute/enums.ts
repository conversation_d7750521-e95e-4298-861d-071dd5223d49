export enum ChargeFlowDisputeStatus {
    WarningNeedsResponse = 'warning_needs_response',
    WarningUnderReview = 'warning_under_review',
    WarningAwaitingresponse = 'warning_awaiting_response',
    WarningWon = 'warning_won',
    WarningLost = 'warning_lost',
    Awaitingresponse = 'awaiting_response',
    Appealable = 'appealable',
    NeedsResponse = 'needs_response',
    UnderReview = 'under_review',
    Refunded = 'refunded',
    Insured = 'insured',
    Lost = 'lost',
    Won = 'won',
    Undefended = 'undefended',
    Prevented = 'prevented',
}

export enum DisputeReason {
    BankCannotProcess = 'bank_cannot_process',
    CheckReturned = 'check_returned',
    CreditNotProcessed = 'credit_not_processed',
    CustomerInitiated = 'customer_initiated',
    Unauthorized = 'debit_not_authorized',
    Duplicate = 'duplicate',
    General = 'general',
    IncorrectAccountDetails = 'incorrect_account_details',
    InsufficientFunds = 'insufficient_funds',
    ProductNotReceived = 'product_not_received',
    ProductNotAsDescribed = 'product_unacceptable',
    NotReceived = 'not_received',
    NotAsDescribed = 'not_as_described',
    SubscriptionCanceled = 'subscription_canceled',
    Canceled = 'canceled',
    DuplicateCharge = 'duplicate_charge',
    PaymentByOtherMeans = 'payment_by_other_means',
    Unrecognized = 'unrecognized',
    IncorrectAmount = 'incorrect_amount',
    Fraud = 'fraud',
    CanceledRecurringBilling = 'canceled_recurring_billing',
    Other = 'other',
    Problem_with_remittance = 'problem_with_remittance',
    UnauthorizedLiteral = 'unauthorized',
}

export enum DisputeAction {
    AcceptClaim = 'accept_claim',
    ProvideEvidence = 'provide_evidence',
    SubmitResponse = 'submit_response',
    ProvideSupportingInfo = 'provide_supporting_info',
    SendMessage = 'send_message',
    Escalate = 'escalate',
    MakeOffer = 'make_offer',
    Appeal = 'appeal',
    Cancel = 'cancel',
    ChangeReason = 'change_reason',
    AcknowledgeReturnItem = 'acknowledge_return_item',
}

export enum DisputeStatusSource {
    Webhook = 'webhook',
    Fetch = 'fetch',
    ReFetch = 're-fetch',
    Reconciliation = 'reconciliation',
    Asana = 'asana',
    Import = 'import',
    AlertsLinking = 'alerts-linking',
    Manual = 'manual',
    Toggle = 'toggle',
}

export enum DisputeStage {
    Chargeback = 'Chargeback',
    chargeback = 'chargeback', // lowercase chargeback is set on a low number of disputes
    Inquiry = 'Inquiry',
    PreArbitration = 'Pre_arbitration', // this is picked from DB, does not look nice
    Arbitration = 'Arbitration',
}

export enum EvidenceType {
    UPLOAD_ATTACHMENT = 'upload_attachment',
    UPLOAD_EVIDENCE = 'upload_evidence',
    DISPUTE_ACCEPTED = 'dispute_accepted',
    RESPOND_TO_INQUIRY = 'respond_to_inquiry',
    TRACKING_INFO = 'tracking_info',
    NOTE = 'note',
    PROOF_OF_FULFILLMENT = 'proof_of_fulfillment',
    OTHER = 'other'
}
