import { DisputeRepositoryFactory } from './DisputeRepositoryFactory';
import { MongoDisputeRepository } from './MongoDisputeRepository';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';

describe(DisputeRepositoryFactory.name, () => {

    beforeAll(() => {
        process.env.DISPUTES_EVENT_BUS = faker.word.noun();
        process.env.STACK_NAME = faker.word.noun();
    });

    describe(DisputeRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mockMongoClient = {} as MongoClient;
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = DisputeRepositoryFactory.create(mockMongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoDisputeRepository);
        });
    });
});
