import {
    ClientSession,
    Document,
    Filter,
    ObjectId,
    PushOperator,
    UpdateFilter,
} from 'mongodb';
import {
    DisputeHistoricalData,
    IDisputeRepository,
} from './IDisputeRepository';
import {
    IAddress,
    IBillingDetails,
    IBulkUpdateResponse,
    IChargeflowResolved,
    IMongoEvidence,
    IEvidence,
    IEvidenceAttachment,
    IMongoUnifiedDisputeObject,
    IOrder,
    IUnifiedDisputeObject,
    IUpdatableTransaction,
    IUpdatableUnifiedDisputeObject,
    IDisputeStatus,
    ILinkingTypeMetadata,
    ITransaction,
} from './Types';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';
import { MongoDisputeMapper } from './MongoDisputeMapper';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { Source } from '../shared/enums';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { WithId } from '../shared/helper-types';
import { UpdateDisputeStatusToPreventedPayload } from '@chargeflow-team/events-infra';
import { ChargeFlowDisputeStatus, DisputeStatusSource } from './enums';
import { buildUpdateObject } from '../shared/helper-functions';

export class MongoDisputeRepository implements IDisputeRepository {
    constructor(
        private readonly addOperation: IAddOperation<IMongoUnifiedDisputeObject>,
        private readonly bulkAddOperation: IBulkAddOperation<IMongoUnifiedDisputeObject>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoUnifiedDisputeObject>,
        private readonly getByIdOperation: IGetByIdOperation<IMongoUnifiedDisputeObject>,
        private readonly updateOperation: IUpdateOperation<IMongoUnifiedDisputeObject>,
    ) { }

    async updateEvidence(
        disputeId: string,
        evidenceId: string,
        evidence: Readonly<Partial<IEvidence>>,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        if (!evidenceId) {
            throw new Error('Missing evidenceId');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const arrayFilter = {
            'evidence.evidenceId': new ObjectId(evidenceId),
        };

        const setObject = Object.entries(evidence).reduce((acc, [ key, value ]) => {
            if (value !== undefined) {
                acc[`chargeflow.evidences.$[evidence].${key}`] = value;
            }
            return acc;
        }, {} as Record<string, unknown>);
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                dateUpdated: new Date(),
                ...setObject,
            },
        };

        const result = await this.updateOperation.updateWithFilterAndArrayFilters(
            filter,
            updateFilter,
            [ arrayFilter ],
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async addEvidence(
        disputeId: string,
        evidence: Readonly<Partial<IMongoEvidence>>,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        if (!evidence) {
            throw new Error('Missing evidence');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter = {
            $addToSet: {
                'chargeflow.evidences': evidence,
            } as unknown as UpdateFilter<IMongoUnifiedDisputeObject>,
            $set: {
                dateUpdated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async getDisputesWithPagination(
        chargeflowIds: string[],
        page: number,
        pageSize: number,
        session?: ClientSession,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination: IPaginationCalculatorResult;
    }> {
        if (!chargeflowIds || chargeflowIds.length === 0) {
            throw new Error('Missing chargeflowIds');
        }
        page = page || 1;
        pageSize = pageSize || 10;
        const objectIds = chargeflowIds.map(id => new ObjectId(id));

        const aggregation: Document[] = [
            {
                $match: {
                    chargeflowId: {
                        $in: objectIds,
                    },
                },
            },
            {
                $sort: {
                    dateCreated: -1,
                },
            },
        ];

        const result = await this.findByQueryOperation.findWithAggregation<IMongoUnifiedDisputeObject>(
            aggregation,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async updateActionStatus(
        disputeId: string,
        actionName: string,
        status: unknown,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        if (!actionName) {
            throw new Error('Missing actionName');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                [`chargeflow.actions.${actionName}.status`]: status,
                dateUpdated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async add(
        dispute: IUnifiedDisputeObject,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        const result = await this.addOperation.add(
            {
                ...MongoDisputeMapper.toMongoDispute(dispute),
            },
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async bulkAdd(
        disputes: IUnifiedDisputeObject[],
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>[]> {
        const result = await this.bulkAddOperation.bulkAdd(
            MongoDisputeMapper.toMongoDisputes(disputes),
            session,
        );

        return MongoDisputeMapper.fromMongoDisputes(result);
    }

    // This will be updated in the next task
    async getByChargeflowId(
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>[]> {
        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find(
            { chargeflowId: new ObjectId(chargeflowId) },
            page,
            pageSize,
            session,
        );

        return MongoDisputeMapper.fromMongoDisputes(result.items);
    }

    async updateAndPushHistoricalData(
        dispute: Readonly<WithId<Partial<IUnifiedDisputeObject>>>,
        historicalData: DisputeHistoricalData,
        session?: ClientSession | undefined,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                ...MongoDisputeMapper.toPartialMongoDispute(dispute),
                dateUpdated: new Date(),
            },
        };

        if (
            Object.entries(historicalData).filter(([ , value ]) => value !== undefined)
                .length > 0
        ) {
            updateFilter.$push =
                historicalData as unknown as PushOperator<IMongoUnifiedDisputeObject>;
        }

        const updatedEntity = await this.updateOperation.updateWithFilter(
            {
                _id: new ObjectId(dispute._id),
            },
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(updatedEntity);
    }

    async getByCaseIdAndProcessorName(
        caseId: string,
        processor: Source,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find(
            {
                'dispute.id': caseId,
                'dispute.processor': processor,
            },
            page,
            pageSize,
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result.items[0]);
    }

    async getManyByCaseIdChargeflowIdProcessor(
        caseIds: string[],
        chargeflowId: string,
        processor: Source,
        session?: ClientSession | undefined,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination?: IPaginationCalculatorResult;
    }> {
        if (!caseIds) {
            throw new Error('Missing caseIds');
        }
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }
        if (!processor) {
            throw new Error('Missing processor');
        }
        if (caseIds.length === 0) {
            return { items: [] };
        }

        const result = await this.findByQueryOperation.find(
            {
                'dispute.id': { $in: caseIds },
                'dispute.processor': processor,
                chargeflowId: new ObjectId(chargeflowId),
            },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async getManyByChargeflowIdAndProcessorIdFromLast180Days(
        chargeflowId: string,
        processorId: string,
        session?: ClientSession | undefined,
        page: number = 1,
        pageSize: number = 10,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination?: IPaginationCalculatorResult;
    }> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }
        if (!processorId) {
            throw new Error('Missing processorId');
        }

        const currentDate = new Date();
        const daysAgo180 = new Date();
        daysAgo180.setDate(currentDate.getDate() - 180);

        const result = await this.findByQueryOperation.find(
            {
                'dispute.processorId': processorId,
                'dispute.dateCreated': { $gte: daysAgo180 },
                chargeflowId: new ObjectId(chargeflowId),
            },
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async get(
        id: string,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject> | null> {
        const result = await this.getByIdOperation.get(new ObjectId(id), session);

        return result ? MongoDisputeMapper.fromMongoDispute(result) : null;
    }

    async getExtensionsByDisputeId(
        disputeId: string,
        session?: ClientSession,
    ): Promise<Partial<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            'dispute.id': disputeId,
        };
        const projection = {
            extensions: 1,
        };

        const aggregationPipeline: Document[] = [
            { $match: filter },
            { $project: { ...projection, _id: 1 } },
        ];

        const result = await this.findByQueryOperation.findWithAggregation<
            Partial<IUnifiedDisputeObject>
        >(aggregationPipeline, 1, 1, session);
        return result.items[0];
    }

    async updateExtensionResponseByDisputeId(
        disputeId: string,
        response: unknown,
        processor: Source,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                [`extensions.${processor}.response`]: response,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async updateExtensionAttachmentsByDisputeId(
        disputeId: string,
        attachment: IEvidenceAttachment,
        processor: Source,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                [`extensions.${processor}.attachments`]: attachment,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async addDisputeOrder(
        disputeId: string,
        order: IOrder | null,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                orderName: order?.name ?? 'N/A',
                orderDate: order?.dateCreated ?? null,
                order: order ?? null,
                dateUpdated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async addDisputeTransaction(
        disputeId: string,
        transaction: ITransaction,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                transaction: transaction,
                dateUpdated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async updateTransaction(
        disputeId: string,
        transaction: Partial<IUpdatableTransaction>,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const setObject = Object.entries(transaction).reduce(
            (acc, [ key, value ]) => {
                if (value !== undefined) {
                    acc[`transaction.${key}`] = value;
                }
                return acc;
            },
            {} as Record<string, unknown>,
        );
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                ...setObject,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async updateChargeflowChecks(
        disputeId: string,
        orderShippingAddress?: IAddress,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const projection = {
            'transaction.billing': 1,
        };

        const aggregationPipeline: Document[] = [
            { $match: filter },
            { $project: { ...projection, _id: 1 } },
        ];

        const billing = await this.findByQueryOperation.findWithAggregation<
            Partial<IUnifiedDisputeObject>
        >(aggregationPipeline);
        const isBillingMissing =
            !billing.items ||
            !billing.items[0]?.transaction?.billing ||
            !orderShippingAddress;

        const setObject = isBillingMissing
            ? {
                'chargeflow.checks.doesBillingMatchShipping': false,
                'chargeflow.checks.doesBillingAddress1MatchShipping': false,
                'chargeflow.checks.doesBillingZipMatchShipping': false,
            }
            : {
                'chargeflow.checks.doesBillingMatchShipping':
                    this.checkIfBillingMatchShipping(
                        orderShippingAddress,
                        billing.items[0].transaction!.billing!,
                    ),
                'chargeflow.checks.doesBillingAddress1MatchShipping':
                    this.checkIfBillingAddress1MatchShipping(
                        orderShippingAddress,
                        billing.items[0].transaction!.billing!,
                    ),
                'chargeflow.checks.doesBillingZipMatchShipping':
                    this.checkIfBillingZipMatchShipping(
                        orderShippingAddress,
                        billing.items[0].transaction!.billing!,
                    ),
            };

        const result = await this.updateOperation.updateWithFilter(
            filter,
            {
                $set: {
                    ...setObject,
                    dateUpdated: new Date(),
                },
            },
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    private checkIfBillingMatchShipping(
        shippingAddress: IAddress,
        billingAddress: IBillingDetails,
    ) {
        if (shippingAddress.address1 || billingAddress?.line1) {
            return false;
        }
        return (
            shippingAddress.country === billingAddress?.country &&
            shippingAddress.city === billingAddress?.city &&
            shippingAddress.address1 === billingAddress?.line1 &&
            shippingAddress.address2 === billingAddress?.line2
        );
    }

    private checkIfBillingAddress1MatchShipping(
        shippingAddress: IAddress,
        billingAddress: IBillingDetails,
    ) {
        if (!shippingAddress.address1 || !billingAddress?.line1) {
            return false;
        }
        return shippingAddress.address1 === billingAddress?.line1;
    }

    private checkIfBillingZipMatchShipping(
        shippingAddress: IAddress,
        billingAddress: IBillingDetails,
    ) {
        if (!shippingAddress.zipcode || !billingAddress?.zip) {
            return false;
        }
        return shippingAddress.zipcode === billingAddress?.zip;
    }

    // Be carefull with this function and how it updates
    async updatePartialDisputesBulk(
        disputes: Readonly<Partial<IUpdatableUnifiedDisputeObject>>[],
        session?: ClientSession | undefined,
    ): Promise<IBulkUpdateResponse> {
        if (!disputes || disputes.length === 0) {
            throw new Error('Missing disputes');
        }
        const bulkOps = disputes.flatMap(dispute => {
            if (!dispute._id) {
                throw new Error('Missing disputeId');
            }

            let updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {};

            if (dispute.lastDisputeStatus) {
                updateFilter = {
                    $push: {
                        'dispute.disputeStatuses': dispute.lastDisputeStatus,
                    } as unknown as PushOperator<IMongoUnifiedDisputeObject>,
                };
            }

            const setAggr: Record<string, unknown> = {};
            Object.entries(dispute).forEach(([ key, value ]) => {
                if (key === '_id') {
                    return;
                }
                if (key === 'lastDisputeStatus' && value !== undefined) {
                    setAggr[key] = value;
                } else if (key === 'chargeflow') {
                    Object.entries(value as Record<string, unknown>).forEach(
                        ([ cfKey, cfValue ]) => {
                            if (cfValue !== undefined) {
                                setAggr[`chargeflow.${cfKey}`] = cfValue;
                            }
                        },
                    );
                } else if (key === 'extensions') {
                    buildUpdateObject(
                        'extensions',
                        value as Record<string, unknown>,
                        setAggr,
                    );
                } else if (value !== undefined) {
                    setAggr[`dispute.${key}`] = value;
                }
            });

            updateFilter = {
                ...updateFilter,
                $set: { ...setAggr, dateUpdated: new Date() },
            };

            return [
                // Operation to initialize disputeStatuses to an empty array if it's null
                {
                    updateOne: {
                        filter: {
                            _id: new ObjectId(dispute._id),
                            'dispute.disputeStatuses': null,
                        },
                        update: { $set: { 'dispute.disputeStatuses': [] } },
                    },
                },
                // Operation to push new status and update other fields
                {
                    updateOne: {
                        filter: { _id: new ObjectId(dispute._id) },
                        update: updateFilter,
                        upsert: true,
                    },
                },
            ];
        });
        return await this.updateOperation.bulkUpdate(bulkOps, session);
    }

    /**
   * Updates the 'isActionRequired' field in the dispute object, based on the following conditions:
   * should update to true if handledByChargeflow is true and the lastDisputeStatus is 'needs_response' or 'warning_needs_response' and no evidence has been submitted
   */
    async updateIsActionRequired(
        disputeId: string,
        session?: ClientSession | undefined,
    ): Promise<IUnifiedDisputeObject> {
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };

        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                'chargeflow.merchantActions.isActionRequired': {
                    $cond: {
                        if: {
                            $and: [
                                {
                                    $in: [ '$lastDisputeStatus.status', [ 'needs_response', 'warning_needs_response' ] ],
                                },
                                { $eq: [ '$chargeflow.cfActions.handleByChargeflow', true ] },
                                        {
                                            $eq: [
                                                {
                                                    $size: {
                                                $ifNull: [ '$chargeflow.merchantActions.performedActions', [] ],
                                                    },
                                                },
                                                0,
                                    ],
                                },
                            ],
                        },
                        then: true,
                        else: false,
                    },
                },
                'chargeflow.merchantActions.performedActions': {
                    $ifNull: [ '$chargeflow.merchantActions.performedActions', [] ],
                }, // Set actions to an empty array if it doesn't exist
                dateUpdated: new Date(),
            },
        };

        const result = await this.updateOperation.updateWithFilter(filter, [ updateFilter ], session);

        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async updateOrderProductsImageStoragePath(
        disputeId: string,
        imageUrl: string,
        imageStoragePath: string,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const arrayFilter = {
            'product.imageUrl': imageUrl,
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                'order.products.$[product].imageStoragePath': imageStoragePath,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilterAndArrayFilters(
            filter,
            updateFilter,
            [ arrayFilter ],
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async updateResolvedObject(
        disputeId: string,
        resolvedObject: IChargeflowResolved,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                'chargeflow.resolved': resolvedObject,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async getOpenDisputesByChargeflowIdAndProcessor(
        chargeflowId: string,
        processor: Source,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination?: IPaginationCalculatorResult;
    }> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }
        if (!processor) {
            throw new Error('Missing processor');
        }
        if (!page) {
            page = 1;
        }
        if (!pageSize) {
            pageSize = 10;
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            chargeflowId: new ObjectId(chargeflowId),
            'dispute.processor': processor,
            'chargeflow.resolved.isResolved': false,
        };

        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async stampSuccessRateOnDispute(
        disputeId: string,
        successRate: number,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                'chargeflow.submitted.successRate': successRate,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async getDisputesForDeactivation(
        processorId: string,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination?: IPaginationCalculatorResult;
    }> {
        if (!processorId) {
            throw new Error('Missing processorId');
        }
        if (!page) {
            page = 1;
        }
        if (!pageSize) {
            pageSize = 10;
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            'dispute.processorId': processorId,
            'lastDisputeStatus.status': {
                $in: [
                    'needs_response',
                    'warning_needs_response',
                    'under_review',
                    'warning_under_review',
                    'warning_awaiting_response',
                ],
            },
            'chargeflow.cfActions.handleByChargeflow': true,
        };

        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async getDisputesForReactivation(
        chargeflowId: string,
        processorId: string,
        page?: number,
        pageSize?: number,
        session?: ClientSession,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination?: IPaginationCalculatorResult;
    }> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }
        if (!processorId) {
            throw new Error('Missing processorId');
        }
        if (!page) {
            page = 1;
        }
        if (!pageSize) {
            pageSize = 10;
        }

        const filter: Filter<IMongoUnifiedDisputeObject> = {
            'chargeflowId': new ObjectId(chargeflowId),
            'dispute.processorId': processorId,
            'lastDisputeStatus.status': {
                $in: [
                    'needs_response',
                    'warning_needs_response',
                    'under_review',
                    'warning_under_review',
                    'warning_awaiting_response',
                ],
            },
            'chargeflow.cfActions.handleByChargeflow': false,
        };

        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
        );

        return {
            items: MongoDisputeMapper.fromMongoDisputes(result.items),
            pagination: result.pagination,
        };
    }

    async updateHandleByChargeflow(
        disputeIds: string[],
        handleByChargeflow: boolean,
        session?: ClientSession,
    ): Promise<IBulkUpdateResponse> {
        if (disputeIds && disputeIds.length === 0) {
            throw new Error('Missing dispute ids to update');
        }

        const bulkOps = disputeIds.map(id => {
            return {
                updateOne: {
                    filter: { _id: new ObjectId(id) },
                    update: {
                        $set: {
                            'chargeflow.cfActions.handleByChargeflow': handleByChargeflow,
                            dateUpdated: new Date(),
                        },
                    },
                    upsert: true,
                },
            };
        });
        return await this.updateOperation.bulkUpdate(bulkOps, session);
    }

    async getDisputeIds(
        session?: ClientSession,
        page = 1,
        pageSize = 1000,
    ): Promise<{ ids: string[]; pagination?: IPaginationCalculatorResult }> {
        const projection = { _id: 1 };
        const result = await this.findByQueryOperation.find(
            {},
            page,
            pageSize,
            session,
            projection,
        );
        const ids = result.items.map(i => i._id.toString());
        return { ids, pagination: result.pagination };
    }

    async updatePromotion(
        disputeId: string,
        promotionCode: string,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject>> {
        if (!disputeId && !promotionCode) {
            throw new Error('Missing required parameters');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            _id: new ObjectId(disputeId),
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                promotional: true,
                promotionCode,
                dateUpdated: new Date(),
            },
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        return MongoDisputeMapper.fromMongoDispute(result);
    }

    async getPromotionalDisputes(
        chargeflowId: string,
        promotionCode: string,
        projection?: Document,
        page: number = 1,
        pageSize: number = 500,
        session?: ClientSession,
    ): Promise<{
        items: WithId<IUnifiedDisputeObject>[];
        pagination: IPaginationCalculatorResult;
    }> {
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            chargeflowId: new ObjectId(chargeflowId),
            promotionCode,
        };
        const result = await this.findByQueryOperation.find(
            filter,
            page,
            pageSize,
            session,
            projection,
        );
        const items = result.items.map(MongoDisputeMapper.fromMongoDispute);
        return { items, pagination: result.pagination };
    }

    async updateDisputeStatusToPrevented(
        data: UpdateDisputeStatusToPreventedPayload,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject> | undefined> {
        const { chargeflowId, transactionId, alertId } = data;
        if (!chargeflowId || !transactionId || !alertId) {
            throw new Error('Missing required parameters');
        }
        const filter: Filter<IMongoUnifiedDisputeObject> = {
            chargeflowId: new ObjectId(chargeflowId),
            'transaction.id': transactionId,
            'lastDisputeStatus.status': ChargeFlowDisputeStatus.Lost,
        };
        const disputeStatus: IDisputeStatus = {
            dateCreated: new Date(),
            statusDate: new Date(),
            status: ChargeFlowDisputeStatus.Prevented,
            processorStatus: null,
            source: DisputeStatusSource.AlertsLinking,
        };
        const updateFilter: UpdateFilter<IMongoUnifiedDisputeObject> = {
            $set: {
                lastDisputeStatus: disputeStatus,
                alertId: alertId,
                dateUpdated: new Date(),
            },
            $push: {
                'dispute.disputeStatuses': disputeStatus,
            } as unknown as PushOperator<IMongoUnifiedDisputeObject>,
        };
        const result = await this.updateOperation.updateWithFilter(
            filter,
            updateFilter,
            session,
        );
        if (result) {
            return MongoDisputeMapper.fromMongoDispute(result);
        }
    }

    async updateAllDisputesBillingStatus(
        chargeflowId: string,
        billingStatus: boolean,
        session?: ClientSession,
    ): Promise<IBulkUpdateResponse> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }

        const updateMany = [
            {
                updateMany: {
                    filter: {
                        chargeflowId: new ObjectId(chargeflowId),
                    },
                    update: {
                        $set: {
                            isBillingActive: billingStatus,
                            dateUpdated: new Date(),
                        },
                    },
                },
            },
        ];
        const result = await this.updateOperation.bulkUpdate(updateMany, session);
        return result;
    }

    async updateDisputeWonEventPublished(
        disputeId: string,
        session?: ClientSession,
    ): Promise<void> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }

        await this.updateOperation.updateWithFilter(
            { _id: new ObjectId(disputeId) },
            {
                $set: {
                    isDisputeWonEventPublished: true,
                    dateUpdated: new Date(),
                },
            },
            session,
        );
    }

    async updateLinkingTypeMetadata(chargeflowId: string, disputeId: string, linkingTypeMetadata: ILinkingTypeMetadata, session?: ClientSession): Promise<void> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        await this.updateOperation.updateWithFilter(
            { chargeflowId: new ObjectId(chargeflowId), _id: new ObjectId(disputeId) },
            {
                $set: {
                    'metadata.orderLinkMethod': linkingTypeMetadata.orderLinkMethod,
                    'metadata.orderLinkType': linkingTypeMetadata.orderLinkType,
                    dateUpdated: new Date(),
                },
            },
            session,
        );
    }

    async getDistinctProcessorsFromDisputes(chargeflowId: string, session?: ClientSession): Promise<string[] | number[]> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }

        const result = await this.findByQueryOperation.findDistinct('dispute.processor', { chargeflowId: new ObjectId(chargeflowId) }, session);
        return result;
    }

    async updatePerformedAction(disputeId: string, performedAction: boolean = true, session?: ClientSession): Promise<void> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        await this.updateOperation.updateWithFilter(
            { _id: new ObjectId(disputeId) },
            {
                $set: {
                    'chargeflow.cfActions.chargeflowPerformedAction': performedAction,
                },
            },
            session,
        );
    }

    async getByChargeflowIdAndProcessorIdAndCaseId(
        chargeflowId: string,
        processorId: string,
        caseId: string,
        session?: ClientSession,
    ): Promise<WithId<IUnifiedDisputeObject> | null> {
        const result = await this.findByQueryOperation.findOne({
            chargeflowId: new ObjectId(chargeflowId),
            'dispute.processorId': processorId,
            'dispute.id': caseId,
        }, session);

        return result ? MongoDisputeMapper.fromMongoDispute(result) : null;
    }

    async updateOrderFinancialStatus(disputeId: string, financialStatus: string, session?: ClientSession): Promise<void> {
        if (!disputeId) {
            throw new Error('Missing disputeId');
        }
        const result = await this.updateOperation.updateWithFilter(
            { _id: new ObjectId(disputeId) },
            [
                {
                    $set: {
                        order: {
                            $ifNull: [ '$order', {} ],
                        },
                    },
                },
                {
                    $set: {
                        'order.financialStatus': financialStatus,
                    },
                },
            ],
            session,
        );
        if (!result) {
            throw new Error('Failed to update order financial status');
        }
    }
}
