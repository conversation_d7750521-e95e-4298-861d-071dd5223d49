import { mock } from 'jest-mock-extended';
import { MongoChargeflowIdRepository } from './MongoChargeflowIdRepository';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IChargeflowId } from './IChargeflowId';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import chargeflowIdMocks from './__mocks__/ChargeflowId';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { ClientSession, ObjectId } from 'mongodb';

describe(MongoChargeflowIdRepository.name, () => {
    let mongoChargeflowIdRepository: MongoChargeflowIdRepository;
    const updateOperation = mock<IUpdateOperation<IChargeflowId>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IChargeflowId>>();
    const session = mock<ClientSession>();

    beforeEach(() => {
        mongoChargeflowIdRepository = new MongoChargeflowIdRepository(
            updateOperation,
            findByQueryOperation,
        );
    });

    describe(MongoChargeflowIdRepository.prototype.assignAccountId.name, () => {
        it('returns ChargeflowId object with assigned accountId', async () => {
            expect.assertions(3);

            // GIVEN
            const id = new ObjectId();
            const accountId = new ObjectId();

            const expectedResult = chargeflowIdMocks.getChargeflowId({
                _id: id,
                accountId,
            });

            updateOperation.update.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoChargeflowIdRepository
                .assignAccountId(id, accountId, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(updateOperation.update).toHaveBeenCalledTimes(1);
            expect(updateOperation.update).toHaveBeenCalledWith({
                _id: id,
                accountId,
                date_updated: expect.any(Date),
            }, session);
        });
    });

    describe(MongoChargeflowIdRepository.prototype.findWithoutAccountId.name, () => {
        it('returns paginated result of ChargeflowId objects without accountId', async () => {
            expect.assertions(3);

            // GIVEN
            const page = 1;
            const pageSize = 2;
            const chargeflowId1 = chargeflowIdMocks.getChargeflowId();
            delete chargeflowId1.accountId;
            const chargeflowId2 = chargeflowIdMocks.getChargeflowId();
            delete chargeflowId2.accountId;
            const pagination = paginationMocks.getPaginationResult();

            const expectedResult: IPaginatedFindByQueryResult<IChargeflowId> = {
                items: [ chargeflowId1, chargeflowId2 ],
                pagination,
            };

            findByQueryOperation.find.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoChargeflowIdRepository
                .findWithoutAccountId(page, pageSize, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({
                accountId: { $exists: false },
            }, page, pageSize, session);
        });
    });
});
