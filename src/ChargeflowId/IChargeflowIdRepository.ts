import { ClientSession, ObjectId } from 'mongodb';
import { IChargeflowId } from './IChargeflowId';
import { IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';

export interface IChargeflowIdRepository {
    assignAccountId(
        id: ObjectId, accountId: ObjectId, clientSession?: ClientSession
    ): Promise<IChargeflowId>,

    findWithoutAccountId(
        page: number, pageSize: number, clientSession?: ClientSession
    ): Promise<IPaginatedFindByQueryResult<IChargeflowId>>,
}
