import { mock } from 'jest-mock-extended';
import { ChargeflowIdRepositoryFactory } from './ChargefowIdRepositoryFactory';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { MongoChargeflowIdRepository } from './MongoChargeflowIdRepository';

describe(ChargeflowIdRepositoryFactory.name, () => {
    describe(ChargeflowIdRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = ChargeflowIdRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoChargeflowIdRepository);
        });
    });
});
