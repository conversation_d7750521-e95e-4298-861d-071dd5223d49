import { MongoClient } from 'mongodb';
import { IChargeflowIdRepository } from './IChargeflowIdRepository';
import { MongoChargeflowIdRepository } from './MongoChargeflowIdRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { IChargeflowId } from './IChargeflowId';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';

export class ChargeflowIdRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IChargeflowIdRepository {
        const updateOperation = new MongoUpdateOperation<IChargeflowId>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IChargeflowId>(mongoClient, dbName, collectionName);

        return new MongoChargeflowIdRepository(updateOperation, findByQueryOperation);
    }
}
