import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IChargeflowId } from './IChargeflowId';
import { IChargeflowIdRepository } from './IChargeflowIdRepository';

export class MongoChargeflowIdRepository implements IChargeflowIdRepository {
    constructor(
        private readonly updateOperation: IUpdateOperation<IChargeflowId>,
        private readonly findByQueryOperation: IFindByQueryOperation<IChargeflowId>,
    ) {}

    assignAccountId(
        id: ObjectId, accountId: ObjectId, session?: ClientSession,
    ): Promise<IChargeflowId> {
        return this.updateOperation.update({
            _id: id,
            accountId,
            date_updated: new Date(),
        }, session);
    }

    findWithoutAccountId(
        page: number, pageSize: number, session?: ClientSession,
    ): Promise<IPaginatedFindByQueryResult<IChargeflowId>> {
        return this.findByQueryOperation.find({
            accountId: { $exists: false },
        }, page, pageSize, session);
    }
}
