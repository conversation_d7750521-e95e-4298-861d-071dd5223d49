import { IChargeflowId } from '../IChargeflowId';
import { ObjectId } from 'mongodb';

const getChargeflowId = (overrides?: Partial<IChargeflowId>): IChargeflowId => ({
    _id: new ObjectId(),
    account_email: '<EMAIL>',
    customer_id: new ObjectId(),
    shop_id: new ObjectId(),
    shop_name: 'Test shop name',
    date_created: new Date(),
    date_updated: new Date(),
    accountId: new ObjectId(),
    ...overrides,
});

export default {
    getChargeflowId,
};
