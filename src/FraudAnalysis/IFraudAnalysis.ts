import { ObjectId, Document } from 'mongodb';

export interface IAutomationCriteria {
  disputeReason: string;
  disputeProcessor: string;
  disputeStage: string;
  orderName: string;
  storeType: string;
  productType: string;
}

export interface IAddressMatch {
  doesBillingMatchShipping: string;
  doesBillingAddress1MatchShipping: string;
  doesBillingZipMatchShipping: string;
}

export interface IStatus {
  status: string | null;
}

export interface ITransactionsAttempts {
  attempts: number;
  cards: string | boolean;
  declines?: unknown[] | null;
}

export interface I3DSecure {
  status: string | null;
  statusReason: string | null;
}

export interface IPastOrderDetails {
  orderId: number;
  ipAddress: string;
  source: string;
  dispute: {
    id: string | null;
    reason: string | null;
    _id: string | null;
  };
  financialStatus: string;
  fingerprintMatchOrder: {
    pastOrderFingerprint: string;
    status: string;
  };
  customerEmailMatchOrder: {
    pastOrderCustomerEmail: string;
    status: string;
  };
  customerPhoneMatchOrder: {
    pastOrderCustomerPhone: string;
    status: string;
  };
  creditCardMatchOrder: {
    pastOrderBinNumber: string;
    pastOrderLast4: string;
    status: string;
  };
  cardholderMatchOrder: {
    pastOrderCardholderName: string;
    status: string;
  };
  shippingAddressMatchOrder: {
    pastOrderShippingAddress: string;
    pastOrderShippingZipCode: string;
    pastOrderShippingAddress1: string;
    pastOrderShippingCountry: string;
    status: string;
  };
  billingAddressMatchOrder: {
    pastOrderBillingAddress: string;
    pastOrderBillingZipCode: string;
    pastOrderBillingAddress1: string;
    pastOrderBillingCountry: string;
    status: string;
  };
  ipAddressMatchOrder: {
    status: string;
  };
  avsClassification: {
    pastOrderAvsCode: string;
    pastOrderAvsDescription: string;
    status: string;
  };
  cvcClassification: {
    pastOrderCvcCode: string | null;
    pastOrderCvcDescription: string;
    status: string;
  };
  cardCountryMatchOrderShipping: {
    pastOrderCardShippingCountry: string;
    status: string;
  };
  cardCountryMatchOrderBilling: {
    pastOrderCardBillingCountry: string;
    status: string;
  };
  transactionsAttempts: {
    attempts: number;
    cards: boolean;
  };
  '3dSecure': {
    status: string | null;
    statusResult: string | null;
  };
}

export interface IAccountAssociation {
  accountCity: string | null;
  status: string | null;
}

export interface IEmailAssociated {
  facebook?: IAccountAssociation;
  gravatar?: IAccountAssociation;
  linkedin?: IAccountAssociation;
  skype?: IAccountAssociation;
}

export interface IPhoneAssociated {
  ok?: IAccountAssociation;
  skype?: IAccountAssociation;
}

export interface IEmailPhoneMatch {
  emailAssociated: IEmailAssociated[] | null;
  phoneAssociated: IPhoneAssociated[] | null;
}

export interface IInnerFraudAnalysis {
  addresses: IAddressMatch;
  customerNameMatchCardholderName: IStatus;
  avsClassification: IStatus;
  cvcClassification: IStatus;
  cardCountryMatchShipping: IStatus;
  cardCountryMatchBilling: IStatus;
  cardCountryMatchIpCountry: IStatus;
  customerNameMatchEmail: IStatus;
  transactionsAttempts: ITransactionsAttempts;
  '3dSecure': I3DSecure;
  abandonedCheckouts: {
    status: string;
  };
  accountsCityMatchBillingCity?: IEmailPhoneMatch;
  accountsCityMatchShippingCity?: IEmailPhoneMatch;
  accountsNameMatchCardholderName?: IEmailPhoneMatch;
  accountsNameMatchCustomerName?: IEmailPhoneMatch;
  pastOrdersInsights: {
    details: IPastOrderDetails;
  }[];
  declines?: unknown[] | null;
  ipCityMatchBillingCity: IStatus;
  ipCityMatchShippingCity: IStatus;
  ipCountryMatchBillingCountry: IStatus;
  ipCountryMatchShippingCountry: IStatus;
  phoneCountryMatchBillingCountry: IStatus;
  phoneCountryMatchIpCountry: IStatus;
  phoneCountryMatchShippingCountry: IStatus;
}

export interface IFraudAnalysisBase {
  automationCriteria: IAutomationCriteria;
  caseId: string;
  dateCreated: Date;
  dateUpdated: Date | null;
  fraudAnalysis: IInnerFraudAnalysis;
  orderId: number;
  orderLinked: boolean;
  shopName: string;
  shopPlatform: string;
}

export interface IFraudAnalysis extends IFraudAnalysisBase {
  _id?: string;
  disputeId?: string;
  chargeflowId?: string;
  accountId?: string | null;
  shopId?: string;
}

export interface IMongoFraudAnalysis extends IFraudAnalysisBase, Document {
  _id?: ObjectId;
  disputeId?: ObjectId;
  chargeflowId?: ObjectId;
  accountId?: ObjectId | null;
  shopId?: ObjectId;
}
