import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFraudAnalysis } from './IFraudAnalysis';

export interface IFraudAnalysisRepository {
    findOneFraudAnalysisDocumentByDisputeId: (
        chargeflowId: string, session?: ClientSession
    ) => Promise<WithId<IFraudAnalysis> | null>,

    saveFraudAnalysisDocument: (
        fraudAnalysis:IFraudAnalysis, session?: ClientSession
    ) => Promise<WithId<IFraudAnalysis> | null>,
}
