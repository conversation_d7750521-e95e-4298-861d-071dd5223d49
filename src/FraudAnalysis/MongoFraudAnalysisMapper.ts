
import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoFraudAnalysis, IFraudAnalysis } from './IFraudAnalysis';

export class MongoFraudAnalysisMapper {
    static toMongoFraudAnalysis(FraudAnalysis: IFraudAnalysis): IMongoFraudAnalysis {
        if (!FraudAnalysis) {
            throw new Error('FraudAnalysis is required for mapping');
        }

        const { _id, ...fraudAnalysis } = FraudAnalysis;
        const mappedFraudAnalysis: IMongoFraudAnalysis = {
            ...fraudAnalysis,
            disputeId: FraudAnalysis.disputeId ? new ObjectId(FraudAnalysis.disputeId) : undefined,
            chargeflowId: FraudAnalysis.chargeflowId ? new ObjectId(FraudAnalysis.chargeflowId) : undefined,
            accountId: FraudAnalysis.accountId ? new ObjectId(FraudAnalysis.accountId) : undefined,
            shopId: FraudAnalysis.shopId ? new ObjectId(FraudAnalysis.shopId) : undefined,
        };

        if (_id) {
            mappedFraudAnalysis._id = new ObjectId(_id);
        }

        return mappedFraudAnalysis;
    }

    static fromMongoFraudAnalysis(mongoFraudAnalysis: MongoWithId<IMongoFraudAnalysis>): WithId<IFraudAnalysis> {
        if (!mongoFraudAnalysis) {
            throw new Error('FraudAnalysis not found');
        }

        return {
            ...mongoFraudAnalysis,
            _id: mongoFraudAnalysis._id.toHexString(),
            disputeId: mongoFraudAnalysis.disputeId?.toHexString(),
            chargeflowId: mongoFraudAnalysis.chargeflowId?.toHexString(),
            accountId: mongoFraudAnalysis.accountId?.toHexString(),
            shopId: mongoFraudAnalysis.shopId?.toHexString(),
        };
    }
}
