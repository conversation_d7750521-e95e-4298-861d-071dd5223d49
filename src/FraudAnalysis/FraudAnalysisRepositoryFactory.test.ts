import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { FraudAnalysisRepositoryFactory } from './FraudAnalysisRepositoryFactory';
import { MongoFraudAnalysisRepository } from './MongoFraudAnalysisRepository';

describe(FraudAnalysisRepositoryFactory.name, () => {
    describe(FraudAnalysisRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = FraudAnalysisRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoFraudAnalysisRepository);
        });
    });
});
