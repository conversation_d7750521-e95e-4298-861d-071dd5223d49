import { ClientSession, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IFraudAnalysis, IMongoFraudAnalysis } from './IFraudAnalysis';
import { IFraudAnalysisRepository } from './IFraudAnalysisRepository';
import { MongoFraudAnalysisMapper } from './MongoFraudAnalysisMapper';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoFraudAnalysisRepository implements IFraudAnalysisRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoFraudAnalysis>,
        private readonly updateOperation: IUpdateOperation<IMongoFraudAnalysis>,
    ) {}

    async findOneFraudAnalysisDocumentByDisputeId(
        disputeId: string, session?: ClientSession,
    ): Promise<WithId<IFraudAnalysis> | null> {
        const result = await this.findByQueryOperation
            .findOne({ 'disputeId': new ObjectId(disputeId) }, session);

        return result ? MongoFraudAnalysisMapper.fromMongoFraudAnalysis(result) : null;
    }

    async saveFraudAnalysisDocument(
        fraudAnalysis: IFraudAnalysis, session?: ClientSession,
    ): Promise<WithId<IFraudAnalysis> | null> {
        const result = await this.updateOperation.upsert(
            { 'disputeId': new ObjectId(fraudAnalysis.disputeId) },
            { $set: MongoFraudAnalysisMapper.toMongoFraudAnalysis(fraudAnalysis) },
            session,
        );

        return result ? MongoFraudAnalysisMapper.fromMongoFraudAnalysis(result) : null;
    }
}
