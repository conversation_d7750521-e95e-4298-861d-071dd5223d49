import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoFraudAnalysis } from './IFraudAnalysis';
import { IFraudAnalysisRepository } from './IFraudAnalysisRepository';
import { MongoFraudAnalysisRepository } from './MongoFraudAnalysisRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class FraudAnalysisRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IFraudAnalysisRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoFraudAnalysis>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoFraudAnalysis>(mongoClient, dbName, collectionName);

        return new MongoFraudAnalysisRepository(
            findByQueryOperation,
            updateOperation,
        );
    }
}
