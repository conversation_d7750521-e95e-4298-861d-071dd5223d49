import { ObjectId, WithId } from 'mongodb';
import { IFraudAnalysis } from '../IFraudAnalysis';

const getFraudAnalysis = (
    overrides: Partial<IFraudAnalysis> = {},
): WithId<IFraudAnalysis> => ({
    _id: new ObjectId().toHexString(),
    disputeId: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    shopId: new ObjectId().toHexString(),
    automationCriteria: {
        disputeReason: 'fraud',
        disputeProcessor: 'processor1',
        disputeStage: 'stage1',
        orderName: 'order123',
        storeType: 'online',
        productType: 'electronics',
    },
    caseId: 'case123',
    dateCreated: new Date(),
    dateUpdated: new Date(),
    fraudAnalysis: {
        addresses: {
            doesBillingMatchShipping: 'yes',
            doesBillingAddress1MatchShipping: 'yes',
            doesBillingZipMatchShipping: 'yes',
        },
        customerNameMatchCardholderName: { status: 'match' },
        avsClassification: { status: 'pass' },
        cvcClassification: { status: 'pass' },
        cardCountryMatchShipping: { status: 'match' },
        cardCountryMatchBilling: { status: 'match' },
        cardCountryMatchIpCountry: { status: 'match' },
        customerNameMatchEmail: { status: 'match' },
        transactionsAttempts: {
            attempts: 1,
            cards: true,
            declines: [],
        },
        '3dSecure': {
            status: 'pass',
            statusReason: 'verified',
        },
        abandonedCheckouts: { status: 'none' },
        pastOrdersInsights: [
            {
                details: {
                    orderId: 12345,
                    ipAddress: '***********',
                    source: 'web',
                    dispute: {
                        id: 'dispute123',
                        reason: 'fraud',
                        _id: 'dispute123',
                    },
                    financialStatus: 'paid',
                    fingerprintMatchOrder: {
                        pastOrderFingerprint: 'fingerprint123',
                        status: 'match',
                    },
                    customerEmailMatchOrder: {
                        pastOrderCustomerEmail: '<EMAIL>',
                        status: 'match',
                    },
                    customerPhoneMatchOrder: {
                        pastOrderCustomerPhone: '1234567890',
                        status: 'match',
                    },
                    creditCardMatchOrder: {
                        pastOrderBinNumber: '123456',
                        pastOrderLast4: '7890',
                        status: 'match',
                    },
                    cardholderMatchOrder: {
                        pastOrderCardholderName: 'John Doe',
                        status: 'match',
                    },
                    shippingAddressMatchOrder: {
                        pastOrderShippingAddress: '123 Main St',
                        pastOrderShippingZipCode: '12345',
                        pastOrderShippingAddress1: 'Apt 1',
                        pastOrderShippingCountry: 'US',
                        status: 'match',
                    },
                    billingAddressMatchOrder: {
                        pastOrderBillingAddress: '123 Main St',
                        pastOrderBillingZipCode: '12345',
                        pastOrderBillingAddress1: 'Apt 1',
                        pastOrderBillingCountry: 'US',
                        status: 'match',
                    },
                    ipAddressMatchOrder: { status: 'match' },
                    avsClassification: {
                        pastOrderAvsCode: 'A',
                        pastOrderAvsDescription: 'Address matches',
                        status: 'pass',
                    },
                    cvcClassification: {
                        pastOrderCvcCode: 'M',
                        pastOrderCvcDescription: 'CVC matches',
                        status: 'pass',
                    },
                    cardCountryMatchOrderShipping: {
                        pastOrderCardShippingCountry: 'US',
                        status: 'match',
                    },
                    cardCountryMatchOrderBilling: {
                        pastOrderCardBillingCountry: 'US',
                        status: 'match',
                    },
                    transactionsAttempts: {
                        attempts: 1,
                        cards: true,
                    },
                    '3dSecure': {
                        status: 'pass',
                        statusResult: 'verified',
                    },
                },
            },
        ],
        declines: [],
        ipCityMatchBillingCity: { status: 'match' },
        ipCityMatchShippingCity: { status: 'match' },
        ipCountryMatchBillingCountry: { status: 'match' },
        ipCountryMatchShippingCountry: { status: 'match' },
        phoneCountryMatchBillingCountry: { status: 'match' },
        phoneCountryMatchIpCountry: { status: 'match' },
        phoneCountryMatchShippingCountry: { status: 'match' },
    },
    orderId: 12345,
    orderLinked: true,
    shopName: 'Shop123',
    shopPlatform: 'Shopify',
    ...overrides,
});

export default {
    getFraudAnalysis,
};
