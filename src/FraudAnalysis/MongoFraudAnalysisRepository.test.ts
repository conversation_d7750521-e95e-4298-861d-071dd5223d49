import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoFraudAnalysis } from './IFraudAnalysis';
import { MongoFraudAnalysisMapper } from './MongoFraudAnalysisMapper';
import { MongoFraudAnalysisRepository } from './MongoFraudAnalysisRepository';
import FraudAnalysisMocks from './__mocks__/FraudAnalysis';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

describe(MongoFraudAnalysisRepository.name, () => {
    let mongoFraudAnalysisRepository: MongoFraudAnalysisRepository;
    const findByQueryOperation =
    mock<IFindByQueryOperation<IMongoFraudAnalysis>>();
    const updateOperation = mock<IUpdateOperation<IMongoFraudAnalysis>>();

    beforeEach(() => {
        mongoFraudAnalysisRepository = new MongoFraudAnalysisRepository(
            findByQueryOperation, updateOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(
        MongoFraudAnalysisRepository.prototype
            .findOneFraudAnalysisDocumentByDisputeId.name,
        () => {
            it('returns shop settings object', async () => {
                expect.assertions(3);

                // GIVEN
                const disputeId = new ObjectId();
                const FraudAnalysis = FraudAnalysisMocks.getFraudAnalysis();
                const session = mock<ClientSession>();

                const findByOneResult = MongoFraudAnalysisMapper.toMongoFraudAnalysis(
                    FraudAnalysis,
                ) as WithId<IMongoFraudAnalysis>;

                findByQueryOperation.findOne.mockResolvedValue(findByOneResult);

                // WHEN
                const result =
          await mongoFraudAnalysisRepository.findOneFraudAnalysisDocumentByDisputeId(
              disputeId.toHexString(),
              session,
          );

                // THEN
                expect(result).toEqual(FraudAnalysis);

                expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
                expect(findByQueryOperation.findOne).toHaveBeenCalledWith({
                    disputeId: disputeId,
                }, session);
            });
        },
    );

    describe(
        MongoFraudAnalysisRepository.prototype.saveFraudAnalysisDocument.name,
        () => {
            it('returns shop settings object', async () => {
                expect.assertions(3);

                // GIVEN
                const FraudAnalysis = FraudAnalysisMocks.getFraudAnalysis();
                const session = mock<ClientSession>();

                const upsertResult = MongoFraudAnalysisMapper.toMongoFraudAnalysis(
                    FraudAnalysis,
                ) as WithId<IMongoFraudAnalysis>;

                updateOperation.upsert.mockResolvedValue(upsertResult);

                // WHEN
                const result =
                    await mongoFraudAnalysisRepository.saveFraudAnalysisDocument(
                        FraudAnalysis,
                        session,
                    );

                // THEN
                expect(result).toEqual(FraudAnalysis);

                expect(updateOperation.upsert).toHaveBeenCalledTimes(1);
                expect(updateOperation.upsert).toHaveBeenCalledWith(
                    { disputeId: new ObjectId(FraudAnalysis.disputeId) },
                    { $set: MongoFraudAnalysisMapper.toMongoFraudAnalysis(FraudAnalysis) },
                    session,
                );
            });
        },
    );

});
