import { MongoClient,ObjectId } from 'mongodb';

let client: MongoClient;

interface IMongoOptions {
    useNewUrlParser: boolean;
    useUnifiedTopology: boolean;
    auth?: {
        username: string;
        password: string;
    };
    appName?: string;
}

async function initClient(mongoUri: string) {
    const options: IMongoOptions = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    };

    if (!process.env.AWS_SAM_LOCAL) {
        options.auth = process.env.MONGO_AUTH
            ? JSON.parse(process.env.MONGO_AUTH)
            : {};

        if (!options?.auth?.username || !options?.auth?.password) {
            throw new Error('Malformed Mongo credentials');
        }
    }

    if (process.env.AWS_LAMBDA_FUNCTION_NAME) {
        options.appName = process.env.AWS_LAMBDA_FUNCTION_NAME;
    }

    return await MongoClient.connect(mongoUri, options);
}

async function getCreateGlobalMongoClient(
    isNewInstance = false,
    mongoUri: string,
) {
    const isUnderTest = !!process.env.npm_lifecycle_event?.startsWith('test');

    if (!client || (isUnderTest && isNewInstance)) {
        client = await initClient(mongoUri);
    }

    return client;
}

export async function getMongoClient(isNewInstance = false) {
    return getCreateGlobalMongoClient(
        isNewInstance,
        process.env.MONGO_URI as string,
    );
}

export async function getMongoReadOnlyClient(isNewInstance = false) {
    return getCreateGlobalMongoClient(
        isNewInstance,
        process.env.MONGO_URI_READ_ONLY as string,
    );
}

export { ObjectId };
