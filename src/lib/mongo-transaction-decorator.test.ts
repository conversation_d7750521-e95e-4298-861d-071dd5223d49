import { MongoMemoryReplSet } from 'mongodb-memory-server';
import { ClientSession, MongoClient, ObjectId } from 'mongodb';
import { MongoConfig } from './MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { Id } from '../shared/helper-types';
import { withMongoTransaction } from './mongo-transaction-decorator';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';

describe('Transaction decorator test', () => {
    const TestCollectionName = 'test';
    let mongoServer: MongoMemoryReplSet;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');
    let testRepository: TestRepository;
    let testAddOperation: IAddOperation<TestEntity>;

    beforeAll(async () => {
        mongoServer = await MongoMemoryReplSet.create({
            replSet: { count: 1 },
        });
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
    });

    beforeEach(() => {
        testAddOperation = new MongoAddOperation<TestEntity>(mongoClient, ChargeflowDbName, TestCollectionName);
        testRepository = new TestRepository(mongoClient, testAddOperation);
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName)
            .collection(TestCollectionName)
            .deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    test('should store two entities successfully when no error occurs', async () => {
        // given
        const test:TestEntity = { name: 'test' };
        const test2:TestEntity = { name: 'test' };

        // when
        const ids = await testRepository.addMany(test, test2);

        // then
        expect(ids.length).toEqual(2);

        // and
        const result = await mongoClient.db(ChargeflowDbName)
            .collection(TestCollectionName)
            .find({})
            .toArray();
        expect(result.length).toEqual(2);
    });

    test('should store multiple entities within a single active transaction', async () => {
        // given
        const test:TestEntity = { name: 'test' };
        const test2:TestEntity = { name: 'test' };
        const test3:TestEntity = { name: 'test' };

        // when
        const ids = await testRepository.addManyWithTransaction(test, test2, test3);

        // then
        expect(ids.length).toEqual(3);

        // and
        const result = await mongoClient.db(ChargeflowDbName)
            .collection(TestCollectionName)
            .find({})
            .toArray();
        expect(result.length).toEqual(3);
    });

    test('should store multiple entities within a transaction with manual session handling', async () => {
        // given
        const test:TestEntity = { name: 'test' };
        const test2:TestEntity = { name: 'test' };
        const test3:TestEntity = { name: 'test' };

        // when
        const ids = await testRepository.addManyWithManualTransaction(test, test2, test3);

        // then
        expect(ids.length).toEqual(3);

        // and
        const result = await mongoClient.db(ChargeflowDbName)
            .collection(TestCollectionName)
            .find({})
            .toArray();
        expect(result.length).toEqual(3);
    });

    test('should not store any data if an error occurs during transaction', async () => {
        // given
        const test:TestEntity = { name: 'test' };
        const test2:TestEntity = { name: 'test' };
        jest.spyOn(testAddOperation, 'add').mockRejectedValue(new Error('error'));

        // expect
        await expect(testRepository.addMany(test, test2)).rejects.toThrow('error');

        // and
        const result = await mongoClient.db(ChargeflowDbName)
            .collection(TestCollectionName)
            .find({})
            .toArray();
        expect(result.length).toEqual(0);
    });
});

interface TestEntity {
    _id?: ObjectId;
    name: string;
}

class TestRepository {
    constructor(
        private readonly mongoClient: MongoClient,
        private readonly testAddOperation: IAddOperation<TestEntity>,
    ) {}

    @withMongoTransaction
    async addManyWithTransaction(
        test: TestEntity, test2: TestEntity, test3: TestEntity, session?: ClientSession,
    ): Promise<Id[]> {
        const id = await this.addOne(test, session);
        const ids = await this.addMany(test2, test3, session);
        return [ id, ...ids ];
    }

    async addManyWithManualTransaction(
        test: TestEntity, test2:TestEntity, test3:TestEntity,
    ): Promise<Id[]> {
        const session = this.mongoClient.startSession();
        session.startTransaction();
        try {
            const id = await this.addOne(test, session);
            const ids = await this.addMany(test2, test3, session);
            await session.commitTransaction();
            return [ id, ...ids ];
        } catch (error: any) {
            await session.abortTransaction();
            throw error;
        } finally {
            await session.endSession();
        }
    }

    @withMongoTransaction
    async addOne(
        test: TestEntity, session?: ClientSession,
    ): Promise<Id> {
        const result = await this.testAddOperation.add(test, session);
        return { _id: result._id.toHexString() };
    }

    @withMongoTransaction
    async addMany(
        test: TestEntity, test2:TestEntity, session?: ClientSession,
    ): Promise<Id[]> {
        const result = await this.testAddOperation.add(test, session);
        const result2 = await this.testAddOperation.add(test2, session);
        return [ { _id: result._id.toHexString() }, { _id: result2._id.toHexString() } ];
    }
}
