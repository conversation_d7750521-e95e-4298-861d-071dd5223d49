import { ClientSession, MongoClient } from 'mongodb';

export function withMongoTransaction(
    target: any,
    key: string,
    descriptor: PropertyDescriptor,
): PropertyDescriptor {
    const originalMethod = descriptor.value;

    descriptor.value = async function(...args: any[]) {
        const mongoClient: MongoClient = (this as any).mongoClient;
        if (!mongoClient) {
            throw new Error(
                'InternalServerError' +
                'MongoClient not found in context',
            );
        }
        const existingSession = args.find(arg => arg instanceof ClientSession) as ClientSession | undefined;
        const session = existingSession || mongoClient.startSession();
        if (!session) {
            throw new Error(
                'InternalServerError' +
                'MongoClient session not found in context',
            );
        }

        const hasActiveTransaction = session.inTransaction();
        try {
            if (!hasActiveTransaction) {
                console.info('Starting new transaction');
                session.startTransaction();
            } else {
                console.info('Using active transaction');
            }
            const result = await originalMethod.apply(this, [ ...args, session ]);
            if (!hasActiveTransaction) {
                await session.commitTransaction();
                console.info('Committed transaction');
            }
            return result;
        } catch (error: any) {
            console.error('InternalServerError', `Failed to execute ${key} in transaction:`, error);
            if (!hasActiveTransaction) {
                await session.abortTransaction();
                console.error('InternalServerError', `Aborted transaction for ${key}`);
            }
            throw error;
        } finally {
            if (!hasActiveTransaction && !existingSession) {
                await session.endSession();
            }
        }
    };

    return descriptor;
}
