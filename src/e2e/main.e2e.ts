import 'dotenv/config';

import { MongoConfig, ParamsConfig } from '@chargeflow-team/chargeflow-utils-sdk';
import { MongoClient } from 'mongodb';
import { IPlatformRepository } from '../Platform/IPlatformRepository';

export type IContext = { mongoClient: MongoClient | null };

export type ICallback = (context: any) => Promise<any>;

const context: IContext = { mongoClient: null };

const before = async () => {
    await ParamsConfig.loadParameters();
    context.mongoClient = await MongoConfig.getMongoClient();
};

const after = async () => {
    if (context.mongoClient) {
        await context.mongoClient.close(true);
    }
};

export const main = async (callback: ICallback) => {
    return before()
        .then(() => callback(context))
        .then(console.log)
        .catch(console.error)
        .finally(after);
};

export const hashID = (size: number) => {
    const letters = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '1234567890';
    const charset = `${numbers}${letters}${letters.toUpperCase()}`.split('');

    const bytes = new Uint8Array(size);
    crypto.getRandomValues(bytes);

    return bytes.reduce((acc, byte) => `${acc}${charset[byte & 0x3d]}`, '');
};

export const findOrCreatePlatform_getId = async (platform: IPlatformRepository, name: string): Promise<string> => {
    const platformResult = await platform.findOneByName(name);
    if (platformResult) {
        return platformResult._id;
    } else {
        const res = await platform.insert({ name });
        return res._id;
    }
};
