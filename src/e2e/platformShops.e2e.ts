import * as assert from 'node:assert';
import { findOrCreatePlatform_getId, IContext, main } from './main.e2e';
import { DataAccessUnitOfWork } from '../DataAccessUnitOfWork';
import { ObjectId } from 'mongodb';

main(async (context: IContext) => {
    const { platform, platformShops } = new DataAccessUnitOfWork(context.mongoClient!);

    const testPlatformName = 'platform-e2e-test';
    const id = await findOrCreatePlatform_getId(platform, testPlatformName);

    console.log('Platform Id for', testPlatformName, 'is', id);

    const chargeflowId = new ObjectId().toHexString();

    const connectResponse = await platformShops.connectShopToPlatform({
        chargeflowId,
        platformId: id,
        merchantId: 'test-merchant-id',
        merchantName: 'test-merchant-name',
    });
    console.log(connectResponse);
    assert.ok(connectResponse);

    const relatedPlatformResponse = await platformShops.getPlatformShopsByChargeflowId(
        chargeflowId,
    );
    console.log(relatedPlatformResponse);
    assert.ok(relatedPlatformResponse);

    const disconnectResponse = await platformShops.disconnectShopToPlatform(id, chargeflowId);
    console.log(disconnectResponse);
    assert.ok(disconnectResponse);

    const connectedShopsResponse = await platformShops.getConnectedShops(id);
    console.log(connectedShopsResponse);
    assert.ok(connectedShopsResponse);

    console.log('Sanity was done successfully for methods: connectShopToPlatform, disconnectShopToPlatform');
})
    .then(console.log);
