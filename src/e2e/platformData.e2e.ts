import * as assert from 'node:assert';
import { findOrCreatePlatform_getId, hashID, IContext, main } from './main.e2e';
import { DataAccessUnitOfWork } from '../DataAccessUnitOfWork';
import { ObjectId } from 'mongodb';

main(async (context: IContext) => {
    const { platform, platformData } = new DataAccessUnitOfWork(context.mongoClient!);

    const testPlatformName = 'platform-e2e-test';
    const id = await findOrCreatePlatform_getId(platform, testPlatformName);

    console.log('Platform Id for', testPlatformName, 'is', id);

    const resRand = await platformData.upsert(
        new ObjectId().toHexString(),
        { stripe_key: 'sk_test_new_record_' + hashID(6) },
    );
    assert.ok(resRand);

    const resExist = await platformData.upsert(id, { stripe_key: 'sk_test_123' });
    assert.ok(resExist);

    const resGetById = await platformData.getByPlatformId(id);
    console.log(resGetById);
    assert.ok(resGetById);

    console.log('Sanity was done successfully for methods: upsert, getByPlatformId');
})
    .then(console.log);
