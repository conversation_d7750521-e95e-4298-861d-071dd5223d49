import * as assert from 'node:assert';
import { findOrCreatePlatform_getId, IContext, main } from './main.e2e';
import { DataAccessUnitOfWork } from '../DataAccessUnitOfWork';

main(async (context: IContext) => {
    const { platform } = new DataAccessUnitOfWork(context.mongoClient!);

    const testPlatformName = 'platform-e2e-test';
    const id = await findOrCreatePlatform_getId(platform, testPlatformName);

    const platformById = await platform.getById(id);
    assert.ok(platformById);
    assert.strictEqual(platformById._id, id);

    const platformByName = await platform.findOneByName(testPlatformName);
    assert.ok(platformByName);
    assert.strictEqual(platformByName._id, id);

    const updateResult = await platform.update(id, { description: 'updated description' });
    assert.ok(updateResult);

    console.log('Sanity was done successfully for methods: insert, getById, findOneByName, update');
})
    .then(console.log);
