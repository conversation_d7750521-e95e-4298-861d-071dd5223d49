import { findOrCreatePlatform_getId, IContext, main } from './main.e2e';
import { DataAccessUnitOfWork } from '../DataAccessUnitOfWork';
import * as assert from 'node:assert';

main(async (context: IContext) => {
    const { platform, platformUsers } = new DataAccessUnitOfWork(context.mongoClient!);

    const testPlatformName = 'platform-e2e-test';
    const id = await findOrCreatePlatform_getId(platform, testPlatformName);

    console.log('Platform Id for', testPlatformName, 'is', id);

    const email = '<EMAIL>';

    const userResult = await platformUsers.getUserByEmail(email);

    if (!userResult) {
        const user = await platformUsers.create({ email, platformId: id });
        assert.ok(user, 'User was not created');
    } else {
        assert.ok(userResult, 'User already exist');
    }

    console.log('Sanity was done successfully for methods: getUserByEmail, create');
})
    .then(console.log);
