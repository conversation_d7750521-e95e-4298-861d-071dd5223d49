import { ObjectId, WithId } from 'mongodb';
import { IMongoUserCookies } from './Types';
import { MongoUserCookiesRepository } from './MongoUserCookiesRepository';
import mocks from './__mocks__/UserCookies';
import updateOperationMocks from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { MongoUserCookiesMapper } from './MongoUserCookiesMapper';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

describe(MongoUserCookiesRepository.name, () => {
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoUserCookies>>;
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoUserCookies>>;
    let repository: MongoUserCookiesRepository;

    beforeEach(() => {
        mockUpdateOperation = updateOperationMocks.getUpdateOperation<IMongoUserCookies>();
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoUserCookies>();

        repository = new MongoUserCookiesRepository(
            mockUpdateOperation,
            mockQueryOperation,
        );
    });

    describe('upsert', () => {
        it('should upsert user cookies by chargeflowId and return upserted doc', async () => {
            const id = new ObjectId();
            const userCookies = mocks.getUserCookies();
            const { chargeflowId, ...doc } = userCookies;
            const mongoDoc = MongoUserCookiesMapper.toMongoUserCookies(userCookies);

            const upsertResult: WithId<IMongoUserCookies> = {
                ...mongoDoc,
                _id: id,
            };

            mockUpdateOperation.upsert.mockResolvedValue(upsertResult);
            const result = await repository.upsert(chargeflowId!, doc);

            expect(mockUpdateOperation.upsert).toHaveBeenCalledWith(
                { chargeflowId: mongoDoc.chargeflowId },
                { $set: doc },
                undefined,
            );
            expect(result).toEqual({
                ...userCookies,
                _id: id.toHexString(),
            });
        });
    });

    describe('findByChargeflowId', () => {
        it('should return doc by chargeflowId', async () => {
            const doc = mocks.getUserCookies();
            const id = new ObjectId();
            const chargeflowId = doc.chargeflowId!;

            const mongoDoc = MongoUserCookiesMapper.toMongoUserCookies(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByChargeflowId(chargeflowId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { chargeflowId: new ObjectId(chargeflowId) },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('upsertViaGaId', () => {
        it('should upsert user cookies by _ga and return upserted doc', async () => {
            const _ga = 'GA1.2.1234567890.1234567890';
            const userCookies = mocks.getUserCookies();
            const { ...doc } = userCookies;
            const mongoDoc = MongoUserCookiesMapper.toMongoUserCookies(userCookies);
            const id = new ObjectId();

            const upsertResult: WithId<IMongoUserCookies> = {
                ...mongoDoc,
                _id: id,
            };

            mockUpdateOperation.upsert.mockResolvedValue(upsertResult);
            const result = await repository.upsertViaGaId(_ga, doc);

            expect(mockUpdateOperation.upsert).toHaveBeenCalledWith(
                { 'cookies._ga': _ga },
                { $set: doc },
                undefined,
            );
            expect(result).toEqual({
                ...userCookies,
                _id: id.toHexString(),
            });
        });
    });

    describe('findByGaId', () => {
        it('should return doc by _ga', async () => {
            const doc = mocks.getUserCookies();
            const _ga = doc.cookies._ga!;
            const id = new ObjectId();

            const mongoDoc = MongoUserCookiesMapper.toMongoUserCookies(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByGaId(_ga);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { 'cookies._ga': _ga },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });
});
