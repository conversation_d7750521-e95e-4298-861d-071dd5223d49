import { ClientSession, ObjectId } from 'mongodb';
import { IUserCookiesRepository } from './IUserCookiesRepository';
import { IUserCookies, IMongoUserCookies, IUserCookiesBase } from './Types';
import { MongoUserCookiesMapper } from './MongoUserCookiesMapper';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoUserCookiesRepository implements IUserCookiesRepository {
    constructor(
        private readonly updateOperation: IUpdateOperation<IMongoUserCookies>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoUserCookies>,
    ) {}

    async upsert(
        chargeflowId: string, userCookies: IUserCookiesBase, clientSession?: ClientSession,
    ): Promise<WithId<IUserCookies>> {
        return MongoUserCookiesMapper.fromMongoUserCookies(
            await this.updateOperation.upsert(
                { chargeflowId: new ObjectId(chargeflowId) },
                { $set: userCookies },
                clientSession,
            ),
        );
    }

    async upsertViaGaId(
        _ga: string, userCookies: IUserCookiesBase, clientSession?: ClientSession,
    ): Promise<WithId<IUserCookies>> {
        return MongoUserCookiesMapper.fromMongoUserCookies(
            await this.updateOperation.upsert(
                { 'cookies._ga': _ga },
                { $set: userCookies },
                clientSession,
            ),
        );
    }

    async findByChargeflowId(chargeflowId: string, clientSession?: ClientSession | undefined): Promise<WithId<IUserCookies>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { chargeflowId: new ObjectId(chargeflowId) },
            clientSession,
        );
        return doc ? MongoUserCookiesMapper.fromMongoUserCookies(doc) : null;
    }

    async findByGaId(_ga: string, clientSession?: ClientSession | undefined): Promise<WithId<IUserCookies>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { 'cookies._ga': _ga },
            clientSession,
        );
        return doc ? MongoUserCookiesMapper.fromMongoUserCookies(doc) : null;
    }
}
