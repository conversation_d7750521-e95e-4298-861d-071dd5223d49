import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { UserCookiesRepositoryFactory } from './UserCookiesRepositoryFactory';
import { MongoUserCookiesRepository } from './MongoUserCookiesRepository';

describe(UserCookiesRepositoryFactory.name, () => {
    describe(UserCookiesRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = UserCookiesRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoUserCookiesRepository);
        });
    });
});
