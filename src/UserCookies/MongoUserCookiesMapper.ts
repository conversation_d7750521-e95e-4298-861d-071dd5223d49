import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { IUserCookies, IMongoUserCookies } from './Types';
import { WithId } from '../shared/helper-types';

export class MongoUserCookiesMapper {
    static toMongoUserCookies(doc: IUserCookies): IMongoUserCookies {
        if (!doc) {
            throw new Error('User cookies is required for mapping');
        }

        return {
            ...doc,
            _id: doc._id ? new ObjectId(doc._id) : undefined,
            chargeflowId: doc.chargeflowId ? new ObjectId(doc.chargeflowId) : undefined,
        };
    }

    static fromMongoUserCookies(doc: MongoWithId<IMongoUserCookies>): WithId<IUserCookies> {
        if (!doc) {
            throw new Error('User cookies is required for mapping');
        }

        return {
            ...doc,
            _id: doc._id?.toHexString(),
            chargeflowId: doc.chargeflowId?.toHexString(),
        };
    }
}
