import { ClientSession } from 'mongodb';
import { IUserCookies, IUserCookiesBase } from './Types';
import { WithId } from '../shared/helper-types';

export interface IUserCookiesRepository {
    upsert(
        chargeflowId: string, userCookies: IUserCookiesBase, clientSession?: ClientSession
    ): Promise<WithId<IUserCookies>>,

    findByChargeflowId(
        chargeflowId: string, clientSession?: ClientSession
    ): Promise<WithId<IUserCookies>|null>,

    upsertViaGaId(
        _ga: string, userCookies: IUserCookiesBase, clientSession?: ClientSession,
    ): Promise<WithId<IUserCookies>>

    findByGaId(_ga: string, clientSession?: ClientSession | undefined): Promise<WithId<IUserCookies>|null>
}
