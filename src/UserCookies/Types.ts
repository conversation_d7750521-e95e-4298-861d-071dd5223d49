import { ObjectId, Document } from 'mongodb';

export interface IUserCookiesBase {
    email: string,
    phone: string,
    currency: string,
    dateCollected: Date,
    ip?: string,
    cookies: Record<string, string>,
    queryParameters: Record<string, string>,
}

export interface IUserCookies extends IUserCookiesBase {
    _id?: string,
    chargeflowId?: string,
}

export interface IMongoUserCookies extends IUserCookiesBase, Document {
    _id?: ObjectId,
    chargeflowId?: ObjectId,
}
