import { ObjectId } from 'mongodb';
import { IUserCookies } from '../Types';
import { faker } from '@faker-js/faker';

const getUserCookies = (overrides?: Partial<IUserCookies>): IUserCookies => ({
    _id: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    dateCollected: new Date(),
    currency: 'USD',
    ip: faker.internet.ip(),
    cookies: {
        test: faker.string.alpha(),
    },
    queryParameters: {
        test: faker.string.alpha(),
    },
    ...overrides,
});

const defaultUserCookies = getUserCookies();

export default {
    getUserCookies,
    defaultUserCookies,
};
