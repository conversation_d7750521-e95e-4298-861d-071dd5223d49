import { MongoClient } from 'mongodb';
import { IUserCookiesRepository } from './IUserCookiesRepository';
import { IMongoUserCookies } from './Types';
import { MongoUserCookiesRepository } from './MongoUserCookiesRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class UserCookiesRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IUserCookiesRepository {
        const updateOperation = new MongoUpdateOperation<IMongoUserCookies>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoUserCookies>(mongoClient, dbName, collectionName);

        return new MongoUserCookiesRepository(updateOperation, findByQueryOperation);
    }
}
