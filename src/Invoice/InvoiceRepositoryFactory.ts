import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoInvoiceCacheDocument } from './IInvoice';
import { IInvoiceRepository } from './IInvoiceRepository';
import { MongoInvoiceRepository } from './MongoInvoiceRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class InvoiceRepositoryFactory {
    static create(
        mongoClient: MongoClient,
        dbName: string,
        collectionName: string,
    ): IInvoiceRepository {
        const findByQueryOperation =
      new MongoFindByQueryOperation<MongoInvoiceCacheDocument>(
          mongoClient,
          dbName,
          collectionName,
      );
        const updateOperation =
      new MongoUpdateOperation<MongoInvoiceCacheDocument>(
          mongoClient,
          dbName,
          collectionName,
      );

        return new MongoInvoiceRepository(
            findByQueryOperation,
            updateOperation,
        );
    }
}
