import { ObjectId } from 'mongodb';
import {
    IMongoCachedEnrichmentDocument,
    ICachedEnrichmentDocument,
} from '../shared/helper-types';

export enum InvoiceSource {
  INTEGRATION = 'integration',
  API = 'api',
  IMPORT = 'import',
  MANUAL = 'manual',
}

export interface IInvoiceProduct {
  extProductId?: string | null;

  // Product Details
  title?: string | null;
  description?: string | null;
  productType?: string | null;

  // URLs
  imageSrc?: string | null;
  productUrl?: string | null;

  rawData?: Record<string, any> | null;
}

export interface IInvoiceCustomer {
  extCustomerId?: string | null;
  name?: string | null;

  // Contact Information
  phone?: string | null;
  email?: string | null;

  // Metadata
  createdDate?: Date | null;
  isEmailVerified?: boolean | null;

  rawData?: Record<string, any> | null;
}

export interface IInvoiceAddress {
  // Contact Details
  name?: string | null;
  phone?: string | null;

  // Address Details
  line1?: string | null;
  line2?: string | null;
  city?: string | null;
  state?: string | null;
  country?: string | null;
  zipCode?: string | null;

  // Geolocation
  longitude?: string | null;
  latitude?: string | null;

  rawData?: Record<string, any> | null;
}

export interface IInvoiceLineItem {
  extInvoiceLineItemId?: string | null;
  extInvoiceId?: string | null;

  // Line Item Details
  product?: IInvoiceProduct | null;
  amount?: number | null;
  currency?: string | null;
  description?: string | null;
  discountAmount?: number | null;
  taxAmount?: number | null;
  unitPrice?: number | null;
  quantity?: number | null;

  rawData?: Record<string, any> | null;
}

export interface IInvoiceAccount {
  extAccountId?: string | null;
  name?: string | null;
  supportAddress?: IInvoiceAddress | null;
  supportEmail?: string | null;
  supportPhone?: string | null;
  supportUrl?: string | null;
  url?: string | null;
}

export interface IInvoice {
  // Primary and Foreign Keys
  accountId?: string | null;
  chargeflowId?: string | null;
  businessId?: string | null;
  processorId?: string | null;
  extInvoiceId?: string | null;
  extCustomerId?: string | null;

  // Account
  accountName?: string | null;
  issuerAccount?: IInvoiceAccount | null;
  issuerType?: string | null;

  // Customer
  customer?: IInvoiceCustomer | null;

  // Addresses
  billingAddress?: IInvoiceAddress | null;
  shippingAddress?: IInvoiceAddress | null;

  // Timestamps
  createdDate?: Date | null;
  dueDate?: Date | null;
  finalizedDate?: Date | null;
  paidDate?: Date | null;
  periodStartDate?: Date | null;
  periodEndDate?: Date | null;

  // Invoice Details
  currency?: string | null;
  invoiceDescription?: string | null;
  invoiceUrl?: string | null;
  invoiceNumber?: string | null;
  receiptNumber?: string | null;
  statementDescriptor?: string | null;
  status?: string | null;
  source?: InvoiceSource | null;
  isManualInvoice?: boolean | null;

  // Financial Details
  amountDue?: number | null;
  amountPaid?: number | null;
  amountRemaining?: number | null;
  totalInvoiceAmount?: number | null;
  invoiceSubtotal?: number | null;
  totalDiscountAmount?: number | null;
  totalTaxAmount?: number | null;
  paymentAttempts?: number | null;

  // Line Items
  lineItems?: IInvoiceLineItem[] | null;

  // Raw Data
  rawData?: Record<string, any> | null;
}

export interface IMongoInvoiceMetadata {
  processor: string;
  source: string;
  disputeId: ObjectId;
}

export interface IInvoiceMetadata {
  processor: string;
  source: string;
  disputeId: string;
}

export type MongoInvoiceCacheDocument = IMongoCachedEnrichmentDocument<
  IInvoice | null,
  IMongoInvoiceMetadata
>;
export type InvoiceCacheDocument = ICachedEnrichmentDocument<
  IInvoice | null,
  IInvoiceMetadata
>;
