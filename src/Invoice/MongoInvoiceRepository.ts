import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IInvoice, MongoInvoiceCacheDocument, InvoiceCacheDocument } from './IInvoice';
import { IInvoiceRepository } from './IInvoiceRepository';
import { MongoInvoiceMapper } from './MongoInvoiceMapper';

export class MongoInvoiceRepository implements IInvoiceRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<MongoInvoiceCacheDocument>,
        private readonly updateOperation: IUpdateOperation<MongoInvoiceCacheDocument>,
    ) {}

    async upsertInvoice(
        disputeId: string,
        chargeflowId: string,
        source: string,
        processor: string,
        invoice: IInvoice | null,
        session?: ClientSession,
    ): Promise<WithId<InvoiceCacheDocument>> {
        const cacheKey = `disputeId:${disputeId}`;

        const result = await this.updateOperation.upsert(
            { cacheKey, chargeflowId: new ObjectId(chargeflowId) },
            {
                $set: {
                    data: invoice,
                    cacheKey,
                    metadata: {
                        processor,
                        source,
                        disputeId: new ObjectId(disputeId),
                    },
                },
                $currentDate: {
                    dateUpdated: true,
                },
                $setOnInsert: {
                    chargeflowId: new ObjectId(chargeflowId),
                    dateCreated: new Date(),
                },
            },
            session,
        );

        return MongoInvoiceMapper.fromMongoInvoiceCacheDocument(result);
    }

    async bumpInvoiceLastUpdatedDate(
        disputeId: string,
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<WithId<InvoiceCacheDocument>> {
        const result = await this.updateOperation.updateWithFilter(
            {
                chargeflowId: new ObjectId(chargeflowId),
                'metadata.disputeId': new ObjectId(disputeId),
            },
            { $currentDate: { dateUpdated: true } },
            session,
        );

        return MongoInvoiceMapper.fromMongoInvoiceCacheDocument(result);
    }

    async getInvoiceByDisputeIdWithCacheInfo(
        disputeId: string,
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<InvoiceCacheDocument | null> {
        const result = await this.findByQueryOperation.findOne(
            {
                chargeflowId: new ObjectId(chargeflowId),
                'metadata.disputeId': new ObjectId(disputeId),
            },
            session,
        );

        return result ? MongoInvoiceMapper.fromMongoInvoiceCacheDocument(result) : null;
    }

    async getInvoiceByDisputeId(
        disputeId: string,
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<IInvoice | null> {
        const result = await this.getInvoiceByDisputeIdWithCacheInfo(disputeId, chargeflowId, session);
        return result?.data ?? null;
    }
}
