import { mock } from 'jest-mock-extended';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import InvoiceMocks from './__mocks__/Invoice';
import { MongoInvoiceCacheDocument } from './IInvoice';
import { MongoInvoiceMapper } from './MongoInvoiceMapper';
import { MongoInvoiceRepository } from './MongoInvoiceRepository';
const MOCK_SYSTEM_TIME = '2024-04-12T00:00:00Z';

describe(MongoInvoiceRepository.name, () => {
    let mongoInvoiceRepository: MongoInvoiceRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<MongoInvoiceCacheDocument>>();
    const updateOperation = mock<IUpdateOperation<MongoInvoiceCacheDocument>>();

    beforeEach(() => {
        mongoInvoiceRepository = new MongoInvoiceRepository(findByQueryOperation, updateOperation);

        jest.useFakeTimers().setSystemTime(new Date(MOCK_SYSTEM_TIME));
    });

    afterEach(jest.clearAllMocks);

    describe(MongoInvoiceRepository.prototype.upsertInvoice.name, () => {
        it('should upsert invoice', async () => {
            expect.assertions(2);

            // GIVEN
            const invoice = InvoiceMocks.getInvoiceCacheDocument();
            const disputeId = new ObjectId().toHexString();
            const session = mock<ClientSession>();
            updateOperation.upsert.mockResolvedValue(MongoInvoiceMapper.toMongoInvoiceCacheDocument(invoice) as WithId<MongoInvoiceCacheDocument>);

            // WHEN
            await mongoInvoiceRepository.upsertInvoice(disputeId, invoice.chargeflowId, invoice.metadata.source, invoice.metadata.processor, invoice.data, session);

            // THEN
            expect(updateOperation.upsert).toHaveBeenCalledTimes(1);
            expect(updateOperation.upsert).toHaveBeenCalledWith(
                {
                    cacheKey: `disputeId:${disputeId}`,
                    chargeflowId: new ObjectId(invoice.chargeflowId),
                },
                expect.objectContaining({
                    $setOnInsert: {
                        dateCreated: expect.any(Date),
                        chargeflowId: new ObjectId(invoice.chargeflowId),
                    },
                    $currentDate: {
                        dateUpdated: true,
                    },
                    $set: {
                        data: invoice.data,
                        cacheKey: `disputeId:${disputeId}`,
                        metadata: {
                            processor: invoice.metadata.processor,
                            source: invoice.metadata.source,
                            disputeId: new ObjectId(disputeId),
                        },
                    },
                }),
                session,
            );
        });
    });

    describe(MongoInvoiceRepository.prototype.bumpInvoiceLastUpdatedDate.name, () => {
        it('should bump invoice last updated date', async () => {
            expect.assertions(2);

            // GIVEN
            const disputeId = new ObjectId();
            const chargeflowId = new ObjectId();
            const session = mock<ClientSession>();
            const invoice = InvoiceMocks.getInvoiceCacheDocument();
            updateOperation.updateWithFilter.mockResolvedValue(MongoInvoiceMapper.toMongoInvoiceCacheDocument(invoice) as WithId<MongoInvoiceCacheDocument>);

            // WHEN
            await mongoInvoiceRepository.bumpInvoiceLastUpdatedDate(disputeId.toHexString(), chargeflowId.toHexString(), session);

            // THEN
            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                {
                    chargeflowId,
                    'metadata.disputeId': disputeId,
                },
                { $currentDate: { dateUpdated: true } },
                session,
            );
        });
    });

    describe(MongoInvoiceRepository.prototype.getInvoiceByDisputeIdWithCacheInfo.name, () => {
        it('should return invoice with cache info object', async () => {
            expect.assertions(3);

            // GIVEN
            const disputeId = new ObjectId();
            const invoice = InvoiceMocks.getInvoiceCacheDocument();
            const session = mock<ClientSession>();

            const expectedResult = invoice;

            const findByQueryResult = MongoInvoiceMapper.toMongoInvoiceCacheDocument(invoice) as WithId<MongoInvoiceCacheDocument>;
            findByQueryOperation.findOne.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoInvoiceRepository.getInvoiceByDisputeIdWithCacheInfo(disputeId.toHexString(), invoice.chargeflowId, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.findOne).toHaveBeenCalledWith(
                {
                    chargeflowId: new ObjectId(invoice.chargeflowId),
                    'metadata.disputeId': disputeId,
                },
                session,
            );
        });
    });

    describe(MongoInvoiceRepository.prototype.getInvoiceByDisputeId.name, () => {
        it('should return invoice', async () => {
            expect.assertions(1);

            // GIVEN
            const disputeId = new ObjectId();
            const invoice = InvoiceMocks.getInvoiceCacheDocument();
            const session = mock<ClientSession>();

            const expectedResult = invoice.data;

            const findByQueryResult = MongoInvoiceMapper.toMongoInvoiceCacheDocument(invoice) as WithId<MongoInvoiceCacheDocument>;
            findByQueryOperation.findOne.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoInvoiceRepository.getInvoiceByDisputeId(disputeId.toHexString(), invoice.chargeflowId, session);

            // THEN
            expect(result).toEqual(expectedResult);
        });
    });
});
