import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { InvoiceRepositoryFactory } from './InvoiceRepositoryFactory';
import { MongoInvoiceRepository } from './MongoInvoiceRepository';

describe(InvoiceRepositoryFactory.name, () => {
    describe(InvoiceRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = InvoiceRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoInvoiceRepository);
        });
    });
});
