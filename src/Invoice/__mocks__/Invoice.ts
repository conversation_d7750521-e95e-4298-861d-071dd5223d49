import { ObjectId, WithId } from 'mongodb';
import { IInvoice, InvoiceCacheDocument } from '../IInvoice';

const getInvoice = (overrides: Partial<IInvoice> = {}): IInvoice => ({
    ...overrides,
    accountId: 'Test account ID',
    chargeflowId: 'Test chargeflow ID',
    processorId: 'Test processor ID',
    extInvoiceId: 'Test external invoice ID',
    customer: {
        extCustomerId: 'Test customer ID',
        name: 'Test customer name',
        phone: 'Test customer phone number',
        email: 'Test customer email address',
    },
    amountDue: 100,
    amountPaid: 50,
    amountRemaining: 50,
    totalInvoiceAmount: 100,
    invoiceSubtotal: 100,
    totalDiscountAmount: 0,
    totalTaxAmount: 0,
    billingAddress: {
        name: 'Test billing address name',
        phone: 'Test billing address phone number',
    },
    shippingAddress: {
        name: 'Test shipping address name',
        phone: 'Test shipping address phone number',
    },
});

const getInvoiceCacheDocument = (overrides: Partial<InvoiceCacheDocument> = {}): WithId<InvoiceCacheDocument> => ({
    ...getInvoice(overrides),
    _id: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    dateCreated: new Date(),
    cacheKey: 'Test cache key',
    data: getInvoice(overrides),
    metadata: {
        disputeId: new ObjectId().toHexString(),
        processor: 'Test processor',
        source: 'Test source',
    },
});

export default {
    getInvoice,
    getInvoiceCacheDocument,
};
