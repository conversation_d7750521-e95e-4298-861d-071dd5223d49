import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import {
    MongoInvoiceCacheDocument,
    InvoiceCacheDocument,
} from './IInvoice';

export class MongoInvoiceMapper {
    static toMongoInvoiceCacheDocument(
        invoiceCacheDocument: InvoiceCacheDocument,
    ): MongoInvoiceCacheDocument {
        if (!invoiceCacheDocument) {
            throw new Error('Invoice cache document is required for mapping');
        }

        return {
            ...invoiceCacheDocument,
            _id: invoiceCacheDocument._id
                ? new ObjectId(invoiceCacheDocument._id)
                : undefined,
            chargeflowId: new ObjectId(invoiceCacheDocument.chargeflowId),
            metadata: {
                ...invoiceCacheDocument.metadata,
                disputeId: new ObjectId(invoiceCacheDocument.metadata.disputeId),
            },
        };
    }

    static fromMongoInvoiceCacheDocument(
        mongoInvoiceCacheDocument: MongoWithId<MongoInvoiceCacheDocument>,
    ): WithId<InvoiceCacheDocument> {
        if (!mongoInvoiceCacheDocument) {
            throw new Error('Invoice cache document not found');
        }

        return {
            ...mongoInvoiceCacheDocument,
            _id: mongoInvoiceCacheDocument._id.toHexString(),
            chargeflowId: mongoInvoiceCacheDocument.chargeflowId.toHexString(),
            metadata: {
                ...mongoInvoiceCacheDocument.metadata,
                disputeId:
          mongoInvoiceCacheDocument.metadata.disputeId.toHexString(),
            },
        };
    }
}
