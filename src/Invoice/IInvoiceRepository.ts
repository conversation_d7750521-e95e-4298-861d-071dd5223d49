import { ClientSession, WithId } from 'mongodb';
import { IInvoice, InvoiceCacheDocument } from './IInvoice';

export interface IInvoiceRepository {
  getInvoiceByDisputeId: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<IInvoice | null>;

  getInvoiceByDisputeIdWithCacheInfo: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<InvoiceCacheDocument | null>;

  upsertInvoice: (
    disputeId: string,
    chargeflowId: string,
    source: string,
    processor: string,
    invoice: IInvoice | null,
    session?: ClientSession
  ) => Promise<WithId<InvoiceCacheDocument>>;

  bumpInvoiceLastUpdatedDate: (
    disputeId: string,
    chargeflowId: string,
    session?: ClientSession
  ) => Promise<WithId<InvoiceCacheDocument>>;
}
