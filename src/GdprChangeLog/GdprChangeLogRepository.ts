import { ClientSession, ObjectId } from 'mongodb';
import { IGdprChangeLogRepository } from './IGdprChangeLogRepository';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IGdprChangeLog, IMongoUnifiedIGdprChangeLogObject } from './Types';
import { WithId } from '../shared/helper-types';
import { MongoGdprChangeLogMapper } from './MongoGdprChangeLogMapper';
import { Logger } from '@chargeflow-team/chargeflow-utils-sdk';

export class MongoGdprChangeLogRepository implements IGdprChangeLogRepository {
    constructor(
        private readonly addOperation: IAddOperation<IMongoUnifiedIGdprChangeLogObject>,
        private readonly bulkAddOperation: IBulkAddOperation<IMongoUnifiedIGdprChangeLogObject>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoUnifiedIGdprChangeLogObject>,
        private readonly getByIdOperation: IGetByIdOperation<IMongoUnifiedIGdprChangeLogObject>,
    ) {}

    async add(
        logEntry: IGdprChangeLog,
        session?: ClientSession,
    ): Promise<WithId<IMongoUnifiedIGdprChangeLogObject>> {
        try {
            const logWithId: IMongoUnifiedIGdprChangeLogObject = {
                ...logEntry,
            };
            const result = await this.addOperation.add(logWithId, session);
            return MongoGdprChangeLogMapper.fromMongoGdprChangeLog(result);
        } catch (error) {
            Logger.error('InternalServerError', 'Error adding GDPR change log entry:', error);
            throw error;
        }
    }

    async bulkAdd(
        logEntries: IGdprChangeLog[],
        session?: ClientSession,
    ): Promise<WithId<IMongoUnifiedIGdprChangeLogObject>[]> {
        try {
            const logsWithId: IMongoUnifiedIGdprChangeLogObject[] = logEntries.map(log => ({
                ...log,
                _id: new ObjectId(),
            }));

            const results = await this.bulkAddOperation.bulkAdd(logsWithId, session);
            return results.map(MongoGdprChangeLogMapper.fromMongoGdprChangeLog);
        } catch (error) {
            Logger.error('InternalServerError', 'Error bulk adding GDPR change log entries:', error);
            throw error;
        }
    }

    async get(
        id: string,
        session?: ClientSession,
    ): Promise<WithId<IMongoUnifiedIGdprChangeLogObject> | null> {
        try {
            const result = await this.getByIdOperation.get(new ObjectId(id), session);
            return result ? MongoGdprChangeLogMapper.fromMongoGdprChangeLog(result) : null;
        } catch (error) {
            Logger.error('InternalServerError', `Error retrieving GDPR change log entry with ID ${id}:`, error);
            throw error;
        }
    }
}
