
import { WithId as MongoWithId } from 'mongodb';
import {
    IGdprChangeLog,
    IMongoUnifiedIGdprChangeLogObject,
} from './Types';
import { WithId } from '../shared/helper-types';

export class MongoGdprChangeLogMapper {
    static fromMongoGdprChangeLog(gdprChangeLog: MongoWithId<IMongoUnifiedIGdprChangeLogObject>): WithId<IGdprChangeLog> {
        if (!gdprChangeLog) {
            throw new Error('GdprChangeLog is required for mapping');
        }

        return {
            ...gdprChangeLog,
            _id: gdprChangeLog._id?.toString(),
        };
    }
}
