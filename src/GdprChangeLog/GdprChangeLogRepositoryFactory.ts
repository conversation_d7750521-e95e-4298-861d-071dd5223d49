import { MongoGdprChangeLogRepository } from './GdprChangeLogRepository';
import { MongoClient } from 'mongodb';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoBulkAddOperation } from '../shared/operation/BulkAddOperation/MongoBulkAddOperation';
import { IMongoUnifiedIGdprChangeLogObject } from './Types';
import { IGdprChangeLogRepository } from './IGdprChangeLogRepository';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';

export class GdprRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IGdprChangeLogRepository {
        const addOperation = new MongoAddOperation<IMongoUnifiedIGdprChangeLogObject>(mongoClient, dbName, collectionName);
        const bulkAddOperation = new MongoBulkAddOperation<IMongoUnifiedIGdprChangeLogObject>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoUnifiedIGdprChangeLogObject>(mongoClient, dbName, collectionName);
        const getByIdOperation = new MongoGetByIdOperation<IMongoUnifiedIGdprChangeLogObject>(mongoClient, dbName, collectionName);

        return new MongoGdprChangeLogRepository(
            addOperation,
            bulkAddOperation,
            findByQueryOperation,
            getByIdOperation,
        );
    }
}
