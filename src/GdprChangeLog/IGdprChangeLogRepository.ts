import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoUnifiedIGdprChangeLogObject, IGdprChangeLog } from './Types';

export interface IGdprChangeLogRepository {
    add: (
        logEntry: IGdprChangeLog, session?: ClientSession
    ) => Promise<WithId<IMongoUnifiedIGdprChangeLogObject>>;

    bulkAdd: (
        logEntries: IGdprChangeLog[], session?: ClientSession
    ) => Promise<WithId<IMongoUnifiedIGdprChangeLogObject>[]>;

    get: (
        id: string, session?: ClientSession
    ) => Promise<WithId<IMongoUnifiedIGdprChangeLogObject> | null>;
}
