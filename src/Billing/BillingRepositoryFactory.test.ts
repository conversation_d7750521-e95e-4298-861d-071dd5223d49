import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { BillingRepositoryFactory } from './BillingRepositoryFactory';
import { MongoBillingRepository } from './MongoBillingRepository';

describe(BillingRepositoryFactory.name, () => {
    describe(BillingRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = BillingRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoBillingRepository);
        });
    });
});
