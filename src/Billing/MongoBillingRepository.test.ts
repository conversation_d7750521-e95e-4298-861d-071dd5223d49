import { ObjectId } from 'mongodb';
import { IMongoBilling } from './Types';
import { MongoBillingRepository } from './MongoBillingRepository';
import mocks from './__mocks__/Billing';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { MongoBillingMapper } from './MongoBillingMapper';

describe(MongoBillingRepository.name, () => {
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoBilling>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoBilling>>;
    let repository: MongoBillingRepository;

    beforeEach(() => {
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoBilling>();
        mockUpdateOperation = {
            updateWithFilter: jest.fn(),
        } as unknown as jest.Mocked<IUpdateOperation<IMongoBilling>>;
        repository = new MongoBillingRepository(mockQueryOperation, mockUpdateOperation);
    });

    describe('findByChargeflowId', () => {
        it('should return doc by chargeflowId', async () => {
            const doc = mocks.getBilling();
            const id = new ObjectId();
            const chargeflowId = doc.chargeflowId.toHexString();

            const mongoDoc = { ...doc, _id: id, chargeflowId: new ObjectId(chargeflowId) };
            mockQueryOperation.findOne.mockResolvedValue(mongoDoc);

            const result = await repository.findByChargeflowId(chargeflowId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { 'chargeflowId': new ObjectId(chargeflowId) },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
                chargeflowId: doc.chargeflowId.toHexString(),
                accountId: doc.accountId.toHexString(),
                shopId: doc.shopId.toHexString(),
            });
        });
    });

    describe('updateOneBillingByChargeflowId', () => {
        it('should return update response by chargeflowId', async () => {
            const doc = mocks.getBilling();
            const id = new ObjectId();
            const chargeflowId = doc.chargeflowId.toHexString();

            const mongoDoc = MongoBillingMapper.fromMongoBilling(doc);
            mockUpdateOperation.updateWithFilter.mockResolvedValue({
                ...mongoDoc, _id: id, chargeflowId: new ObjectId(chargeflowId), shopId: new ObjectId(doc.shopId),
                accountId: new ObjectId(doc.accountId) });

            const result = await repository.updateOneBillingByChargeflowId(chargeflowId.toString(), new Date(), new Date(), 1, 1, 1);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { 'chargeflowId': new ObjectId(chargeflowId) },
                {
                    $set: {
                        dateUpdated: expect.any(Date),
                        billingDate: expect.any(Date),
                        activeCappedAmount: 1,
                        activeBalanceUsed: 1,
                        activeBalanceRemaining: 1,
                    },
                },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
                chargeflowId: doc.chargeflowId.toHexString(),
                accountId: doc.accountId.toHexString(),
                shopId: doc.shopId.toHexString(),
            });
        });
    });
});
