import { ObjectId } from 'mongodb';
import { IMongoBilling } from '../Types';
import { faker } from '@faker-js/faker';

const getBilling = (overrides?: Partial<IMongoBilling>): IMongoBilling => ({
    _id: new ObjectId(),
    dateCreated: new Date(),
    dateUpdated: new Date(),
    billingDate: new Date(),
    chargeflowId: new ObjectId(),
    shopId: new ObjectId(),
    shopName: faker.company.name(),
    activeRecurringAppChargeId: faker.number.float(),
    pendingRecurringAppChargeId: null,
    activeStatus: faker.datatype.boolean(),
    pendingStatus: faker.datatype.boolean(),
    canceledStatus: faker.datatype.boolean(),
    activeCappedAmount: faker.number.int(),
    activeBalanceUsed: faker.number.float(),
    activeBalanceRemaining: faker.number.float(),
    pendingCappedAmount: null,
    accountId: new ObjectId(),
    ...overrides,
});

const defaultBilling = getBilling();

export default {
    getBilling,
    defaultBilling,
};
