import { ClientSession } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IBilling } from './Types';

export interface IMongoBillingRepository {
    findByChargeflowId(
        chargeflowId: string, clientSession?: ClientSession
    ): Promise<WithId<IBilling>|null>,
    updateOneBillingByChargeflowId(
        chargeflowId: string, updatedAt: Date, billingOn: Date, cappedAmount: number, balanceUsed: number, balanceRemaining: number, clientSession?: ClientSession
    ): Promise<WithId<IBilling>|null>,
}
