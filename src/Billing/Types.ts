import { ObjectId } from 'mongodb';

export interface IMongoBilling {
    _id: ObjectId,
    dateCreated: Date,
    dateUpdated: Date,
    billingDate: Date,
    chargeflowId: ObjectId,
    shopId: ObjectId,
    shopName: string,
    activeRecurringAppChargeId: number | null,
    pendingRecurringAppChargeId: number | null,
    activeStatus: boolean,
    pendingStatus: boolean,
    canceledStatus: boolean,
    activeCappedAmount: number | null,
    activeBalanceUsed: number | null,
    activeBalanceRemaining: number | null,
    pendingCappedAmount: number | null
    accountId: ObjectId
}

export interface IBilling {
    _id: string,
    dateCreated: Date,
    dateUpdated: Date,
    billingDate: Date,
    chargeflowId: string,
    shopId: string,
    shopName: string,
    activeRecurringAppChargeId: number | null,
    pendingRecurringAppChargeId: number | null,
    activeStatus: boolean,
    pendingStatus: boolean,
    canceledStatus: boolean,
    activeCappedAmount: number | null,
    activeBalanceUsed: number | null,
    activeBalanceRemaining: number | null,
    pendingCappedAmount: number | null,
    accountId: string
}
