import { ClientSession, ObjectId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoBillingRepository } from './IMongoBillingRepository';
import { MongoBillingMapper } from './MongoBillingMapper';
import { IBilling, IMongoBilling } from './Types';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoBillingRepository implements IMongoBillingRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoBilling>,
        private readonly updateOperation: IUpdateOperation<IMongoBilling>,
    ) {}

    async findByChargeflowId(chargeflowId: string, clientSession?: ClientSession | undefined): Promise<WithId<IBilling>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { 'chargeflowId': new ObjectId(chargeflowId) },
            clientSession,
        );
        return doc ? MongoBillingMapper.fromMongoBilling(doc) : null;
    }

    async updateOneBillingByChargeflowId(
        chargeflowId: string, updatedAt: Date, billingOn: Date, cappedAmount: number, balanceUsed: number, balanceRemaining: number, clientSession?: ClientSession,
    ): Promise<WithId<IBilling> | null> {
        const result = await this.updateOperation.updateWithFilter(
            { 'chargeflowId': new ObjectId(chargeflowId) },
            {
                $set: {
                    dateUpdated: updatedAt
                        ? new Date(updatedAt)
                        : undefined,
                    billingDate: billingOn
                        ? new Date(billingOn)
                        : undefined,
                    activeCappedAmount: Number(cappedAmount),
                    activeBalanceUsed: Number(balanceUsed),
                    activeBalanceRemaining: Number(balanceRemaining),
                },
            },
            clientSession);
        return result ? MongoBillingMapper.fromMongoBilling(result) : null;
    }
}
