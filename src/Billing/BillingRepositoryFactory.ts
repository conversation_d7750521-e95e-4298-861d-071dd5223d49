import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoBillingRepository } from './IMongoBillingRepository';
import { IMongoBilling } from './Types';
import { MongoBillingRepository } from './MongoBillingRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class BillingRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IMongoBillingRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoBilling>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoBilling>(mongoClient, dbName, collectionName);

        return new MongoBillingRepository(findByQueryOperation, updateOperation);
    }
}
