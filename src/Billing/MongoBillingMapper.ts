import { WithId as MongoWithId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IBilling, IMongoBilling } from './Types';

export class MongoBillingMapper {

    static fromMongoBilling(doc: MongoWithId<IMongoBilling>): WithId<IBilling> {
        if (!doc) {
            throw new Error('Billing is required for mapping');
        }

        return {
            ...doc,
            _id: doc._id?.toHexString(),
            chargeflowId: doc.chargeflowId?.toHexString(),
            accountId: doc.accountId?.toHexString(),
            shopId: doc.shopId?.toHexString(),
        };
    }
}
