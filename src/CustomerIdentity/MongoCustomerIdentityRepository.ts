import { ClientSession } from 'mongodb';
import { ICustomerIdentityData } from './ICustomerIdentity';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ICustomerIdentityRepository } from './ICustomerIdentityRepository';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoCustomerIdentityRepository implements ICustomerIdentityRepository {
    constructor(
        private readonly updateOperation: IUpdateOperation<ICustomerIdentityData>,
        private readonly findByQueryOperation: IFindByQueryOperation<ICustomerIdentityData>,
    ) {}

    async getCustomerIdentityByRequestHash(
        requestHash: string, clientSession?: ClientSession,
    ): Promise<ICustomerIdentityData> {
        const page = 1;
        const pageSize = 1;
        const res = await this.findByQueryOperation.find({ requestHash }, page, pageSize, clientSession);
        return res.items[0];
    }

    async saveCustomerIdentityData(
        requestHash: string, customerIdentityData: ICustomerIdentityData, clientSession?: ClientSession,
    ): Promise<void> {
        await this.updateOperation.upsert({ requestHash }, { $set: customerIdentityData }, clientSession);
    }
}
