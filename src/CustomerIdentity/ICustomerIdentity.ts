export interface ICustomerIdentityData {
    dateCreated: Date,
    response: ICustomerIdentity
}

export interface ICustomerIdentityAppliedRule {
    name: string,
    id: string,
    operation: string,
    score: number
}

export interface ICustomerIdentityBreach {
    name: string,
    domain?: string | null,
    date?: string | null
}

export interface ICustomerIdentityFlag {
    note: string | null,
    date: string | null,
    industry: string | null
}

export interface ICustomerIdentity {
    dateReceived: Date;
    customerIdentityProvider?: string | null;
    id?: string | null;
    status?: string | null;
    score?: number | null;
    apiVersion?: string | null;
    ipDetails?: {
        ip?: string | null;
        score?: number | null;
        country?: string | null;
        state?: string | null;
        city?: string | null;
        isp?: {
            name?: string | null;
            type?: string | null;
            description?: string | null;
        } | null;
        lat?: number | null;
        lon?: number | null;
        tor?: boolean | null;
        harmful?: boolean | null;
        vpn?: boolean | null;
        webProxy?: boolean | null;
        publicProxy?: boolean | null;
        spamList?: {
            count?: number | null;
            urls?: string[] | null;
        } | null;
        history?: {
            searches?: number | null;
            uniqueSearches?: number | null;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: ICustomerIdentityFlag[] | null;
    };
    emailDetails?: {
        email?: string | null;
        score?: number | null;
        deliverable?: boolean | null;
        domain?: {
            tld?: string | null;
            name?: string | null;
            dateCreated?: string | null;
            dateUpdated?: string | null;
            registered?: boolean | null;
            registrarName?: string | null;
            disposable?: boolean | null;
            free?: boolean | null;
            custom?: boolean | null;
            dmarcEnforced?: boolean | null;
            spfStrict?: boolean | null;
            validMx?: boolean | null;
            acceptAll?: boolean | null;
            suspiciousTld?: boolean | null;
            websiteExists?: boolean | null;
            registeredAccounts?: string[] | null;
            registeredAccountsDetails?: Record<string, unknown> | null;
        };
        breachDetails?: {
            emailCompromised?: boolean | null;
            breaches?: ICustomerIdentityBreach[] | null;
        };
        history?: {
            searches?: number | null;
            uniqueSearches?: number | null;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: ICustomerIdentityFlag[] | null;
    };
    binDetails?: {
        bin?: string | null;
        issuanceBank?: string | null;
        cardNetwork?: string | null;
        cardType?: string | null;
        cardLevel?: string | null;
        cardCountry?: string | null;
        binValid?: boolean | null;
        cardIssuer?: string | null;
    };
    phoneDetails?: {
        number?: number | null;
        valid?: boolean | null;
        disposable?: boolean | null;
        type?: string | null;
        country?: string | null;
        carrier?: string | null;
        score?: number | null;
        registeredAccounts?: string[] | null;
        registeredAccountsDetails?: Record<string, unknown> | null;
        history?: {
            searches?: number | null;
            uniqueSearches?: number | null;
            firstSearch?: string | null;
            recentSearch?: string | null;
        };
        flags?: ICustomerIdentityFlag[] | null;
    };
    appliedRules?: ICustomerIdentityAppliedRule[] | null;
}
