import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { CustomerIdentityRepositoryFactory } from './CustomerIdentityRepositoryFactory';
import { MongoClient } from 'mongodb';
import { MongoCustomerIdentityRepository } from './MongoCustomerIdentityRepository';

describe(CustomerIdentityRepositoryFactory.name, () => {
    describe(CustomerIdentityRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = CustomerIdentityRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoCustomerIdentityRepository);
        });
    });
});
