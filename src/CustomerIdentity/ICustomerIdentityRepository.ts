import { ClientSession } from 'mongodb';
import { ICustomerIdentityData } from './ICustomerIdentity';

export interface ICustomerIdentityRepository {
    getCustomerIdentityByRequestHash(
        requestHash: string, clientSession?: ClientSession
    ): Promise<ICustomerIdentityData>,

    saveCustomerIdentityData(
        requestHash: string, customerIdentityData: ICustomerIdentityData, clientSession?: ClientSession
    ): Promise<void>
}
