import { MongoClient } from 'mongodb';
import { ICustomerIdentityRepository } from './ICustomerIdentityRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { ICustomerIdentityData } from './ICustomerIdentity';
import { MongoCustomerIdentityRepository } from './MongoCustomerIdentityRepository';

export class CustomerIdentityRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): ICustomerIdentityRepository {
        const updateOperation = new MongoUpdateOperation<ICustomerIdentityData>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<ICustomerIdentityData>(mongoClient, dbName, collectionName);

        return new MongoCustomerIdentityRepository(updateOperation, findByQueryOperation);
    }
}
