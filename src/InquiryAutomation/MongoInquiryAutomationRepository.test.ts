import { mock } from 'jest-mock-extended';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, ObjectId } from 'mongodb';
import { MongoInquiryAutomationRepository } from './MongoInquiryAutomationRepository';
import { IMongoInquiryAutomation } from './IInquiryAutomation';
import inquiryAutomationMocks from './__mocks__/InquriyAutomation';

describe(MongoInquiryAutomationRepository.name, () => {
    let mongoInquiryAutomationRepository: MongoInquiryAutomationRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoInquiryAutomation>>();

    beforeEach(() => {
        mongoInquiryAutomationRepository = new MongoInquiryAutomationRepository(
            findByQueryOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoInquiryAutomationRepository.prototype.findInquirySessionCountByDisputeId.name, () => {
        it('returns paginated result of ChargeflowId objects without accountId', async () => {
            expect.assertions(3);

            // GIVEN
            const disputeId = new ObjectId();
            const inquiryAutomation = inquiryAutomationMocks.getInquiryAutomation();
            const session = mock<ClientSession>();

            const projection = {
                'iterationsCount': 1,
                _id: 0,
            };

            const findByQueryResult = inquiryAutomationMocks.getMongoInquiryAutomation();

            findByQueryOperation.findOne.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoInquiryAutomationRepository.findInquirySessionCountByDisputeId(disputeId.toHexString(), session);

            // THEN
            expect(result).toEqual(inquiryAutomation.iterationsCount);

            expect(findByQueryOperation.findOne).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.findOne).toHaveBeenCalledWith({ 'disputeId': disputeId }, session, projection);
        });
    });
});
