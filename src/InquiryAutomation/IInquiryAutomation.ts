import { Document, ObjectId } from 'mongodb';

interface EventHistoryItem {
    event: string;
    date: Date;
}

interface InquiryMessage {
    datePosted: Date;
    postedBy: string;
    content: string;
    documents: {name: string, url: string}[];
}

export interface IInquiryAutomationBase {
    eventHistory: EventHistoryItem[],
    projectName: string;
    sessionDateCreated: Date;
    sessionDateUpdated: Date;
    sessionId: string;
    disputeResult: string;
    sessionFeedback: string;
    chatMessages: InquiryMessage[];
    hasEditedResponse: boolean;
    iterationsCount: number;
}

export interface IInquiryAutomation extends IInquiryAutomationBase {
    _id: string;
    chargeflowId: string;
    disputeId: string;
    accountId: string;
}

export interface IMongoInquiryAutomation extends IInquiryAutomationBase, Document {
    _id: ObjectId;
    chargeflowId: ObjectId;
    disputeId: ObjectId;
    accountId: ObjectId;
}
