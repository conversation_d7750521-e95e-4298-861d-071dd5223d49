import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IInquiryAutomationRepository } from './IInquiryAutomationRepository';
import { IMongoInquiryAutomation } from './IInquiryAutomation';

export class MongoInquiryAutomationRepository implements IInquiryAutomationRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoInquiryAutomation>,
    ) {}

    async findInquirySessionCountByDisputeId(disputeId: string, session?: ClientSession): Promise<number> {
        const filter = {
            disputeId: new ObjectId(disputeId),
        };
        const projection = {
            'iterationsCount': 1,
            _id: 0,
        };

        const result = await this.findByQueryOperation.findOne(filter, session, projection);
        return result?.iterationsCount ?? 0;
    }
}
