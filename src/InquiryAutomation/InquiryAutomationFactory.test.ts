import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { InquiryAutomationRepositoryFactory } from './InquiryAutomationFactory';
import { MongoInquiryAutomationRepository } from './MongoInquiryAutomationRepository';

describe(InquiryAutomationRepositoryFactory.name, () => {
    describe(InquiryAutomationRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = InquiryAutomationRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoInquiryAutomationRepository);
        });
    });
});
