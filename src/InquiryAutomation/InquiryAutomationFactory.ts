import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IInquiryAutomationRepository } from './IInquiryAutomationRepository';
import { IMongoInquiryAutomation } from './IInquiryAutomation';
import { MongoInquiryAutomationRepository } from './MongoInquiryAutomationRepository';

export class InquiryAutomationRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IInquiryAutomationRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoInquiryAutomation>(mongoClient, dbName, collectionName);

        return new MongoInquiryAutomationRepository(
            findByQueryOperation,
        );
    }
}
