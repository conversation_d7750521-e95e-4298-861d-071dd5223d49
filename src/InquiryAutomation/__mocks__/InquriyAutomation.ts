import { ObjectId, WithId } from 'mongodb';
import { IInquiryAutomation, IMongoInquiryAutomation } from '../IInquiryAutomation';

const defaultInquiryAutomation = {
    eventHistory: [],
    projectName: 'example-project',
    sessionDateCreated: new Date(),
    sessionDateUpdated: new Date(),
    sessionId: 'example-session-id',
    disputeResult: 'example-dispute-result',
    sessionFeedback: 'example-session-feedback',
    chatMessages: [],
    hasEditedResponse: true,
    iterationsCount: 2,
};

const getInquiryAutomation = (overrides: Partial<IInquiryAutomation> = {}): WithId<IInquiryAutomation> => ({
    _id: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    disputeId: new ObjectId().toHexString(),
    chargeflowId: new ObjectId().toHexString(),
    ...defaultInquiryAutomation,
    ...overrides,
});

const getMongoInquiryAutomation = (overrides: Partial<IMongoInquiryAutomation> = {}): WithId<IMongoInquiryAutomation> => ({
    _id: new ObjectId(),
    accountId: new ObjectId(),
    disputeId: new ObjectId(),
    chargeflowId: new ObjectId(),
    ...defaultInquiryAutomation,
    ...overrides,
});

export default {
    getInquiryAutomation,
    getMongoInquiryAutomation,
};
