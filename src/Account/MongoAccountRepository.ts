import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IAccount, IAccountDto } from './IAccount';
import { IAccountRepository } from './IAccountRepository';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { DateTime } from 'luxon';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoAccountRepository implements IAccountRepository {

    constructor(
        private readonly addOperation: IAddOperation<IAccount>,
        private readonly findByQueryOperation: IFindByQueryOperation<IAccount>,
        private readonly updateOperation: IUpdateOperation<IAccount>,
    ) {}

    add(account: IAccountDto, session?: ClientSession): Promise<WithId<IAccount>> {
        const now = DateTime.now();

        return this.addOperation.add({
            ...account,
            createdAt: now,
            updatedAt: now,
        }, session);
    }

    findByEmail(
        email: string, page?: number, pageSize?: number, session?: ClientSession,
    ): Promise<IPaginatedFindByQueryResult<IAccount>> {
        return this.findByQueryOperation.find({ email }, page, pageSize, session);
    }

    async findOneByLegacyChargeflowId(
        chargeflowId: ObjectId, session?: ClientSession,
    ): Promise<IAccount | null> {
        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation
            .find({ 'legacy.chargeflowId': chargeflowId }, page, pageSize, session);

        return result.items[0] || null;
    }

    async findOneByAccountId(
        accountId: ObjectId, session?: ClientSession,
    ): Promise<IAccount | null> {
        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation.find({ _id: accountId }, page, pageSize, session);

        return result.items[0] || null;
    }

    async update(
        id: ObjectId, accountDto: IAccountDto, session?: ClientSession,
    ): Promise<IAccount | null> {
        return this.updateOperation.update({
            _id: id, ...accountDto,
        }, session);
    }
}
