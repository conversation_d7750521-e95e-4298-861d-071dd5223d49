import { ClientSession, ObjectId, WithId } from 'mongodb';
import { IAccount, IAccountDto } from './IAccount';
import { IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';

export interface IAccountRepository {
    add: (
        entity: IAccountDto, session?: ClientSession
    ) => Promise<WithId<IAccount>>,

    findByEmail: (
        email: string, page?: number, pageSize?: number, session?: ClientSession
    ) => Promise<IPaginatedFindByQueryResult<IAccount>>

    update: (
        id: ObjectId, accountDto: IAccountDto, session?: ClientSession,
    ) => Promise<IAccount | null>

    findOneByLegacyChargeflowId: (
        chargeflowId: ObjectId, session?: ClientSession
    ) => Promise<IAccount | null>,

    findOneByAccountId: (
        accountId: ObjectId, session?: ClientSession
    ) => Promise<IAccount | null>,
}
