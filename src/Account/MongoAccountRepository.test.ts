import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoAccountRepository } from './MongoAccountRepository';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { IAccount } from './IAccount';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import accountMocks from './__mocks__/Account';
import { ClientSession, ObjectId } from 'mongodb';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { DateTime } from 'luxon';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

describe(MongoAccountRepository.name, () => {
    let mongoAccountRepository: MongoAccountRepository;
    const addOpertion = mock<IAddOperation<IAccount>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IAccount>>();
    const updateOperation = mock<IUpdateOperation<IAccount>>();

    beforeEach(() => {
        mongoAccountRepository = new MongoAccountRepository(
            addOpertion,
            findByQueryOperation,
            updateOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoAccountRepository.prototype.add.name, () => {
        it('calls add operation', async () => {
            expect.assertions(3);

            // GIVEN
            const accountDto = accountMocks.getAccountDto();
            const account = accountMocks.getAccount(accountDto);
            const session = mock<ClientSession>();

            addOpertion.add.mockResolvedValue(account);

            // WHEN
            const result = await mongoAccountRepository.add(accountDto, session);

            // THEN
            expect(result).toBe(account);

            expect(addOpertion.add).toHaveBeenCalledTimes(1);
            expect(addOpertion.add).toHaveBeenCalledWith({
                ...accountDto,
                createdAt: expect.any(DateTime),
                updatedAt: expect.any(DateTime),
            }, session);
        });
    });

    describe(MongoAccountRepository.prototype.findByEmail.name, () => {
        it('calls find by query operation', async () => {
            expect.assertions(3);

            // GIVEN
            const email = faker.internet.email();
            const page = 1;
            const pageSize = 1;
            const account = accountMocks.getAccount();
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ account ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoAccountRepository.findByEmail(email, page, pageSize, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ email }, page, pageSize, session);
        });
    });

    describe(MongoAccountRepository.prototype.findOneByLegacyChargeflowId.name, () => {
        it('calls find by query operation and returns one account', async () => {
            expect.assertions(3);

            // GIVEN
            const chargeflowId = new ObjectId();
            const account = accountMocks.getAccount();
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ account ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoAccountRepository.findOneByLegacyChargeflowId(chargeflowId, session);

            // THEN
            expect(result).toEqual(account);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ 'legacy.chargeflowId': chargeflowId }, 1, 1, session);
        });
    });

    describe(MongoAccountRepository.prototype.findOneByAccountId.name, () => {
        it('calls find by query operation and returns one account', async () => {
            expect.assertions(3);

            // GIVEN
            const accountId = new ObjectId();
            const account = accountMocks.getAccount();
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ account ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoAccountRepository.findOneByAccountId(accountId, session);

            // THEN
            expect(result).toEqual(account);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ _id: accountId }, 1, 1, session);
        });
    });

    describe(MongoAccountRepository.prototype.add.name, () => {
        it('calls update operation', async () => {
            expect.assertions(2);

            // GIVEN
            const accountId= new ObjectId('65b1132436972cac74c0669e');
            const accountDto = accountMocks.getAccountDto();
            const session = mock<ClientSession>();

            // WHEN
            await mongoAccountRepository.update(accountId, accountDto, session);

            // THEN
            expect(updateOperation.update).toHaveBeenCalledTimes(1);
            expect(updateOperation.update).toHaveBeenCalledWith({
                _id: accountId,
                ...accountDto,
            }, session);
        });
    });
});
