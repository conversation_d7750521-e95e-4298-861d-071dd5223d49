import { MongoClient } from 'mongodb';
import { MongoAccountRepository } from './MongoAccountRepository';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IAccount } from './IAccount';
import { IAccountRepository } from './IAccountRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class AccountRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IAccountRepository {
        const addOperation = new MongoAddOperation<IAccount>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IAccount>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IAccount>(mongoClient, dbName, collectionName);

        return new MongoAccountRepository(
            addOperation,
            findByQueryOperation,
            updateOperation,
        );
    }
}
