import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { AccountRepositoryFactory } from './AccountRepositoryFactory';
import { MongoClient } from 'mongodb';
import { MongoAccountRepository } from './MongoAccountRepository';

describe(AccountRepositoryFactory.name, () => {
    describe(AccountRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = AccountRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoAccountRepository);
        });
    });
});
