import { IAccount, IAccountDto } from '../IAccount';
import { ObjectId, WithId } from 'mongodb';
import { DateTime } from 'luxon';

const getAccountDto = (overrides: Partial<IAccountDto> = {}): IAccountDto => ({
    email: '<EMAIL>',
    legacy: {
        chargeflowId: new ObjectId(),
        shopId: new ObjectId(),
        customerId: new ObjectId(),
    },
    ...overrides,
});

const getAccount = (overrides: Partial<IAccount> = {}): WithId<IAccount> => ({
    _id: new ObjectId(),
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    ...getAccountDto(),
    ...overrides,
});

export default {
    getAccountDto,
    getAccount,
};
