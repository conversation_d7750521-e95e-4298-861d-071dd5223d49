import { mock } from 'jest-mock-extended';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { MongoShopRepository } from './MongoShopRepository';
import { IMongoShop } from './IShop';
import shopMocks from './__mocks__/Shop';
import { MongoShopMapper } from './MongoShopMapper';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IBulkUpdateResponse } from '../Dispute/Types';

describe(MongoShopRepository.name, () => {
    let mongoShopRepository: MongoShopRepository;
    const updateOperation = mock<IUpdateOperation<IMongoShop>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoShop>>();
    const getByIdOperation = mock<IGetByIdOperation<IMongoShop>>();
    const session = mock<ClientSession>();

    beforeEach(() => {
        mongoShopRepository = new MongoShopRepository(
            findByQueryOperation,
            getByIdOperation,
            updateOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoShopRepository.prototype.update.name, () => {
        it('returns updated shop', async () => {
            expect.assertions(3);

            // GIVEN
            const id = new ObjectId();

            const expectedResult = shopMocks.getShop({
                _id: id.toHexString(),

            });

            updateOperation.update.mockResolvedValue(MongoShopMapper.toMongoShop(expectedResult) as WithId<IMongoShop>);

            // WHEN
            const result = await mongoShopRepository
                .update(id.toHexString(), {  registrationType: 'Test registrationType', domain: 'Test domain', platform: 'Test platform' }, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(updateOperation.update).toHaveBeenCalledTimes(1);
            expect(updateOperation.update).toHaveBeenCalledWith({
                _id: id,
                registrationType: 'Test registrationType',
                domain: 'Test domain',
                platform: 'Test platform',
            }, session);
        });
    });

    describe(MongoShopRepository.prototype.getById.name, () => {
        it('returns a single shop, if found', async () => {
            expect.assertions(3);

            // GIVEN
            const shop = shopMocks.getShop();
            const session = mock<ClientSession>();

            const getByIdResult = MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop>;

            getByIdOperation.get.mockResolvedValue(getByIdResult);

            // WHEN
            const result = await mongoShopRepository.getById(shop._id, session);

            // THEN
            expect(result).toEqual(shop);

            expect(getByIdOperation.get).toHaveBeenCalledTimes(1);
            expect(getByIdOperation.get).toHaveBeenCalledWith(new ObjectId(shop._id), session);
        });

        it('returns null, if shop is not found', async () => {
            expect.assertions(3);

            // GIVEN
            const session = mock<ClientSession>();
            getByIdOperation.get.mockResolvedValue(null);

            // WHEN
            const result = await mongoShopRepository.getById(ObjectId.createFromTime(1).toHexString(), session);

            // THEN
            expect(result).toBeNull();

            expect(getByIdOperation.get).toHaveBeenCalledTimes(1);
            expect(getByIdOperation.get).toHaveBeenCalledWith(ObjectId.createFromTime(1), session);
        });
    });

    describe(MongoShopRepository.prototype.findOneByLegacyChargeflowId.name, () => {
        it('returns paginated result of ChargeflowId objects without accountId', async () => {
            expect.assertions(3);

            // GIVEN
            const chargeflowId = new ObjectId();
            const shop = shopMocks.getShop();
            const session = mock<ClientSession>();

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoShopRepository.findOneByLegacyChargeflowId(chargeflowId.toHexString(), session);

            // THEN
            expect(result).toEqual(shop);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ 'chargeflow_id': chargeflowId }, 1, 1, session);
        });
    });

    describe(MongoShopRepository.prototype.findOneByShopName.name, () => {
        it('returns paginated result of shopName objects', async () => {
            expect.assertions(3);

            // GIVEN
            const shopName = 'test-shop-name';
            const shop = shopMocks.getShop();
            const session = mock<ClientSession>();

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoShopRepository.findOneByShopName(shopName, session);

            // THEN
            expect(result).toEqual(shop);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ $or: [ { 'name': shopName },{ 'platform_name': shopName } ] }, 1, 1, session);
        });
    });

    describe(MongoShopRepository.prototype.findByRegistrationType.name, () => {
        it('returns paginated result of shops found by registration type', async () => {
            expect.assertions(3);

            // GIVEN
            const shop = shopMocks.getShop();
            const registrationType = 'Test registrationType';
            const page = 1;
            const pageSize = 1;
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ shop ],
                pagination: paginationMocks.getPaginationResult(),
            };

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoShopRepository.findByRegistrationType(registrationType, page, pageSize, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ 'registrationType': registrationType }, 1, 1, session);
        });
    });

    describe(MongoShopRepository.prototype.addProcessorId.name, () => {
        it('adds a new processor id when no processor of the same type exists', async () => {
            expect.assertions(3);

            // GIVEN
            const accountId = new ObjectId();
            const processorType = 'paypal';
            const processorId = '64eb0b779fbe239083525cf4';
            const shop = shopMocks.getShop();

            const expectedResult = {
                ...shop,
                processors: [
                    { type: processorType, ids: [ processorId ] },
                ],
            };

            updateOperation.updateWithFilter.mockResolvedValue(MongoShopMapper.toMongoShop(expectedResult) as WithId<IMongoShop>);

            // WHEN
            const result = await mongoShopRepository.addProcessorId(accountId.toHexString(), processorType, processorId, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                { accountId: new ObjectId(accountId) }, [
                    {
                        $set: {
                            processors: {
                                $cond: {
                                    if: {
                                        $in: [ processorType, { $ifNull: [ '$processors.type', [] ] } ],
                                    },
                                    then: {
                                        $map: {
                                            input: { $ifNull: [ '$processors', [] ] },
                                            as: 'processor',
                                            in: {
                                                $cond: {
                                                    if: { $eq: [ '$$processor.type', processorType ] },
                                                    then: {
                                                        $mergeObjects: [
                                                            '$$processor',
                                                            {
                                                                ids: { $setUnion: [ '$$processor.ids', [ processorId ] ] },
                                                            },
                                                        ],
                                                    },
                                                    else: '$$processor',
                                                },
                                            },
                                        },
                                    },
                                    else: {
                                        $concatArrays: [
                                            { $ifNull: [ '$processors', [] ] },
                                            [ { type: processorType, ids: [ processorId ] } ],
                                        ],
                                    },
                                },
                            },
                        },
                    },
                ],
                session,
            );
        });

        it('adds a new processor id to an existing processor of the same type', async () => {
            expect.assertions(3);

            // GIVEN
            const accountId = new ObjectId();
            const processorType = 'paypal';
            const processorId = '64eb0b779fbe239083525cf4';
            const shop = shopMocks.getShop({
                processors: [
                    { type: processorType, ids: [ '64eb0b779fbe239083525cf5' ] },
                ],
            });

            const expectedResult = {
                ...shop,
                processors: [
                    { type: processorType, ids: [ '64eb0b779fbe239083525cf5', processorId ] },
                ],
            };

            updateOperation.updateWithFilter.mockResolvedValue(MongoShopMapper.toMongoShop(expectedResult) as WithId<IMongoShop>);

            // WHEN
            const result = await mongoShopRepository.addProcessorId(accountId.toHexString(), processorType, processorId, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                { accountId: new ObjectId(accountId) }, [
                    {
                        $set: {
                            processors: {
                                $cond: {
                                    if: {
                                        $in: [ processorType, { $ifNull: [ '$processors.type', [] ] } ],
                                    },
                                    then: {
                                        $map: {
                                            input: { $ifNull: [ '$processors', [] ] },
                                            as: 'processor',
                                            in: {
                                                $cond: {
                                                    if: { $eq: [ '$$processor.type', processorType ] },
                                                    then: {
                                                        $mergeObjects: [
                                                            '$$processor',
                                                            {
                                                                ids: { $setUnion: [ '$$processor.ids', [ processorId ] ] },
                                                            },
                                                        ],
                                                    },
                                                    else: '$$processor',
                                                },
                                            },
                                        },
                                    },
                                    else: {
                                        $concatArrays: [
                                            { $ifNull: [ '$processors', [] ] },
                                            [ { type: processorType, ids: [ processorId ] } ],
                                        ],
                                    },
                                },
                            },
                        },
                    },
                ],
                session,
            );
        });
    });

    describe(MongoShopRepository.prototype.updateShopChargeflowStatus.name, () => {
        it('updates the chargeflow status of a shop', async () => {

            const chargeflowId = '64eb0b779fbe239083525cf4';
            const chargeflowStatus = 'active';
            const shop = shopMocks.getShop();
            const dateUpdated = new Date();

            const expectedResult = {
                ...shop,
                dateUpdated,
                chargeflowStatus: chargeflowStatus,
            };

            updateOperation.updateWithFilter.mockResolvedValue(MongoShopMapper.toMongoShop(expectedResult) as WithId<IMongoShop>);

            await mongoShopRepository.updateShopChargeflowStatus(chargeflowId, chargeflowStatus, dateUpdated, session);

            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                { 'chargeflow_id': new ObjectId(chargeflowId) }, [ { $set: { chargeflow_status: chargeflowStatus, date_updated: new Date(dateUpdated) } } ], session,
            );
        });
    });

    describe(MongoShopRepository.prototype.updateKeyValidity.name, () => {
        it('updates the key validity of a shop', async () => {
            const id = '64eb0b779fbe239083525cf4';
            const isKeyValid = true;
            const shop = shopMocks.getShop();

            const expectedResult = {
                ...shop,
                isKeyValid: isKeyValid,
            };

            updateOperation.updateWithFilter.mockResolvedValue(MongoShopMapper.toMongoShop(expectedResult) as WithId<IMongoShop>);

            await mongoShopRepository.updateKeyValidity(id, isKeyValid, session);

            expect(updateOperation.updateWithFilter).toHaveBeenCalledTimes(1);
            expect(updateOperation.updateWithFilter).toHaveBeenCalledWith(
                { _id: new ObjectId(id) }, { $set: { isKeyValid: isKeyValid } }, session,
            );
        });
    });

    describe(MongoShopRepository.prototype.getShopifyPaymentsShops.name, () => {
        it('returns paginated result of Shopify Payments shops', async () => {
            expect.assertions(3);

            // GIVEN
            const shop = shopMocks.getShop();
            const page = 1;
            const pageSize = 1;
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ shop ],
                pagination: paginationMocks.getPaginationResult(),
            };

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoShopRepository.getShopifyPaymentsShops(page, pageSize, undefined, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ chargeflow_status: 'enabled', platform: 'shopify' }, 1, 1, session);
        });
    });

    describe(MongoShopRepository.prototype.getAllShopifyShops.name, () => {
        it('returns paginated result of Shopify shops', async () => {
            expect.assertions(3);

            // GIVEN
            const shop = shopMocks.getShop();
            const page = 1;
            const pageSize = 1;
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ shop ],
                pagination: paginationMocks.getPaginationResult(),
            };

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            // WHEN
            const result = await mongoShopRepository.getAllShopifyShops(page, pageSize, undefined, session);

            // THEN
            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith({ 'connect.access_token': { $ne: '' }, platform: 'shopify' }, 1, 1, session);
        });
    });

    describe(MongoShopRepository.prototype.updateShopSuccessRates.name, () => {
        it('updates the success rates for shops based on the provided filter', async () => {
            expect.assertions(3);

            const successRate = 25;
            const filter = { platform: 'shopify' };
            const bulkUpdateResponse = { matchedCount: 1, modifiedCount: 1 };

            updateOperation.bulkUpdate.mockResolvedValue(bulkUpdateResponse as unknown as IBulkUpdateResponse);

            const result = await mongoShopRepository.updateShopSuccessRates(successRate, filter);

            expect(result).toEqual(bulkUpdateResponse);

            expect(updateOperation.bulkUpdate).toHaveBeenCalledTimes(1);
            expect(updateOperation.bulkUpdate).toHaveBeenCalledWith([
                {
                    updateMany: {
                        filter,
                        update: { $set: { 'chargeflow_success_rate': successRate } },
                    },
                },
            ]);
        });
    });

    describe(MongoShopRepository.prototype.findManyByChargeflowIDs.name, () => {
        it('returns paginated result of shops found by chargeflow IDs', async () => {
            expect.assertions(3);

            const shop = shopMocks.getShop();
            const chargeflowIDs = [ '64eb0b779fbe239083525cf4', '64eb0b779fbe239083525cf5' ];
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ shop ],
                pagination: paginationMocks.getPaginationResult(),
            };

            const findByQueryResult = {
                items: [ MongoShopMapper.toMongoShop(shop) as WithId<IMongoShop> ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const result = await mongoShopRepository.findManyByChargeflowIDs(chargeflowIDs, 1, 100, session);

            expect(result).toEqual(expectedResult);

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith(
                {
                    chargeflow_id: {
                        $in: chargeflowIDs.map(id => new ObjectId(id)),
                    },
                },
                1,
                100,
                session,
            );
        });

        it('returns empty result when no shops match the chargeflow IDs', async () => {
            expect.assertions(3);

            const chargeflowIDs = [ '64eb0b779fbe239083525cf4', '64eb0b779fbe239083525cf5' ];
            const session = mock<ClientSession>();

            const findByQueryResult = {
                items: [],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(findByQueryResult);

            const result = await mongoShopRepository.findManyByChargeflowIDs(chargeflowIDs, 1, 100, session);

            expect(result).toEqual({
                items: [],
                pagination: paginationMocks.getPaginationResult(),
            });

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find).toHaveBeenCalledWith(
                {
                    chargeflow_id: {
                        $in: chargeflowIDs.map(id => new ObjectId(id)),
                    },
                },
                1,
                100,
                session,
            );
        });
    });

});
