import { Document, ObjectId } from 'mongodb';
import { ChargeflowStatus, ClientStatus, ShopifyPlan } from './Types';

export interface IConnect {
    access_token: string;
    shop_name: string;
    scope: string;
}

export interface IProcessors {
    type: string;
    ids: string[];
}

export interface IShopNote {
    datePosted: Date;
    content: string;
}

export interface IShopBase {
    registrationType: string;
    domain?: string | null;
    platform: string;
    name: string;
    disputes_status?: string;
    date_created: Date;
    date_updated: Date;
    activation_date?: Date;
    closed_date?: Date;
    connect: IConnect;
    chargeflow_success_rate: number;
    shopify_shop_id: string;
    myshopify_domain: string;
    email: string;
    support_email: string;
    is_data_ready: boolean;
    shop_type: string | null;
    timezone: string | null;
    shopify_payments_connected: boolean;
    shopify_plan: ShopifyPlan | null;
    shopNotes: IShopNote[];
    chargeflow_status: ChargeflowStatus;
    client_status?: ClientStatus;
    paying_date?: Date;
    processors?: IProcessors | IProcessors[];
    utmInfo?: UtmInfo;
    source?: string | null;
    vendor_account_id?: string;
    platform_name?: string;
}

type UtmInfo = {
    utmSource: string | null;
    utmMedium: string | null;
    utmCampaign: string | null;
}

export interface IShop extends IShopBase {
    _id?: string;
    accountId?: string;
    chargeflow_id?: string;
    customer_id?: string;
}

export interface IMongoShop extends IShopBase, Document {
    _id?: ObjectId;
    accountId?: ObjectId;
    chargeflow_id?: ObjectId;
    customer_id?: ObjectId;
}
