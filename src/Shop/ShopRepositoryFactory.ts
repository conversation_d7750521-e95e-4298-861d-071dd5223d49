import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoShopRepository } from './MongoShopRepository';
import { IMongoShop } from './IShop';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { IShopRepository } from './IShopRepository';
import { MongoGetByIdOperation } from '../shared/operation/GetByIdOperation/MongoGetByIdOperation';

export class ShopRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IShopRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoShop>(mongoClient, dbName, collectionName);
        const getByIdOperation = new MongoGetByIdOperation<IMongoShop>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoShop>(mongoClient, dbName, collectionName);

        return new MongoShopRepository(
            findByQueryOperation,
            getByIdOperation,
            updateOperation,
        );
    }
}
