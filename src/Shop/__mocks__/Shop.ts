import { ObjectId, WithId } from 'mongodb';
import { IMongoShop, IShop, IShopBase } from '../IShop';
import { ChargeflowStatus, ClientStatus, ShopifyPlan } from '../Types';

const defaultShop: IShopBase = {
    registrationType: 'standard',
    domain: 'example.com',
    platform: 'shopify',
    name: 'Example Shop',
    disputes_status: 'active',
    date_created: new Date(),
    date_updated: new Date(),
    activation_date: new Date(),
    connect: {
        access_token: 'abc123',
        shop_name: 'Example Shop',
        scope: 'read_orders,write_products',
    },
    chargeflow_success_rate: 95.5,
    shopify_shop_id: '123456789',
    myshopify_domain: 'example.myshopify.com',
    email: '<EMAIL>',
    support_email: '<EMAIL>',
    is_data_ready: true,
    shop_type: 'retail',
    timezone: 'America/New_York',
    shopify_payments_connected: true,
    shopify_plan: ShopifyPlan.ShopifyBasic,
    shopNotes: [
        {
            datePosted: new Date(),
            content: 'Initial setup complete.',
        },
    ],
    chargeflow_status: ChargeflowStatus.Active,
    client_status: ClientStatus.Active,
    paying_date: new Date(),
    processors: [
        {
            type: 'payment',
            ids: [ 'proc1', 'proc2' ],
        },
    ],
};

const getShop = (overrides: Partial<IShop> = {}): WithId<IShop> => ({
    _id: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    chargeflow_id: new ObjectId().toHexString(),
    customer_id: new ObjectId().toHexString(),
    ...defaultShop,
    ...overrides,
});

const getMongoShop = (overrides: Partial<IMongoShop> = {}): WithId<IMongoShop> => ({
    _id: new ObjectId(),
    accountId: new ObjectId(),
    chargeflow_id: new ObjectId(),
    customer_id: new ObjectId(),
    ...defaultShop,
    ...overrides,
});

export default {
    getShop,
    getMongoShop,
};
