import { ClientSession, Filter, ObjectId, UpdateFilter, Document } from 'mongodb';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IShopRepository } from './IShopRepository';
import { IShop, IMongoShop } from './IShop';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { WithId } from '../shared/helper-types';
import { MongoShopMapper } from './MongoShopMapper';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IGetByIdOperation } from '../shared/operation/GetByIdOperation/IGetByIdOperation';
import { IBulkUpdateResponse } from '../Dispute/Types';
import { ChargeflowStatus } from './Types';

export class MongoShopRepository implements IShopRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoShop>,
        private readonly getByIdOperation: IGetByIdOperation<IMongoShop>,
        private readonly updateOperation: IUpdateOperation<IMongoShop>,
    ) { }

    async getById(
        id: string, session?: ClientSession,
    ): Promise<WithId<IShop> | null> {
        const result = await this.getByIdOperation
            .get(new ObjectId(id), session);

        return result ? MongoShopMapper.fromMongoShop(result) : null;
    }

    async findOneByLegacyChargeflowId(
        chargeflowId: string, session?: ClientSession,
    ): Promise<WithId<IShop> | null> {
        const page = 1;
        const pageSize = 1;

        const result = await this.findByQueryOperation
            .find({ 'chargeflow_id': new ObjectId(chargeflowId) }, page, pageSize, session);

        return MongoShopMapper.fromMongoShop(result.items[0]) || null;
    }

    async findOneByShopName(
        shopName: string, session?: ClientSession,
    ): Promise<WithId<IShop> | null> {

        const page = 1;
        const pageSize = 1;
        const result = await this.findByQueryOperation
            .find({ $or: [ { 'name': shopName }, { 'platform_name': shopName } ] }, page, pageSize, session);

        return MongoShopMapper.fromMongoShop(result.items[0]) || null;
    }

    async findByRegistrationType(
        registrationType: string, page?: number, pageSize?: number, session?: ClientSession,
    ): Promise<IPaginatedFindByQueryResult<IShop>> {
        const result = await this.findByQueryOperation.find({ registrationType }, page, pageSize, session);

        return {
            items: MongoShopMapper.fromMongoShops(result.items),
            pagination: result.pagination,
        };
    }

    async update(
        id: string, shop: Partial<IShop>, session?: ClientSession,
    ): Promise<WithId<IShop> | null> {
        const result = await this.updateOperation.update({
            ...MongoShopMapper.toPartialMongoShop(shop),
            _id: new ObjectId(id),
        }, session);

        return MongoShopMapper.fromMongoShop(result) || null;
    }

    async addProcessorId(
        accountId: string, processorType: string, processorId: string, session?: ClientSession,
    ): Promise<WithId<IShop> | null> {
        const filter = { accountId: new ObjectId(accountId) };
        const updateFilter: UpdateFilter<IMongoShop> = [ {
            $set: {
                processors: {
                    $cond: {
                        if: {
                            $in: [ processorType, { $ifNull: [ '$processors.type', [] ] } ],
                        },
                        then: {
                            $map: {
                                input: { $ifNull: [ '$processors', [] ] },
                                as: 'processor',
                                in: {
                                    $cond: {
                                        if: { $eq: [ '$$processor.type', processorType ] },
                                        then: {
                                            $mergeObjects: [
                                                '$$processor',
                                                {
                                                    ids: { $setUnion: [ '$$processor.ids', [ processorId ] ] },
                                                },
                                            ],
                                        },
                                        else: '$$processor',
                                    },
                                },
                            },
                        },
                        else: {
                            $concatArrays: [
                                { $ifNull: [ '$processors', [] ] },
                                [ { type: processorType, ids: [ processorId ] } ],
                            ],
                        },
                    },
                },
            },
        } ];
        const result = await this.updateOperation.updateWithFilter(
            filter, updateFilter, session);
        return MongoShopMapper.fromMongoShop(result) || null;
    }

    async updateShopChargeflowStatus(chargeflowId: string, chargeflowStatus: 'active' | 'inactive', dateUpdated: Date, session?: ClientSession) {
        const filter = { chargeflow_id: new ObjectId(chargeflowId) };
        const updateFilter: UpdateFilter<IMongoShop> = [ {
            $set: {
                chargeflow_status: chargeflowStatus,
                date_updated: new Date(dateUpdated),
            },
        } ];
        const result = await this.updateOperation.updateWithFilter(
            filter, updateFilter, session);
        return MongoShopMapper.fromMongoShop(result) || null;
    }

    async updateKeyValidity(id: string, isKeyValid: boolean, session?: ClientSession): Promise<WithId<IShop> | null> {
        const result = await this.updateOperation.updateWithFilter({
            _id: new ObjectId(id),
        },
        {
            $set: {
                isKeyValid,
            },
        }, session);

        return MongoShopMapper.fromMongoShop(result) || null;
    }

    async getShopifyPaymentsShops(
        page?: number, pageSize?: number, startFromId?: string, session?: ClientSession,
    ): Promise<{ items: WithId<IShop>[], pagination?: IPaginationCalculatorResult }> {
        const filter: Filter<IMongoShop> = {
            chargeflow_status: ChargeflowStatus.Enabled,
            platform: 'shopify',
        };

        if (startFromId) {
            filter._id = { $gte: new ObjectId(startFromId) };
        }

        const result = await this.findByQueryOperation.find(filter, page, pageSize, session);

        return {
            items: MongoShopMapper.fromMongoShops(result.items),
            pagination: result.pagination,
        };
    }

    async getAllShopifyShops(
        page?: number, pageSize?: number, startFromId?: string, session?: ClientSession,
    ): Promise<{ items: WithId<IShop>[], pagination?: IPaginationCalculatorResult }> {
        const filter: Filter<IMongoShop> = {
            'connect.access_token': { $ne: '' },
            'platform': 'shopify',
        };

        if (startFromId) {
            filter._id = { $gte: new ObjectId(startFromId) };
        }

        const result = await this.findByQueryOperation.find(filter, page, pageSize, session);

        return {
            items: MongoShopMapper.fromMongoShops(result.items),
            pagination: result.pagination,
        };
    }

    async updateShopSuccessRates(successRate: number, filter: Filter<IMongoShop>): Promise<IBulkUpdateResponse | null> {
        const result = await this.updateOperation.bulkUpdate([
            {
                updateMany: {
                    filter,
                    update: { $set: { 'chargeflow_success_rate': successRate } },
                },
            },
        ]);

        return result;
    }

    async getAllShopsOfPlatform(
        chargeflowId: string,
        session?: ClientSession,
    ): Promise<{ items: WithId<IShop>[], pagination?: IPaginationCalculatorResult }> {
        if (!chargeflowId) {
            throw new Error('Missing chargeflowId');
        }

        const shop = await this.findOneByLegacyChargeflowId(chargeflowId, session);

        if (!shop) {
            throw new Error('Shop not found');
        }

        const aggregation: Document[] = [
            {
                $match: {
                    source: shop.platform_name,
                    chargeflow_id: { $ne: new ObjectId(chargeflowId) },
                },
            },
            {
                $sort: {
                    name: 1,
                },
            },
        ];

        const result = await this.findByQueryOperation.findWithAggregation<IMongoShop>(
            aggregation,
            1,
            50,
            session,
        );

        return {
            items: MongoShopMapper.fromMongoShops(result.items),
            pagination: result.pagination,
        };
    }

    public async findManyByChargeflowIDs(
        chargeflowIDs: string[],
        page = 1,
        pageSize = 10,
        session?: ClientSession,
    ) {
        const filter = {
            chargeflow_id: {
                $in: chargeflowIDs.map(id => new ObjectId(id)),
            },
        };

        const result = await this.findByQueryOperation.find(filter, page, pageSize, session);

        return {
            items: MongoShopMapper.fromMongoShops(result.items),
            pagination: result.pagination,
        };
    }
}
