import { ClientSession, Filter } from 'mongodb';
import { IMongoShop, IShop } from './IShop';
import { IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { WithId } from '../shared/helper-types';
import { IPaginationCalculatorResult } from '../shared/pagination/IPaginationCalculatorOptionsResult';
import { IBulkUpdateResponse } from '../Dispute/Types';

export interface IShopRepository {
    getById: (
        id: string, session?: ClientSession
    ) => Promise<WithId<IShop> | null>,

    findOneByLegacyChargeflowId: (
        chargeflowId: string, session?: ClientSession
    ) => Promise<WithId<IShop> | null>,

    findOneByShopName: (
        shopName: string, session?: ClientSession
    ) => Promise<WithId<IShop> | null>,

    findByRegistrationType: (
        registrationType: string, page?: number, pageSize?: number, session?: ClientSession,
    ) => Promise<IPaginatedFindByQueryResult<IShop>>,

    update: (
        id: string, shop: Partial<IShop>, session?: ClientSession
    ) => Promise<WithId<IShop> | null>,

    /**
     * Adds a processorId to the specified shop's processors array.
     * This implementation assumes the current state where it's possible to have one store under one chargeflow account.
     * In the future, it should be adjusted to additionally handle shopId.
     */
    addProcessorId: (accountId: string, type: string, processorId: string, session?: ClientSession
    ) => Promise<WithId<IShop> | null>

    updateShopChargeflowStatus: (chargeflowId: string, chargeflowStatus: 'active' | 'inactive', dateUpdated: Date, session?: ClientSession) => Promise<WithId<IShop> | null>

    // Add isKeyValid field to the shop until shops that use shopify payments are migrated to use processor object
    updateKeyValidity: (
        id: string, isKeyValid: boolean, session?: ClientSession,
    ) => Promise<WithId<IShop> | null>,

    getShopifyPaymentsShops: (
        page?: number, pageSize?: number, startFromId?: string, session?: ClientSession
    ) => Promise<{ items: WithId<IShop>[], pagination?: IPaginationCalculatorResult }>,

    getAllShopifyShops: (
        page?: number, pageSize?: number, startFromId?: string, session?: ClientSession
    ) => Promise<{ items: WithId<IShop>[], pagination?: IPaginationCalculatorResult }>,

    updateShopSuccessRates(successRate: number, filter: Filter<IMongoShop>): Promise<IBulkUpdateResponse | null>

    getAllShopsOfPlatform: (
        chargeflowId: string,
        session?: ClientSession,
    ) => Promise<{ items: WithId<IShop>[] }>

    findManyByChargeflowIDs: (
        chargeflowIDs: string[],
        page?: number,
        pageSize?: number,
        session?: ClientSession,
    ) => Promise<IPaginatedFindByQueryResult<IShop>>
}
