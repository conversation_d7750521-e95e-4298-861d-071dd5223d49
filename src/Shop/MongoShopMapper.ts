
import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoShop, IShop } from './IShop';

export class MongoShopMapper {
    static toMongoShop(shop: IShop): IMongoShop {
        if (!shop) {
            throw new Error('Dispute is required for mapping');
        }

        return {
            ...shop,
            _id: shop._id ? new ObjectId(shop._id) : undefined,
            accountId: shop.accountId ? new ObjectId(shop.accountId) : undefined,
            chargeflow_id: shop.chargeflow_id ? new ObjectId(shop.chargeflow_id) : undefined,
            customer_id: shop.customer_id ? new ObjectId(shop.customer_id) : undefined,

        };
    }
    static toPartialMongoShop(shop: Partial<IShop>): Partial<IMongoShop> {
        if (!shop) {
            throw new Error('Dispute is required for mapping');
        }

        return {
            ...shop,
            _id: shop._id ? new ObjectId(shop._id) : undefined,
            accountId: shop.accountId ? new ObjectId(shop.accountId) : undefined,
            chargeflow_id: shop.chargeflow_id ? new ObjectId(shop.chargeflow_id) : undefined,
            customer_id: shop.customer_id ? new ObjectId(shop.customer_id) : undefined,
        };
    }

    static toMongoShops(shops: IShop[]): IMongoShop[] {
        const mongoShops: IMongoShop[] = [];

        for (let i = 0; i < shops.length; i++) {
            const mongoShop = MongoShopMapper.toMongoShop(shops[i]);
            mongoShops.push(mongoShop);
        }

        return mongoShops;
    }

    static fromMongoShop(mongoShop: MongoWithId<IMongoShop>): WithId<IShop> {
        if (!mongoShop) {
            throw new Error('MongoShop is required for mapping');
        }

        return {
            ...mongoShop,
            _id: mongoShop._id.toHexString(),
            accountId: mongoShop.accountId?.toHexString(),
            chargeflow_id: mongoShop.chargeflow_id?.toHexString(),
            customer_id: mongoShop.customer_id?.toHexString(),
        };
    }

    static fromMongoShops(mongoShops: MongoWithId<IMongoShop>[]): WithId<IShop>[] {
        const shops: WithId<IShop>[] = [];

        for (let i = 0; i < mongoShops.length; i++) {
            const shop = MongoShopMapper.fromMongoShop(mongoShops[i]);
            shops.push(shop);
        }

        return shops;
    }

}
