import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { ShopRepositoryFactory } from './ShopRepositoryFactory';
import { MongoShopRepository } from './MongoShopRepository';

describe(ShopRepositoryFactory.name, () => {
    describe(ShopRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = ShopRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoShopRepository);
        });
    });
});
