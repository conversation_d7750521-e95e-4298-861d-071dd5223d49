import { Document, ObjectId } from 'mongodb';
import { IAuditableFields } from '../shared/helper-types';

export interface IAccountMapping {
    chargeflowId: string,
    accountId: string,
    email: string;
    dateCreated: Date;
    dateUpdated: Date;
    adminName: string;
    name: string;
    onboardingStatus: 'active' | 'pending' | 'inactive';
    role: 'admin' | 'member'
}

export interface IMongoAccountMapping extends IAuditableFields, Document {
    _id: ObjectId,
    chargeflowId: ObjectId,
    accountId: ObjectId,
}

export interface IAccountMappingForInsert {
    chargeflowId: ObjectId,
    accountId: ObjectId,
    email: string;
    dateCreated: Date;
    dateUpdated: Date;
    adminName: string;
    name: string;
    onboardingStatus: 'active' | 'pending' | 'inactive';
    role: 'admin' | 'member'
}

export interface IAccountMappingWithAggregatedShop extends IAccountMapping {
    shopName: string,
}
