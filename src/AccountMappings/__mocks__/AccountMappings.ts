import { ObjectId } from 'mongodb';
import { IAccountMapping, IMongoAccountMapping } from '../IAccountMapping';

const getMongoAccountMapping = (overrides?: Partial<IMongoAccountMapping>): IMongoAccountMapping => ({
    _id: new ObjectId(),
    chargeflowId: new ObjectId('66187980cacc5e60aacf4130'),
    accountId: new ObjectId('66187980cacc5e60aacf4131'),
    email: '<EMAIL>',
    dateCreated: new Date(),
    dateUpdated: new Date(),
    ...overrides,
});

const getAccountMapping = (overrides?: Partial<IAccountMapping>): IAccountMapping => ({
    chargeflowId: '66187980cacc5e60aacf4130',
    accountId: '66187980cacc5e60aacf4131',
    email: '<EMAIL>',
    dateCreated: new Date(),
    dateUpdated: new Date(),
    adminName: 'admin',
    name: 'test',
    onboardingStatus: 'pending',
    role: 'member',
    ...overrides,
});

export default {
    getMongoAccountMapping,
    getAccountMapping,
};
