import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IAccountMappingForInsert, IMongoAccountMapping } from './IAccountMapping';
import { MongoAccountMappingsRepository } from './MongoAccountMappingsRepository';
import { IAccountMappingsRepository } from './IAccountMappingsRepository';
import { MongoBulkAddOperation } from '../shared/operation/BulkAddOperation/MongoBulkAddOperation';

export class AccountMappingsRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IAccountMappingsRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoAccountMapping>(mongoClient, dbName, collectionName);
        const bulkAddOperation = new MongoBulkAddOperation<IAccountMappingForInsert>(mongoClient, dbName, collectionName);
        return new MongoAccountMappingsRepository(findByQueryOperation, bulkAddOperation);
    }
}
