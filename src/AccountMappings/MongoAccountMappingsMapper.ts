import { WithId as MongoWithId, ObjectId } from 'mongodb';
import { IAccountMapping, IAccountMappingForInsert, IMongoAccountMapping } from './IAccountMapping';

export class MongoAccountMappingsMapper {

    static fromMongoAccountMapping(accountMapping: MongoWithId<IMongoAccountMapping>): IAccountMapping {
        if (!accountMapping) {
            throw new Error('AccountMapping is required for mapping');
        }
        return {
            accountId: accountMapping.accountId.toHexString(),
            chargeflowId: accountMapping.chargeflowId.toHexString(),
            email: accountMapping.email,
            dateCreated: accountMapping.dateCreated,
            dateUpdated: accountMapping.dateUpdated ?? new Date(),
            adminName: accountMapping.adminName,
            name: accountMapping.name,
            onboardingStatus: accountMapping.onboardingStatus,
            role: accountMapping.role,
        };
    }

    static toMongoAccountMapping(accountMapping: IAccountMapping): IAccountMappingForInsert {
        return {
            accountId: new ObjectId(accountMapping.accountId),
            chargeflowId: new ObjectId(accountMapping.chargeflowId),
            email: accountMapping.email,
            dateCreated: accountMapping.dateCreated,
            dateUpdated: accountMapping.dateUpdated,
            adminName: accountMapping.adminName,
            name: accountMapping.name,
            onboardingStatus: accountMapping.onboardingStatus,
            role: accountMapping.role,
        };
    }
}
