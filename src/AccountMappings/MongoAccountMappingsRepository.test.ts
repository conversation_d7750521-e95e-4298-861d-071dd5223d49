import { MongoAccountMappingsRepository } from './MongoAccountMappingsRepository';
import { IAccountMappingsRepository } from './IAccountMappingsRepository';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient, ObjectId } from 'mongodb';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { AccountMappingsRepositoryFactory } from './AccountMappingsRepositoryFactory';
import { AccountMappingsCollectionName } from './constants';

import accountMappings from './__mocks__/AccountMappings';
import { IAccountMapping, IMongoAccountMapping } from './IAccountMapping';
import Settings from '../Settings/__mocks__/Settings';
import { IMongoSettings } from '../Settings/ISettings';
import { SettingsCollectionName } from '../Settings/constants';

describe(MongoAccountMappingsRepository.name, () => {
    let repository: IAccountMappingsRepository;
    let mongoServer: MongoMemoryServer;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repository = AccountMappingsRepositoryFactory.create(
            mongoClient,
            ChargeflowDbName,
            AccountMappingsCollectionName,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName).collection<IMongoAccountMapping>(AccountMappingsCollectionName).deleteMany({});
        await mongoClient.db(ChargeflowDbName).collection<IMongoSettings>(SettingsCollectionName).deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    it('should get account mappings filtered by email', async () => {
        // GIVEN
        const notMatchingAccountMapping = accountMappings.getMongoAccountMapping();
        const matchingAccountMapping = accountMappings.getMongoAccountMapping({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
            adminName: 'Admin Name',
            dateCreated: new Date(),
            dateUpdated: new Date(),
            name: 'Test Name',
            onboardingStatus: 'active',
            role: 'admin',
        });
        const expectedAccountMapping = {
            chargeflowId: '65b0d8a736972cac7463cc50',
            accountId: '65b0d8a736972cac7463cc51',
            adminName: 'Admin Name',
            dateCreated: new Date(),
            dateUpdated: new Date(),
            email: '<EMAIL>',
            name: 'Test Name',
            onboardingStatus: 'active',
            role: 'admin',
        };
        const expectedAccountMappings =[ expectedAccountMapping ];
        await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoAccountMapping>(AccountMappingsCollectionName)
            .insertMany([ matchingAccountMapping, notMatchingAccountMapping ]);
        const email = '<EMAIL>';

        // WHEN
        const result = await repository.findAllByEmail(email);

        // THEN
        expect(result).toHaveLength(1);
        expect(result).toEqual(expectedAccountMappings);
    });

    it('should get account mappings with shop names filtered by email', async () => {
        // GIVEN
        const notMatchingAccountMapping = accountMappings.getMongoAccountMapping();
        const matchingAccountMapping = accountMappings.getMongoAccountMapping({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
        });
        await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoAccountMapping>(AccountMappingsCollectionName)
            .insertMany([ matchingAccountMapping, notMatchingAccountMapping ]);

        const setting = Settings.getMongoSettings({
            chargeflow_id: new ObjectId('65b0d8a736972cac7463cc50'),
        });
        await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoSettings>(SettingsCollectionName)
            .insertOne(setting);

        const expectedAccountMapping = {
            chargeflowId: '65b0d8a736972cac7463cc50',
            accountId: '65b0d8a736972cac7463cc51',
            shopName: 'Test shop name',
        };
        const expectedAccountMappings =[ expectedAccountMapping ];

        const email = '<EMAIL>';

        // WHEN
        const result = await repository.findAllWithShopNameByEmail(email);

        // THEN
        expect(result).toHaveLength(1);
        expect(result).toEqual(expectedAccountMappings);
    });

    it('should get account mappings sorted by shop name', async () => {
        // GIVEN
        const accountMapping = accountMappings.getMongoAccountMapping({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
        });
        await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoAccountMapping>(AccountMappingsCollectionName)
            .insertOne(accountMapping);

        const settingA = Settings.getMongoSettings({
            chargeflow_id: new ObjectId('65b0d8a736972cac7463cc50'),
            shop: {
                ...Settings.getMongoSettings().shop,
                shop_name: 'A',
            },
        });
        const settingB = Settings.getMongoSettings({
            chargeflow_id: new ObjectId('65b0d8a736972cac7463cc50'),
            shop: {
                ...Settings.getMongoSettings().shop,
                shop_name: 'B',
            },
        });
        const settingC = Settings.getMongoSettings({
            chargeflow_id: new ObjectId('65b0d8a736972cac7463cc50'),
            shop: {
                ...Settings.getMongoSettings().shop,
                shop_name: 'C',
            },
        });
        await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoSettings>(SettingsCollectionName)
            .insertMany([ settingB, settingC, settingA ]);

        const email = '<EMAIL>';

        // WHEN
        const result = await repository.findAllWithShopNameByEmail(email);

        // THEN
        expect(result).toHaveLength(3);
        expect(result.map(mapping => mapping.shopName)).toEqual([ 'A', 'B', 'C' ]);
    });

    it('should insert mapped values', async () => {
        // GIVEN
        const accountMapping = accountMappings.getMongoAccountMapping({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
            adminName: 'Admin Name',
            dateCreated: new Date(),
            dateUpdated: new Date(),
            name: 'Test Name',
            onboardingStatus: 'active',
            role: 'admin',
        });

        // WHEN
        const result = await repository.insertAccountMapping(accountMapping as unknown as IAccountMapping, undefined);

        // THEN
        expect(result).toHaveLength(1);
        expect(result[0]).toMatchObject({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
            adminName: 'Admin Name',
            dateCreated: new Date(),
            dateUpdated: new Date(),
            name: 'Test Name',
            onboardingStatus: 'active',
            role: 'admin',
        });

        const insertedAccountMapping = await mongoClient
            .db(ChargeflowDbName)
            .collection<IMongoAccountMapping>(AccountMappingsCollectionName)
            .findOne({ email: '<EMAIL>' });

        expect(insertedAccountMapping).toMatchObject({
            chargeflowId: new ObjectId('65b0d8a736972cac7463cc50'),
            accountId: new ObjectId('65b0d8a736972cac7463cc51'),
            email: '<EMAIL>',
            adminName: 'Admin Name',
            dateCreated: new Date(),
            dateUpdated: new Date(),
            name: 'Test Name',
            onboardingStatus: 'active',
            role: 'admin',
        });
    });
});
