import { IAccountMappingsRepository } from './IAccountMappingsRepository';
import {
    IAccountMapping,
    IAccountMappingForInsert,
    IAccountMappingWithAggregatedShop,
    IMongoAccountMapping,
} from './IAccountMapping';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, Document, WithId } from 'mongodb';
import { MongoAccountMappingsMapper } from './MongoAccountMappingsMapper';
import { IBulkAddOperation } from '../shared/operation/BulkAddOperation/IBulkAddOperation';

export class MongoAccountMappingsRepository implements IAccountMappingsRepository {

    private readonly page = 1;
    private readonly pageSize = 100;

    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoAccountMapping>,
        private readonly bulkAddOperation: IBulkAddOperation<IAccountMappingForInsert>,
    ) {}

    async findAllByEmail(email: string, session: ClientSession | undefined): Promise<IAccountMapping[]> {
        const result = await this.findByQueryOperation.find({ email }, this.page, this.pageSize, session);
        return result.items.map(MongoAccountMappingsMapper.fromMongoAccountMapping);
    }

    async findAllWithShopNameByEmail(email: string, session?: ClientSession | undefined): Promise<IAccountMappingWithAggregatedShop[]> {
        const aggregation: Document[] = [
            {
                $lookup: {
                    from: 'settings',
                    localField: 'chargeflowId',
                    foreignField: 'chargeflow_id',
                    as: 'setting',
                },
            },
            {
                $match: {
                    email,
                },
            },
            {
                $unwind: '$setting',
            },
            {
                $project: {
                    _id: 0,
                    accountId: { $toString: '$accountId' },
                    chargeflowId: { $toString: '$chargeflowId' },
                    shopName: '$setting.shop.shop_name',
                },
            },
            {
                $sort: {
                    shopName: 1,
                },
            },
        ];
        const result = await this.findByQueryOperation.findWithAggregation<IAccountMappingWithAggregatedShop>(aggregation, this.page, this.pageSize, session);
        return result.items;
    }

    async insertAccountMapping(accountMapping: IAccountMapping | IAccountMapping[], session: ClientSession | undefined): Promise<WithId<IMongoAccountMapping>[]> {
        const accountMappings = Array.isArray(accountMapping) ? accountMapping : [ accountMapping ];
        return this.bulkAddOperation.bulkAdd(accountMappings.map(MongoAccountMappingsMapper.toMongoAccountMapping), session);
    }
}
