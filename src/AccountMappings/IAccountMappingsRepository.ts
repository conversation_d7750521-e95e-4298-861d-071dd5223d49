import { ClientSession, WithId } from 'mongodb';
import { IAccountMapping, IAccountMappingWithAggregatedShop, IMongoAccountMapping } from './IAccountMapping';

export interface IAccountMappingsRepository {

    findAllByEmail: (
        email: string,
        session?: ClientSession
    ) => Promise<IAccountMapping[]>;

    findAllWithShopNameByEmail: (
        email: string,
        session?: ClientSession
    ) => Promise<IAccountMappingWithAggregatedShop[]>;

    insertAccountMapping: (
        accountMapping: IAccountMapping | IAccountMapping[],
        session?: ClientSession
    ) => Promise<WithId<IMongoAccountMapping>[]>;
}
