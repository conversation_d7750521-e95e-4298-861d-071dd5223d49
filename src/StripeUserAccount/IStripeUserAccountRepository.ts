import { ClientSession } from 'mongodb';
import { IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IStripeUserAccount } from './IStripeUserAccount';

export interface IStripeUserAccountRepository {
    findWithLegacyChargeflowIdSet: (
        page?: number, pageSize?: number, session?: ClientSession
    ) => Promise<IPaginatedFindByQueryResult<IStripeUserAccount>>

    findById: (
        stripeUserAccountDbId: string, session?: ClientSession
    ) => Promise<IStripeUserAccount | null>;
}
