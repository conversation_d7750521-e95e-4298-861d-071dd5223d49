import { ObjectId } from 'mongodb';
import { IStripeUserAccount } from '../IStripeUserAccount';
import { WithId } from '../../shared/helper-types';
import { faker } from '@faker-js/faker';

const getStripeUserAccount = (overrides: Partial<IStripeUserAccount> = {}): WithId<IStripeUserAccount> => ({
    _id: new ObjectId().toHexString(),
    business: {
        url: 'https://test-business.com',
    },
    chargeflowId: new ObjectId().toHexString(),
    accountId: new ObjectId().toHexString(),
    statementDescriptor: faker.company.name(),
    stripeDashboardDisplayName: faker.company.buzzNoun(),
    accountType: faker.word.noun(),
    email: faker.internet.email(),
    ...overrides,
});

export default {
    getStripeUserAccount,
};
