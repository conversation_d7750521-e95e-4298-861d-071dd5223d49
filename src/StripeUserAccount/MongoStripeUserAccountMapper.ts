
import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { WithId } from '../shared/helper-types';
import { IMongoStripeUserAccount, IStripeUserAccount } from './IStripeUserAccount';

export class MongoStripeUserAccountMapper {
    static toMongoStripeUserAccount(stripeUserAccount: IStripeUserAccount): IMongoStripeUserAccount {
        if (!stripeUserAccount) {
            throw new Error('Dispute is required for mapping');
        }

        return {
            ...stripeUserAccount,
            _id: stripeUserAccount._id ? new ObjectId(stripeUserAccount._id) : undefined,
            chargeflowId: stripeUserAccount.chargeflowId ? new ObjectId(stripeUserAccount.chargeflowId): null,
            accountId: stripeUserAccount.accountId ? new ObjectId(stripeUserAccount.accountId): null,
        };
    }
    static toPartialMongoStripeUserAccount(stripeUserAccount: Partial<IStripeUserAccount>): Partial<IMongoStripeUserAccount> {
        if (!stripeUserAccount) {
            throw new Error('Dispute is required for mapping');
        }

        return {
            ...stripeUserAccount,
            _id: stripeUserAccount._id ? new ObjectId(stripeUserAccount._id) : undefined,
            chargeflowId: stripeUserAccount.chargeflowId ? new ObjectId(stripeUserAccount.chargeflowId) : null,
            accountId: stripeUserAccount.accountId ? new ObjectId(stripeUserAccount.accountId) : null,
        };
    }

    static toMongoStripeUserAccounts(stripeUserAccount: IStripeUserAccount[]): IMongoStripeUserAccount[] {
        const mongoDisputes: IMongoStripeUserAccount[] = [];

        for (let i = 0; i < stripeUserAccount.length; i++) {
            const mongoDispute = MongoStripeUserAccountMapper.toMongoStripeUserAccount(stripeUserAccount[i]);
            mongoDisputes.push(mongoDispute);
        }

        return mongoDisputes;
    }

    static fromMongoStripeUserAccount(stripeUserAccount: MongoWithId<IMongoStripeUserAccount>): WithId<IStripeUserAccount> {
        if (!stripeUserAccount) {
            throw new Error('StripeUserAccount is required for mapping');
        }

        return {
            ...stripeUserAccount,
            _id: stripeUserAccount._id.toString(),
            chargeflowId: stripeUserAccount.chargeflowId?.toString(),
            accountId: stripeUserAccount.accountId?.toString(),
        };
    }

    static fromMongoStripeUserAccounts(disputes: MongoWithId<IMongoStripeUserAccount>[]): WithId<IStripeUserAccount>[] {
        const udoDisputes: WithId<IStripeUserAccount>[] = [];

        for (let i = 0; i < disputes.length; i++) {
            const udoDispute = MongoStripeUserAccountMapper.fromMongoStripeUserAccount(disputes[i]);
            udoDisputes.push(udoDispute);
        }

        return udoDisputes;
    }

}
