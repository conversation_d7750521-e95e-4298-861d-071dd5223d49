import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IStripeUserAccountRepository } from './IStripeUserAccountRepository';
import { IMongoStripeUserAccount } from './IStripeUserAccount';
import { MongoStripeUserAccountRepository } from './MongoStripeUserAccountRepository';

export class StripeUserAccountRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IStripeUserAccountRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoStripeUserAccount>(mongoClient, dbName, collectionName);

        return new MongoStripeUserAccountRepository(
            findByQueryOperation,
        );
    }
}
