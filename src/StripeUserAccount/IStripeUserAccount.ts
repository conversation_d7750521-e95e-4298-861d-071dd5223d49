// TODO: Define full interface for IStripeUserAccount

import { ObjectId, Document } from 'mongodb';

interface IBusiness {
    url?: string | null,
}

export interface IStripeUserAccountBase {
    business: IBusiness,
    statementDescriptor: string,
    stripeDashboardDisplayName: string,
    accountType: string,
    email: string,
}

export interface IStripeUserAccount extends IStripeUserAccountBase {
    _id?: string,
    chargeflowId?: string | null,
    accountId?: string | null,
}

export interface IMongoStripeUserAccount extends IStripeUserAccountBase, Document {
    _id?: ObjectId,
    chargeflowId?: ObjectId | null,
    accountId?: ObjectId | null,
}
