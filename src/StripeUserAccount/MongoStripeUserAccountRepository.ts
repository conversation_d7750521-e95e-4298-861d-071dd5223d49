import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IMongoStripeUserAccount, IStripeUserAccount } from './IStripeUserAccount';
import { IStripeUserAccountRepository } from './IStripeUserAccountRepository';
import { MongoStripeUserAccountMapper } from './MongoStripeUserAccountMapper';

export class MongoStripeUserAccountRepository implements IStripeUserAccountRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoStripeUserAccount>,
    ) {}

    async findWithLegacyChargeflowIdSet(
        page?: number | undefined, pageSize?: number | undefined, session?: ClientSession | undefined,
    ): Promise<IPaginatedFindByQueryResult<IStripeUserAccount>> {
        const result = await this.findByQueryOperation.find({ chargeflowId: { $not: { $eq: null } } }, page, pageSize, session);
        const resultItems = MongoStripeUserAccountMapper.fromMongoStripeUserAccounts(result.items);
        return { items: resultItems, pagination: result.pagination };
    }

    async findById(stripeUserAccountDbId: string, session?: ClientSession): Promise<IStripeUserAccount | null> {
        const result = await this.findByQueryOperation.findOne(
            { _id: new ObjectId(stripeUserAccountDbId) },
            session,
        );
        if (result) {
            return MongoStripeUserAccountMapper.fromMongoStripeUserAccount(result);
        }
        return null;
    }
}
