import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { StripeUserAccountRepositoryFactory } from './StripeUserAccountRepositoryFactory';
import { MongoStripeUserAccountRepository } from './MongoStripeUserAccountRepository';

describe(StripeUserAccountRepositoryFactory.name, () => {
    describe(StripeUserAccountRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = StripeUserAccountRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoStripeUserAccountRepository);
        });
    });
});
