import { mock } from 'jest-mock-extended';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, ObjectId } from 'mongodb';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';
import { MongoStripeUserAccountRepository } from './MongoStripeUserAccountRepository';
import stripeUserAccountMocks from './__mocks__/StripeUserAccount';
import { IMongoStripeUserAccount } from './IStripeUserAccount';
import { MongoStripeUserAccountMapper } from './MongoStripeUserAccountMapper';

describe(MongoStripeUserAccountRepository.name, () => {
    let mongoStripeUserAccountRepository: MongoStripeUserAccountRepository;
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoStripeUserAccount>>();

    beforeEach(() => {
        mongoStripeUserAccountRepository = new MongoStripeUserAccountRepository(
            findByQueryOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoStripeUserAccountRepository.prototype.findWithLegacyChargeflowIdSet.name, () => {
        it('calls find by query operation', async () => {
            expect.assertions(3);

            // GIVEN
            const page = 1;
            const pageSize = 1;
            const stripeUserAccount = stripeUserAccountMocks.getStripeUserAccount();
            const session = mock<ClientSession>();

            const expectedResult = {
                items: [ {
                    ...MongoStripeUserAccountMapper.toMongoStripeUserAccount(stripeUserAccount),
                    _id: new ObjectId(stripeUserAccount._id),
                },
                ],
                pagination: paginationMocks.getPaginationResult(),
            };

            findByQueryOperation.find.mockResolvedValue(expectedResult);

            // WHEN
            const result = await mongoStripeUserAccountRepository
                .findWithLegacyChargeflowIdSet(page, pageSize, session);

            // THEN
            expect(result).toEqual({
                items: MongoStripeUserAccountMapper.fromMongoStripeUserAccounts(expectedResult.items),
                pagination: expectedResult.pagination,
            });

            expect(findByQueryOperation.find).toHaveBeenCalledTimes(1);
            expect(findByQueryOperation.find)
                .toHaveBeenCalledWith({ chargeflowId: { $not: { $eq: null } } }, page, pageSize, session);
        });
    });
});
