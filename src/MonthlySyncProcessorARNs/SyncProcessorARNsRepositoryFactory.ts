import { MongoClient } from 'mongodb';
import { MongoSyncProcessorARNsRepository } from './MongoSyncProcessorARNsRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { ProcessorsAwaitingARNsSyncRecord } from './Types';

export class SyncProcessorARNsRepositoryFactory {
    static create(
        mongoClient: MongoClient,
        dbName: string,
        collectionName: string,
    ) {
        const collection = mongoClient.db(dbName).collection<ProcessorsAwaitingARNsSyncRecord>(collectionName);
        const updateOperation = new MongoUpdateOperation<ProcessorsAwaitingARNsSyncRecord>(mongoClient, dbName, collectionName);

        return new MongoSyncProcessorARNsRepository(
            collection,
            updateOperation,
        );

    }
}
