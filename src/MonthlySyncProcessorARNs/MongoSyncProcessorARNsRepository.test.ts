import { Collection } from 'mongodb';
import { mock } from 'jest-mock-extended';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { MongoSyncProcessorARNsRepository } from './MongoSyncProcessorARNsRepository';
import { ProcessorsAwaitingARNsSyncRecord, ProcessorSyncStatus } from './Types';

describe('MongoSyncProcessorARNsRepository', () => {
    const mockCollection = mock<Collection<ProcessorsAwaitingARNsSyncRecord>>();
    const mockUpdateOperation = mock<IUpdateOperation<ProcessorsAwaitingARNsSyncRecord>>();
    const repository = new MongoSyncProcessorARNsRepository(mockCollection, mockUpdateOperation);
    const mockDate = new Date('2025-02-16T15:06:00.000Z');

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers().setSystemTime(mockDate);
    });

    describe('bulkInsert', () => {
        it('should perform bulk insert with correct operations', async () => {
            const records = [
                { processorId: '1', status: ProcessorSyncStatus.Pending },
                { processorId: '2', status: ProcessorSyncStatus.Pending },
            ] as ProcessorsAwaitingARNsSyncRecord[];

            await repository.bulkInsert(records);

            expect(mockCollection.bulkWrite).toHaveBeenCalledWith([
                { insertOne: { document: records[0] } },
                { insertOne: { document: records[1] } },
            ]);
        });
    });

    describe('fetchAwaitingRecordsAndLock', () => {
        it('should find records with correct query and limit', async () => {
            const mockRecords = [
                { processorId: '1', status: ProcessorSyncStatus.Pending },
            ] as ProcessorsAwaitingARNsSyncRecord[];

            const mockCursor = {
                limit: jest.fn().mockReturnThis(),
                toArray: jest.fn().mockResolvedValue(mockRecords),
            };

            // @ts-expect-error; mock object
            mockCollection.find.mockReturnValue(mockCursor);

            const result = await repository.fetchAwaitingRecordsAndLock(10, 5000);

            expect(mockCollection.find).toHaveBeenCalledWith({
                status: {
                    $in: [ ProcessorSyncStatus.Pending, ProcessorSyncStatus.Failed ],
                },
                attempts: { $lt: 3 },
                $or: [
                    { lockedAt: { $exists: false } },
                    { lockedAt: { $lt: new Date(mockDate.getTime() - 5000) } },
                ],
            });

            expect(mockCursor.limit).toHaveBeenCalledWith(10);
            expect(mockCursor.toArray).toHaveBeenCalled();
            expect(result).toEqual(mockRecords);
        });
    });

    describe('markManyAsProcessing', () => {
        it('should perform bulk update with correct parameters', async () => {
            const processorIDs = [ '1', '2' ];

            await repository.markManyAsProcessing(processorIDs);

            expect(mockUpdateOperation.bulkUpdate).toHaveBeenCalledWith([ {
                updateMany: {
                    filter: { processorId: { $in: processorIDs } },
                    update: {
                        $set: {
                            status: ProcessorSyncStatus.Processing,
                            lockedAt: mockDate,
                        },
                        $inc: { attempts: 1 },
                    },
                },
            } ]);
        });
    });

    describe('markManyAsFailed', () => {
        it('should perform bulk update with correct parameters', async () => {
            const processorIDs = [ '1', '2' ];

            await repository.markManyAsFailed(processorIDs, 'Error reason');

            expect(mockUpdateOperation.bulkUpdate).toHaveBeenCalledWith([ {
                updateMany: {
                    filter: { processorId: { $in: processorIDs } },
                    update: {
                        $set: {
                            status: ProcessorSyncStatus.Failed,
                            lockedAt: null,
                            errorReason: 'Error reason',
                        },
                    },
                },
            } ]);
        });
    });

    describe('markAsFailed', () => {
        it('should update record with failure status', async () => {
            const processorId = '123';
            const errorReason = 'Test error';

            await repository.markAsFailed(processorId, errorReason);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { processorId },
                {
                    $set: {
                        status: ProcessorSyncStatus.Failed,
                        errorReason,
                        lockedAt: null,
                    },
                },
            );
        });
    });

    describe('deleteByProcessorId', () => {
        it('should delete record with specified processorId', async () => {
            const processorId = '123';

            await repository.deleteByProcessorId(processorId);

            expect(mockCollection.deleteMany).toHaveBeenCalledWith({
                processorId,
            });
        });
    });

    describe('cleanUpOldRecords', () => {
        it('should delete all records', async () => {
            await repository.cleanUpOldRecords();

            expect(mockCollection.deleteMany).toHaveBeenCalledWith({});
        });
    });
});
