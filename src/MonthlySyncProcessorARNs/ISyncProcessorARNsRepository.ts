import { ProcessorsAwaitingARNsSyncRecord } from './Types';

export interface ISyncProcessorARNsRepository {
    bulkInsert(records: ProcessorsAwaitingARNsSyncRecord[]): Promise<void>;

    fetchAwaitingRecordsAndLock(
        batchSize: number,
        lockTimeoutInMs: number
    ): Promise<ProcessorsAwaitingARNsSyncRecord[]>;

    markManyAsProcessing(
        processorIDs: string[]
    ): Promise<void>;

    markManyAsFailed(
        processorIDs: string[],
        errorReason: string
    ): Promise<void>;

    fetchRecordsReadyForProcessing(batchSize: number): Promise<ProcessorsAwaitingARNsSyncRecord[]>;

    deleteByProcessorId(processorId: string): Promise<void>;

    markAsFailed(processorId: string, errorReason: string): Promise<void>;

    cleanUpOldRecords(): Promise<void>;
}
