import { Collection } from 'mongodb';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { ISyncProcessorARNsRepository } from './ISyncProcessorARNsRepository';
import { ProcessorsAwaitingARNsSyncRecord, ProcessorSyncStatus } from './Types';

export class MongoSyncProcessorARNsRepository implements ISyncProcessorARNsRepository {
    constructor(
        private readonly collection: Collection<ProcessorsAwaitingARNsSyncRecord>,
        private readonly updateOperation: IUpdateOperation<ProcessorsAwaitingARNsSyncRecord>,
    ) {}

    public async bulkInsert(records: ProcessorsAwaitingARNsSyncRecord[]): Promise<void> {
        await this.collection.bulkWrite(records.map(record => ({
            insertOne: {
                document: record,
            },
        })));
    }

    public async fetchAwaitingRecordsAndLock(batchSize: number, lockTimeoutInMs: number): Promise<ProcessorsAwaitingARNsSyncRecord[]> {
        const processorsToSync = await  this.collection.find({
            status: {
                $in: [ ProcessorSyncStatus.Pending, ProcessorSyncStatus.Failed ],
            },
            attempts: {
                $lt: 3,
            },
            $or: [
                {
                    lockedAt: { $exists: false },
                },
                {
                    lockedAt: {
                        $lt: new Date(Date.now() - lockTimeoutInMs),
                    },
                },
            ],
        }).limit(batchSize).toArray();

        this.markManyAsProcessing(processorsToSync.map(record => record.processorId));

        return processorsToSync;
    }

    public async fetchRecordsReadyForProcessing(batchSize: number): Promise<ProcessorsAwaitingARNsSyncRecord[]> {
        return this.collection.find({
            status: ProcessorSyncStatus.Processing,
        }).limit(batchSize).toArray();
    }

    public async markManyAsProcessing(processorIDs: string[]): Promise<void> {
        await this.updateOperation.bulkUpdate([
            {
                updateMany: {
                    filter: {
                        processorId: {
                            $in: processorIDs,
                        },
                    },
                    update: {
                        $set: {
                            status: ProcessorSyncStatus.Processing,
                            lockedAt: new Date(),
                        },
                        $inc: {
                            attempts: 1,
                        },
                    },
                },
            },
        ]);
    }

    public async markManyAsFailed(processorIDs: string[], errorReason: string): Promise<void> {
        await this.updateOperation.bulkUpdate([
            {
                updateMany: {
                    filter: {
                        processorId: {
                            $in: processorIDs,
                        },
                    },
                    update: {
                        $set: {
                            status: ProcessorSyncStatus.Failed,
                            errorReason,
                            lockedAt: null,
                        },
                    },
                },
            },
        ]);
    }

    public async markAsFailed(processorId: string, errorReason: string): Promise<void> {
        await this.updateOperation.updateWithFilter({
            processorId,
        }, {
            $set: {
                status: ProcessorSyncStatus.Failed,
                errorReason,
                lockedAt: null,
            },
        });
    }

    public async deleteByProcessorId(processorId: string): Promise<void> {
        await this.collection.deleteMany({
            processorId,
        });
    }

    public async cleanUpOldRecords(): Promise<void> {
        await this.collection.deleteMany({});
    }
}
