import { MongoClient } from 'mongodb';
import { SyncProcessorARNsRepositoryFactory } from './SyncProcessorARNsRepositoryFactory';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { MongoSyncProcessorARNsRepository } from './MongoSyncProcessorARNsRepository';

// Mock dependencies
jest.mock('../shared/operation/UpdateOperation/MongoUpdateOperation', () => ({
    MongoUpdateOperation: jest.fn().mockImplementation(() => ({
        bulkUpdate: jest.fn(),
        updateWithFilter: jest.fn(),
    })),
}));

jest.mock('./MongoSyncProcessorARNsRepository', () => ({
    MongoSyncProcessorARNsRepository: jest.fn().mockImplementation(() => ({
        fetchAwaitingRecords: jest.fn(),
        markManyAsProcessing: jest.fn(),
    })),
}));

describe('SyncProcessorARNsRepositoryFactory', () => {
    const mockMongoClient = new MongoClient('mongodb://localhost:27017');
    const testDbName = 'testDB';
    const testCollectionName = 'testCollection';

    describe('create', () => {
        it('should initialize MongoUpdateOperation with correct parameters', () => {
            SyncProcessorARNsRepositoryFactory.create(
                mockMongoClient,
                testDbName,
                testCollectionName,
            );

            expect(MongoUpdateOperation).toHaveBeenCalledWith(
                mockMongoClient,
                testDbName,
                testCollectionName,
            );
        });

        it('should return repository with initialized operations', () => {

            SyncProcessorARNsRepositoryFactory.create(mockMongoClient, testDbName, testCollectionName);

            expect(MongoSyncProcessorARNsRepository).toHaveBeenCalled();
        });
    });
});
