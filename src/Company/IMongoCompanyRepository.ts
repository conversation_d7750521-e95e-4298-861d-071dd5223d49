import { ClientSession, Filter } from 'mongodb';
import { ICompany, IMongoCompany } from './Types';
import { WithId } from '../shared/helper-types';

export interface IMongoCompanyRepository {
    findByChargeflowId(
        chargeflowId: string, clientSession?: ClientSession
    ): Promise<WithId<ICompany>|null>,
    findByAccountIdAndEmail(
        accountId: string, email: string, clientSession?: ClientSession
    ): Promise<WithId<ICompany>|null>,
    findByAccountId(
        accountId: string, clientSession?: ClientSession
    ): Promise<WithId<ICompany>|null>,
    savePaymentMethodId(
        chargeflowId: string, paymentMethodId: string, clientSession?: ClientSession,
    ): Promise<WithId<ICompany>|null>,
    saveCompanyDescription(
        chargeflowId: string, companyDescription: string, clientSession?: ClientSession,
    ): Promise<WithId<ICompany>|null>,
    findByCustomerId(
        customerId: string, clientSession?: ClientSession
    ): Promise<WithId<ICompany>|null>,
    findAllPaginated(
        filter: Filter<IMongoCompany>, page: number, limit: number, clientSession?: ClientSession
    ): Promise<WithId<ICompany>[]|null>,
}
