import { ObjectId, Document } from 'mongodb';

export type ICompanyStatus = 'registered' | 'active';
export type ICompanyOnboardingStage =
    'Step 1' | 'Step 2' | 'Step 3' | 'Integrations' |
    'Connect Integration' | 'Schedule Call' | 'Payment' | 'Completed';

export interface ICompanyUsersBase {
    dateCreated: Date,
    dateUpdated: Date,
    status: ICompanyStatus,
    onboardingStageV1: ICompanyOnboardingStage,
    onboardingStageDate: Date,
    email: string,
    firstName: string,
    lastName: string,
    contactInformation: {
        contactName: string,
        contactEmail: string,
    },
    businessInformation: {
        productsType?: string[],
        businessCountry?: string,
        businessName?: string,
        ownerNotes?: string,
        businessType?: string,
        businessUrl?: string,
    },
    billing: {
        source?: string,
        stripeCustomer?: {
            isSourceExpired?: boolean,
            customerPortalUrl?: string,
            id?: string,
            paymentMethodId?: string,
            dateCreated?: Date,
        }
        alertsSubscription?: {
            source?: string,
            isSourceExpired?: boolean,
            customerPortalUrl?: string,
            id?: string,
            paymentMethodId?: string,
            dateCreated?: Date,
        }
    },
    integrations: {
        platforms?: string[],
        processors?: string[],
        dataEnrichment?: string[],
    },
    companyDescription?: string,
    companyContactDetails?: string,
    promotional?: boolean,
    promotionCode?: string,
}

export interface ICompanyBase {
    cognitoUsername: string,
    users: ICompanyUsersBase,
}

export interface ICompanyUsers extends ICompanyUsersBase {
    chargeflowId?: string,
    accountId?: string,
}

export interface IMongoCompanyUsers extends ICompanyUsersBase {
    chargeflowId?: ObjectId,
    accountId?: ObjectId,
}

export interface ICompany extends ICompanyBase {
    _id?: string,
    users: ICompanyUsers,
}

export interface IMongoCompany extends ICompanyBase, Document {
    _id?: ObjectId,
    users: IMongoCompanyUsers
}
