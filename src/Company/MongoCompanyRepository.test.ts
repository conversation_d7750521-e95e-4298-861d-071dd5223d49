import { ObjectId } from 'mongodb';
import { IMongoCompany } from './Types';
import { MongoCompanyRepository } from './MongoCompanyRepository';
import mocks from './__mocks__/Company';
import findByQueryOperationMocks from '../shared/operation/FindByQueryOperation/__mocks__/MongoFindByQueryOperation';
import { MongoCompanyMapper } from './MongoCompanyMapper';
import { IFindByQueryOperation, IPaginatedFindByQueryResult } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import getUpdateOperation from '../shared/operation/UpdateOperation/__mocks__/MongoUpdateOperation';

describe(MongoCompanyRepository.name, () => {
    let mockQueryOperation: jest.Mocked<IFindByQueryOperation<IMongoCompany>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<IMongoCompany>>;
    let repository: MongoCompanyRepository;

    beforeEach(() => {
        mockQueryOperation = findByQueryOperationMocks.getFindByQueryOperation<IMongoCompany>();
        mockUpdateOperation = getUpdateOperation.getUpdateOperation<IMongoCompany>();
        repository = new MongoCompanyRepository(
            mockQueryOperation,
            mockUpdateOperation,
        );
    });

    describe('findByChargeflowId', () => {
        it('should return doc by chargeflowId', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const chargeflowId = doc.users.chargeflowId!;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByChargeflowId(chargeflowId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { 'users.chargeflowId': new ObjectId(chargeflowId) },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('findByAccountIdAndEmail', () => {
        it('should return doc by accountId and email', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const accountId = doc.users.accountId!;
            const email = doc.users.email!;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByAccountIdAndEmail(accountId, email);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { 'users.accountId': new ObjectId(accountId),
                    'users.email': email },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('findByAccountId', () => {
        it('should return doc by accountId', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const accountId = doc.users.accountId!;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByAccountId(accountId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                { 'users.accountId': new ObjectId(accountId) },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('savePaymentMethodId', () => {
        it('should return update response by chargeflowId', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const chargeflowId = doc.users.chargeflowId!;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockUpdateOperation.updateWithFilter.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.savePaymentMethodId(chargeflowId, 'paymentMethodId');

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { 'users.chargeflowId': new ObjectId(chargeflowId) },
                {
                    $set: {
                        'users.billing.stripeCustomer.paymentMethodId': 'paymentMethodId',
                    },
                },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('saveCompanyDescription', () => {
        it('should return update response by chargeflowId', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const chargeflowId = doc.users.chargeflowId!;
            const companyDescription = 'ACME is a company';

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockUpdateOperation.updateWithFilter.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.saveCompanyDescription(chargeflowId,companyDescription);

            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { 'users.chargeflowId': new ObjectId(chargeflowId) },
                {
                    $set: {
                        'users.companyDescription': companyDescription,
                    },
                },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('findByCustomerId', () => {
        it('should return doc by customerId in stripeCustomer', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const customerId = doc.users?.billing?.stripeCustomer?.id as string;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByCustomerId(customerId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                {   $or: [ { 'users.billing.stripeCustomer.id': customerId },
                    { 'users.billing.alertsSubscription.id': customerId } ] },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });

        it('should return doc by customerId in alertsSubscription', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const customerId = doc.users?.billing?.alertsSubscription?.id as string;

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.findOne.mockResolvedValue({ ...mongoDoc, _id: id });

            const result = await repository.findByCustomerId(customerId);

            expect(mockQueryOperation.findOne).toHaveBeenCalledWith(
                {   $or: [ { 'users.billing.stripeCustomer.id': customerId },
                    { 'users.billing.alertsSubscription.id': customerId } ] },
                undefined,
            );
            expect(result).toEqual({
                ...doc,
                _id: id.toHexString(),
            });
        });
    });

    describe('findAllPaginated', () => {
        it('should return doc by chargeflowId', async () => {
            const doc = mocks.getCompany();
            const id = new ObjectId();
            const filter = { 'users.chargeflowId': new ObjectId(doc.users.chargeflowId!) };

            const mongoDoc = MongoCompanyMapper.toMongoCompany(doc);
            mockQueryOperation.find.mockResolvedValue({ items: [ { ...mongoDoc, _id: id } ] } as unknown as IPaginatedFindByQueryResult<IMongoCompany>);

            const result = await repository.findAllPaginated(filter, 1, 1);

            expect(mockQueryOperation.find).toHaveBeenCalledWith(filter, 1, 1);
            expect(result).toEqual([ {
                ...doc,
                _id: id.toHexString(),
            } ]);
        });
    });

});
