import { ClientSession, Filter, ObjectId } from 'mongodb';
import { IMongoCompanyRepository } from './IMongoCompanyRepository';
import { ICompany, IMongoCompany } from './Types';
import { MongoCompanyMapper } from './MongoCompanyMapper';
import { WithId } from '../shared/helper-types';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoCompanyRepository implements IMongoCompanyRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoCompany>,
        private readonly updateOperation: IUpdateOperation<IMongoCompany>,
    ) {}

    async findByChargeflowId(chargeflowId: string, clientSession?: ClientSession | undefined): Promise<WithId<ICompany>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { 'users.chargeflowId': new ObjectId(chargeflowId) },
            clientSession,
        );
        return doc ? MongoCompanyMapper.fromMongoCompany(doc) : null;
    }

    async findByAccountIdAndEmail(accountId: string, email: string, clientSession?: ClientSession | undefined): Promise<WithId<ICompany>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { 'users.accountId': new ObjectId(accountId),
                'users.email': email },
            clientSession,
        );
        return doc ? MongoCompanyMapper.fromMongoCompany(doc) : null;
    }

    async findByAccountId(accountId: string, clientSession?: ClientSession | undefined): Promise<WithId<ICompany>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { 'users.accountId': new ObjectId(accountId) },
            clientSession,
        );
        return doc ? MongoCompanyMapper.fromMongoCompany(doc) : null;
    }

    async findByCustomerId(customerId: string, clientSession?: ClientSession | undefined): Promise<WithId<ICompany>|null> {
        const doc = await this.findByQueryOperation.findOne(
            { $or: [ { 'users.billing.stripeCustomer.id': customerId }, { 'users.billing.alertsSubscription.id': customerId } ] },
            clientSession,
        );
        return doc ? MongoCompanyMapper.fromMongoCompany(doc) : null;
    }

    async savePaymentMethodId(
        chargeflowId: string, paymentMethodId: string, clientSession?: ClientSession,
    ): Promise<WithId<ICompany> | null> {
        const result = await this.updateOperation.updateWithFilter(
            { 'users.chargeflowId': new ObjectId(chargeflowId) },
            {
                $set: {
                    'users.billing.stripeCustomer.paymentMethodId': paymentMethodId,
                },
            },
            clientSession);
        return result ? MongoCompanyMapper.fromMongoCompany(result) : null;
    }

    async saveCompanyDescription(
        chargeflowId: string, companyDescription: string, clientSession?: ClientSession,
    ): Promise<WithId<ICompany> | null> {
        const result = await this.updateOperation.updateWithFilter(
            { 'users.chargeflowId': new ObjectId(chargeflowId) },
            {
                $set: {
                    'users.companyDescription': companyDescription,
                },
            },
            clientSession);
        return result ? MongoCompanyMapper.fromMongoCompany(result) : null;
    }

    async findAllPaginated(filter: Filter<IMongoCompany>, page: number, limit: number): Promise<WithId<ICompany>[]> {
        const result = await this.findByQueryOperation.find(filter, page, limit);
        return result.items.map(MongoCompanyMapper.fromMongoCompany);
    }
}
