import { ObjectId } from 'mongodb';
import { ICompany } from '../Types';
import { faker } from '@faker-js/faker';

const getCompany = (overrides?: Partial<ICompany>): ICompany => ({
    _id: new ObjectId().toHexString(),
    cognitoUsername: faker.string.alpha(),
    users: {
        chargeflowId: new ObjectId().toHexString(),
        accountId: new ObjectId().toHexString(),
        dateCreated: new Date(),
        dateUpdated: new Date(),
        status: 'registered',
        onboardingStageV1: 'Step 1',
        onboardingStageDate: new Date(),
        email: faker.internet.email(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        contactInformation: {
            contactName: faker.person.fullName(),
            contactEmail: faker.internet.email(),
        },
        billing: {
            source: 'stripe',
            stripeCustomer: {
                isSourceExpired: false,
                customerPortalUrl: faker.internet.url(),
                id: faker.string.alpha(),
                paymentMethodId: faker.string.alpha(),
                dateCreated: new Date(),
            },
            alertsSubscription: {
                source: 'stripe',
                isSourceExpired: false,
                customerPortalUrl: faker.internet.url(),
                id: faker.string.alpha(),
                paymentMethodId: faker.string.alpha(),
                dateCreated: new Date(),
            },
        },
        businessInformation: {},
        integrations: {},
    },
    ...overrides,
});

const defaultUserCookies = getCompany();

export default {
    getCompany,
    defaultUserCookies,
};
