import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { ICompany, ICompanyUsers, IMongoCompany, IMongoCompanyUsers } from './Types';
import { WithId } from '../shared/helper-types';

export class MongoCompanyMapper {
    static toMongoCompany(doc: ICompany): IMongoCompany {
        if (!doc) {
            throw new Error('Company is required for mapping');
        }
        const users: IMongoCompanyUsers = {
            ...doc.users,
            chargeflowId: doc.users.chargeflowId ?
                new ObjectId(doc.users.chargeflowId) : undefined,
            accountId: doc.users.accountId ?
                new ObjectId(doc.users.accountId) : undefined,
        };

        return {
            ...doc,
            _id: doc._id ? new ObjectId(doc._id) : undefined,
            users,
        };
    }

    static fromMongoCompany(doc: MongoWithId<IMongoCompany>): WithId<ICompany> {
        if (!doc) {
            throw new Error('Company is required for mapping');
        }

        const users: ICompanyUsers = {
            ...doc.users,
            chargeflowId: doc.users.chargeflowId?.toHexString(),
            accountId: doc.users.accountId?.toHexString(),
        };

        return {
            ...doc,
            _id: doc._id?.toHexString(),
            users,
        };
    }
}
