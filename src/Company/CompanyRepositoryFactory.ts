import { MongoClient } from 'mongodb';
import { IMongoCompanyRepository } from './IMongoCompanyRepository';
import { IMongoCompany } from './Types';
import { MongoCompanyRepository } from './MongoCompanyRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';

export class CompanyRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IMongoCompanyRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoCompany>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoCompany>(mongoClient, dbName, collectionName);

        return new MongoCompanyRepository(findByQueryOperation, updateOperation);
    }
}
