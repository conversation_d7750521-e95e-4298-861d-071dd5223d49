import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { CompanyRepositoryFactory } from './CompanyRepositoryFactory';
import { MongoCompanyRepository } from './MongoCompanyRepository';

describe(CompanyRepositoryFactory.name, () => {
    describe(CompanyRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = CompanyRepositoryFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(MongoCompanyRepository);
        });
    });
});
