import { ObjectId, WithId as MongoWithId } from 'mongodb';
import { IAdyenAuthorisation, ICreateAdyenAuthorisation, IMongoAdyenAuthorisation } from './Types';

export class IMongoAdyenAuthorisationMapper {
    static toMongoAdyenAuthorisation(chargeflowId: string, adyenAuthorisation: ICreateAdyenAuthorisation): IMongoAdyenAuthorisation {
        if (!chargeflowId) {
            throw new Error('Chargeflow ID is required for mapping');
        }
        if (!adyenAuthorisation) {
            throw new Error('Adyen Authorisation is required for mapping');
        }

        return {
            ...adyenAuthorisation,
            _id: new ObjectId(),
            chargeflowId: new ObjectId(chargeflowId),
            dateCreated: new Date(),
        };
    }

    static fromMongoAdyenAuthorisation(adyenAuthorisation: MongoWithId<IMongoAdyenAuthorisation>): IAdyenAuthorisation {
        if (!adyenAuthorisation) {
            throw new Error('AdyenAuthorisation is required for mapping');
        }
        return {
            chargeflowId: adyenAuthorisation.chargeflowId.toHexString(),
            pspReference: adyenAuthorisation.pspReference,
            rawData: adyenAuthorisation.rawData,
        };
    }
}
