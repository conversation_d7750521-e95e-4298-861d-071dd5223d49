import { Document, ObjectId } from 'mongodb';
import { IAuditableFields } from '../shared/helper-types';

export interface ICreateAdyenAuthorisation {
    pspReference: string,
    rawData: any,
}

export interface IAdyenAuthorisation extends ICreateAdyenAuthorisation {
    chargeflowId: string,
}

export interface IMongoAdyenAuthorisation extends ICreateAdyenAuthorisation, IAuditableFields, Document {
    chargeflowId: ObjectId,
}
