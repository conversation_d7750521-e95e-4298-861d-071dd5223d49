import { ClientSession, ObjectId } from 'mongodb';
import { IAddOperation } from '../shared/operation/AddOperation/IAddOperation';
import { Id } from '../shared/helper-types';
import { IAdyenAuthorisationRepository } from './IAdyenAuthorisationRepository';
import { IAdyenAuthorisation, ICreateAdyenAuthorisation, IMongoAdyenAuthorisation } from './Types';
import { IMongoAdyenAuthorisationMapper } from './IMongoAdyenAuthorisationMapper';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';

export class AdyenAuthorisationRepository implements IAdyenAuthorisationRepository {
    constructor(
        private readonly addOperation: IAddOperation<IMongoAdyenAuthorisation>,
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoAdyenAuthorisation>,
    ) {
    }

    async add(chargeflowId: string, adyenAuthorisation: ICreateAdyenAuthorisation, session?: ClientSession,
    ): Promise<Id> {
        const created = await this.addOperation.add(IMongoAdyenAuthorisationMapper.toMongoAdyenAuthorisation(chargeflowId, adyenAuthorisation), session);
        return { _id: created._id.toHexString() };
    }

    async findOneByChargeflowIdAndPspReference(chargeflowId: string, pspReference: string, session?: ClientSession,
    ): Promise<IAdyenAuthorisation | null> {
        const result = await this.findByQueryOperation
            .findOne({
                'chargeflowId': new ObjectId(chargeflowId),
                pspReference,
            }, session);
        if (!result) {
            return null;
        }
        return IMongoAdyenAuthorisationMapper.fromMongoAdyenAuthorisation(result);
    }
}
