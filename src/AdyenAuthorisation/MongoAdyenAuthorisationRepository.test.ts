import { MongoClient, ObjectId } from 'mongodb';
import { AdyenAuthorisationRepository } from './AdyenAuthorisationRepository';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoConfig } from '../lib/MongoConfig';
import { ChargeflowDbName } from '../shared/constants';
import { AdyenAuthorisationCollectionName } from './constants';
import { AdyenAuthorisationFactory } from './AdyenAuthorisationFactory';
import { IAdyenAuthorisationRepository } from './IAdyenAuthorisationRepository';
import adyenAuthorisationMocks from './__mocks__/AdyenAuthorisation';
import { IMongoAdyenAuthorisation } from './Types';

describe(AdyenAuthorisationRepository.name, () => {
    let repository: IAdyenAuthorisationRepository;
    let mongoServer: MongoMemoryServer;
    let mongoClient: MongoClient;
    const stubbedDate = new Date('2024-04-12T00:00:00Z');

    beforeAll(async () => {
        mongoServer = await MongoMemoryServer.create();
        process.env.MONGO_URI = mongoServer.getUri();
        process.env.AWS_SAM_LOCAL = 'true';
        mongoClient = await MongoConfig.getMongoClient();
        repository = AdyenAuthorisationFactory.create(
            mongoClient,
            ChargeflowDbName,
            AdyenAuthorisationCollectionName,
        );
    });

    beforeEach(() => {
        jest
            .useFakeTimers({ doNotFake: [ 'nextTick', 'setImmediate' ] })
            .setSystemTime(new Date(stubbedDate));
    });

    afterEach(async () => {
        jest.useRealTimers();
        await mongoClient.db(ChargeflowDbName)
            .collection<IMongoAdyenAuthorisation>(AdyenAuthorisationCollectionName)
            .deleteMany({});
    });

    afterAll(async () => {
        await mongoClient.close();
        await mongoServer.stop();
    });

    it('should add an adyen authorisation and return created id', async () => {
        // GIVEN
        const chargeflowId = new ObjectId().toHexString();
        const adyenAuthorisation = adyenAuthorisationMocks.getAdyenAuthorisation();
        const expectedMongoAdyenAuthorisation = adyenAuthorisationMocks.getMongoAdyenAuthorisation({
            chargeflowId: new ObjectId(chargeflowId),
            dateCreated: stubbedDate,
            dateUpdated: undefined,
        });

        // WHEN
        const id = await repository.add(chargeflowId, adyenAuthorisation);

        // THEN
        expect(id)
            .not
            .toBeNull();
        const mongoAdyenAuthorisation = await mongoClient.db(ChargeflowDbName)
            .collection<IMongoAdyenAuthorisation>(AdyenAuthorisationCollectionName)
            .findOne({ _id: new ObjectId(id._id) });
        expect(mongoAdyenAuthorisation)
            .toEqual({
                ...expectedMongoAdyenAuthorisation,
                _id: new ObjectId(id._id),
            });
    });

    it('should return an Adyen Authorisation', async () => {
        // GIVEN
        const chargeflowId = new ObjectId().toHexString();
        const adyenAuthorisation = adyenAuthorisationMocks.getAdyenAuthorisation({ chargeflowId });
        await repository.add(chargeflowId, adyenAuthorisation);

        // WHEN
        const foundAdyenAuthorisation = await repository.findOneByChargeflowIdAndPspReference(chargeflowId, adyenAuthorisation.pspReference);

        // THEN
        expect(foundAdyenAuthorisation)
            .not
            .toBeNull();
        expect(foundAdyenAuthorisation)
            .toEqual(adyenAuthorisation);
    });
});
