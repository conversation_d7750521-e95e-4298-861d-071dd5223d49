import { ClientSession } from 'mongodb';
import { Id } from '../shared/helper-types';
import { IAdyenAuthorisation, ICreateAdyenAuthorisation } from './Types';

export interface IAdyenAuthorisationRepository {
    add: (chargeflowId: string, adyenAuthorisation: ICreateAdyenAuthorisation, session?: ClientSession,
    ) => Promise<Id>;

    findOneByChargeflowIdAndPspReference: (chargeflowId: string, pspReference: string, session?: ClientSession,
    ) => Promise<IAdyenAuthorisation | null>;
}
