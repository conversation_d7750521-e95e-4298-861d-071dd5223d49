import { mock } from 'jest-mock-extended';
import { MongoClient } from 'mongodb';
import { faker } from '@faker-js/faker';
import { AdyenAuthorisationFactory } from './AdyenAuthorisationFactory';
import { AdyenAuthorisationRepository } from './AdyenAuthorisationRepository';

describe(AdyenAuthorisationFactory.name, () => {
    describe(AdyenAuthorisationFactory.create.name, () => {
        it('returns created repository', () => {
            // GIVEN
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            // WHEN
            const result = AdyenAuthorisationFactory.create(mongoClient, dbName, collectionName);

            // THEN
            expect(result).toBeInstanceOf(AdyenAuthorisationRepository);
        });
    });
});
