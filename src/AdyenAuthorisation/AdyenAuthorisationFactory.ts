import { MongoClient } from 'mongodb';
import { MongoAddOperation } from '../shared/operation/AddOperation/MongoAddOperation';
import { IAdyenAuthorisationRepository } from './IAdyenAuthorisationRepository';
import { AdyenAuthorisationRepository } from './AdyenAuthorisationRepository';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { IMongoAdyenAuthorisation } from './Types';

export class AdyenAuthorisationFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IAdyenAuthorisationRepository {
        const addOperation = new MongoAddOperation<IMongoAdyenAuthorisation>(mongoClient, dbName, collectionName);
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoAdyenAuthorisation>(mongoClient, dbName, collectionName);
        return new AdyenAuthorisationRepository(addOperation, findByQueryOperation);
    }
}
