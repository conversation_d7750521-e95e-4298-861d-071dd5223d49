import { ObjectId } from 'mongodb';
import { IAdyenAuthorisation, IMongoAdyenAuthorisation } from '../Types';

const getAdyenAuthorisation = (overrides?: Partial<IAdyenAuthorisation>): IAdyenAuthorisation => ({
    chargeflowId: new ObjectId('66187980cacc5e60aacf4130').toHexString(),
    pspReference: 'AAAABBBB1234',
    rawData: {},
    ...overrides,
});

const getMongoAdyenAuthorisation = (overrides?: Partial<IMongoAdyenAuthorisation>): IMongoAdyenAuthorisation => ({
    _id: new ObjectId(),
    chargeflowId: new ObjectId('66187980cacc5e60aacf4130'),
    pspReference: 'AAAABBBB1234',
    rawData: {},
    dateCreated: new Date(),
    dateUpdated: new Date(),
    ...overrides,
});

export default {
    getAdyenAuthorisation,
    getMongoAdyenAuthorisation,
};
