import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { MongoPlatformDataRepository } from './MongoPlatformDataRepository';
import { IMongoPlatformData } from './IPlatformData';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
import { IPlatformDataRepository } from './IPlatformDataRepository';

export class PlatformDataRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): IPlatformDataRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<IMongoPlatformData>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<IMongoPlatformData>(mongoClient, dbName, collectionName);

        return new MongoPlatformDataRepository(
            findByQueryOperation,
            updateOperation,
        );
    }
}
