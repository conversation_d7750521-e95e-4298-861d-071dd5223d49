import { ClientSession } from 'mongodb';
import { IMongoPlatformData, IPlatformData } from './IPlatformData';
import { WithId } from '../shared/helper-types';

export interface IPlatformDataRepository {
    getByPlatformId: (platformId: string, session?: ClientSession) => Promise<WithId<IPlatformData> | null>;

    upsert: (platformId: string, platform: Partial<IPlatformData>, session?: ClientSession) => Promise<WithId<IPlatformData> | null>;

    deactivateApiKey(platformId: string, _id: string, session?: ClientSession): Promise<WithId<IPlatformData> | null>

    getById(_id: string, session?: ClientSession): Promise<WithId<IPlatformData> | null>

    invalidateApiKey(platformId: string, _id: string, session?: ClientSession): Promise<WithId<IMongoPlatformData>>
}
