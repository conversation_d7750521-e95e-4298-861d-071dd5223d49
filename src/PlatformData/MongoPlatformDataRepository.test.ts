import { mock } from 'jest-mock-extended';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ClientSession, ObjectId, WithId } from 'mongodb';
import { MongoPlatformDataRepository } from './MongoPlatformDataRepository';
import { IMongoPlatformData } from './IPlatformData';
import platformMocks from './__mocks__/PlatformData';
import { convertStringToObjectId } from '../shared/helper-functions';
import paginationMocks from '../shared/pagination/__mocks__/Pagination';

describe(MongoPlatformDataRepository.name, () => {
    let mongoPlatformDataRepository: MongoPlatformDataRepository;
    const updateOperation = mock<IUpdateOperation<IMongoPlatformData>>();
    const findByQueryOperation = mock<IFindByQueryOperation<IMongoPlatformData>>();

    beforeEach(() => {
        mongoPlatformDataRepository = new MongoPlatformDataRepository(
            findByQueryOperation,
            updateOperation,
        );
    });

    afterEach(jest.clearAllMocks);

    describe(MongoPlatformDataRepository.prototype.getByPlatformId.name, () => {
        it('returns a single platform, if found', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformData();
            const session = mock<ClientSession>();
            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformData>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformDataRepository.getByPlatformId(platform._id, session);

            expect(result)
                .toEqual(platform);
            expect(findByQueryOperation.find)
                .toHaveBeenCalledTimes(1);
        });

        it('returns null, if platform is not found', async () => {
            expect.assertions(2);

            const session = mock<ClientSession>();
            findByQueryOperation.find.mockResolvedValue({
                items: [],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformDataRepository.getByPlatformId(ObjectId.createFromTime(1)
                .toHexString(), session);

            expect(result)
                .toBeNull();
            expect(findByQueryOperation.find)
                .toHaveBeenCalledTimes(1);
        });
    });

    describe(MongoPlatformDataRepository.prototype.getById.name, () => {
        it('returns a single platform, if found', async () => {
            expect.assertions(2);

            const platform = platformMocks.getPlatformData();
            const session = mock<ClientSession>();
            const getByIdResult = convertStringToObjectId<WithId<IMongoPlatformData>>(platform);

            findByQueryOperation.find.mockResolvedValue({
                items: [ getByIdResult ],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformDataRepository.getById(platform._id, session);

            expect(result)
                .toEqual(platform);
            expect(findByQueryOperation.find)
                .toHaveBeenCalledTimes(1);
        });

        it('returns null, if platform is not found', async () => {
            expect.assertions(2);

            const session = mock<ClientSession>();
            findByQueryOperation.find.mockResolvedValue({
                items: [],
                pagination: paginationMocks.getPaginationResult(),
            });

            const result = await mongoPlatformDataRepository.getById(ObjectId.createFromTime(1)
                .toHexString(), session);

            expect(result)
                .toBeNull();
            expect(findByQueryOperation.find)
                .toHaveBeenCalledTimes(1);
        });
    });

    describe(MongoPlatformDataRepository.prototype.invalidateApiKey.name, () => {
        it('throws an error if platformId is not a valid Mongo ObjectId', async () => {
            expect.assertions(1);

            const invalidPlatformId = 'invalid-id';
            const _id = ObjectId.createFromTime(1).toHexString();
            const session = mock<ClientSession>();

            await expect(
                mongoPlatformDataRepository.invalidateApiKey(invalidPlatformId, _id, session),
            ).rejects.toThrow('Invalid Mongo ObjectId format for platformId');
        });

        it('throws an error if _id is not a valid Mongo ObjectId', async () => {
            expect.assertions(1);

            const platformId = ObjectId.createFromTime(1).toHexString();
            const invalidId = 'invalid-id';
            const session = mock<ClientSession>();

            await expect(
                mongoPlatformDataRepository.invalidateApiKey(platformId, invalidId, session),
            ).rejects.toThrow('Invalid Mongo ObjectId format for _id');
        });
    });
});
