import { IMongoPlatformData, IPlatformData, IPlatformDataBase } from '../IPlatformData';
import { ObjectId, WithId } from 'mongodb';

const defaultPlatformData: IPlatformDataBase = {
    stripe_key: 'test_stripe_key',
};

const getPlatformData = (overrides: Partial<IPlatformData> = {}): WithId<IPlatformData> => ({
    _id: new ObjectId().toHexString(),
    platformId: new ObjectId().toHexString(),
    ...defaultPlatformData,
    ...overrides,
});

const getMongoPlatformData = (overrides: Partial<IMongoPlatformData> = {}): WithId<IMongoPlatformData> => ({
    _id: new ObjectId(),
    platformId: new ObjectId(),
    ...defaultPlatformData,
    ...overrides,
});

export default {
    getPlatformData,
    getMongoPlatformData,
};
