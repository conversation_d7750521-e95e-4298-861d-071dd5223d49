import { ClientSession, ObjectId } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { IPlatformDataRepository } from './IPlatformDataRepository';
import { IMongoPlatformData, IPlatformData } from './IPlatformData';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';
import { WithId } from '../shared/helper-types';
import { convertObjectIdToString, isMongoObjectId } from '../shared/helper-functions';

export class MongoPlatformDataRepository implements IPlatformDataRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<IMongoPlatformData>,
        private readonly updateOperation: IUpdateOperation<IMongoPlatformData>,
    ) {
    }

    async getByPlatformId(platformId: string, session?: ClientSession): Promise<WithId<IPlatformData> | null> {
        if (!isMongoObjectId(platformId)) {
            throw new Error('Invalid Mongo ObjectId format');
        }
        const query = { platformId: new ObjectId(platformId.toString()), stripe_key: { $ne: null } };
        const result = await this.findByQueryOperation.find(query, 1, 1, session);
        return result.items?.length ? convertObjectIdToString<WithId<IPlatformData>>(result.items[0]) : null;
    }

    async getById(_id: string, session?: ClientSession): Promise<WithId<IPlatformData> | null> {
        if (!isMongoObjectId(_id)) {
            throw new Error('Invalid Mongo ObjectId format');
        }
        const query = { _id: new ObjectId(_id) };
        const result = await this.findByQueryOperation.find(query, 1, 1, session);
        return result.items?.length ? convertObjectIdToString<WithId<IPlatformData>>(result.items[0]) : null;
    }

    async upsert(platformId: string, platformData: Partial<IPlatformData>, session?: ClientSession): Promise<WithId<IPlatformData> | null> {
        if (!isMongoObjectId(platformId)) {
            throw new Error('Invalid Mongo ObjectId format');
        }

        const result = await this.updateOperation.upsert(
            { platformId: new ObjectId(platformId) },
            {
                $set: {
                    stripe_key: platformData.stripe_key,
                    is_stripe_key_valid: platformData.is_stripe_key_valid,
                    updatedAt: new Date(),
                },
                $setOnInsert: {
                    createdAt: new Date(),
                },
            },
            session,
        );

        return convertObjectIdToString<WithId<IPlatformData>>(result);
    }

    async deactivateApiKey(platformId: string, _id: string, session?: ClientSession): Promise<WithId<IPlatformData> | null> {
        if (!isMongoObjectId(platformId)) {
            throw new Error('Invalid Mongo ObjectId format for platformId');
        }

        if (!isMongoObjectId(_id)) {
            throw new Error('Invalid Mongo ObjectId format for _id');
        }

        const result = await this.updateOperation.updateWithFilter(
            { platformId: new ObjectId(platformId), _id: new ObjectId(_id) },
            {
                $set: {
                    stripe_key: null,
                    is_stripe_key_valid: false,
                    updatedAt: new Date(),
                },
            },
            session,
        );

        return convertObjectIdToString<WithId<IPlatformData>>(result);
    }

    async invalidateApiKey(platformId: string, _id: string, session?: ClientSession): Promise<WithId<IMongoPlatformData>> {
        if (!isMongoObjectId(platformId)) {
            throw new Error('Invalid Mongo ObjectId format for platformId');
        }

        if (!isMongoObjectId(_id)) {
            throw new Error('Invalid Mongo ObjectId format for _id');
        }

        const result = await this.updateOperation.updateWithFilter(
            { platformId: new ObjectId(platformId), _id: new ObjectId(_id) },
            {
                $set: {
                    is_stripe_key_valid: false,
                    updatedAt: new Date(),
                },
            },
            session,
        );

        return result as WithId<IMongoPlatformData>;
    }
}
