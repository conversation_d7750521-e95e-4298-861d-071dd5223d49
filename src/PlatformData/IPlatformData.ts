import { Document, ObjectId } from 'mongodb';

export interface IPlatformDataBase {
    stripe_key?: string | null;
    stripe_key_expiration?: Date | string;
    stripe_key_date_created?: Date | string;
    is_stripe_key_valid?: boolean;

    createdAt?: Date | string;
    updatedAt?: Date | string | null;

    // Here will be stored platform metadata
    [key: string]: any;
}

export interface IPlatformData extends IPlatformDataBase {
    _id?: string;
    platformId?: string;
}

export interface IMongoPlatformData extends IPlatformDataBase, Document {
    _id?: ObjectId;
    platformId?: ObjectId;
}
