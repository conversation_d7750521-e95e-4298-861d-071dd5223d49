import { mock } from 'jest-mock-extended';
import { faker } from '@faker-js/faker';
import { MongoClient } from 'mongodb';
import { PlatformDataRepositoryFactory } from './PlatformDataRepositoryFactory';
import { MongoPlatformDataRepository } from './MongoPlatformDataRepository';

describe(PlatformDataRepositoryFactory.name, () => {
    describe(PlatformDataRepositoryFactory.create.name, () => {
        it('returns created repository', () => {
            const mongoClient = mock<MongoClient>();
            const dbName = faker.word.noun();
            const collectionName = faker.word.noun();

            const result = PlatformDataRepositoryFactory.create(mongoClient, dbName, collectionName);

            expect(result).toBeInstanceOf(MongoPlatformDataRepository);
        });
    });
});
