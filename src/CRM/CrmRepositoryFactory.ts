import { MongoClient } from 'mongodb';
import { MongoFindByQueryOperation } from '../shared/operation/FindByQueryOperation/MongoFindByQueryOperation';
import { ICrmData } from './ICrm';
import { ICrmRepository } from './ICrmRepository';
import { MongoCrmRepository } from './MongoCrmRepository';
import { MongoUpdateOperation } from '../shared/operation/UpdateOperation/MongoUpdateOperation';
export class CrmRepositoryFactory {
    static create(mongoClient: MongoClient, dbName: string, collectionName: string): ICrmRepository {
        const findByQueryOperation = new MongoFindByQueryOperation<ICrmData>(mongoClient, dbName, collectionName);
        const updateOperation = new MongoUpdateOperation<ICrmData>(mongoClient, dbName, collectionName);
        return new MongoCrmRepository(findByQueryOperation, updateOperation);
    }
}
