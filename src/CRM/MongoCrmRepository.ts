import { ClientSession } from 'mongodb';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ICrmData } from './ICrm';
import { ICrmRepository } from './ICrmRepository';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

export class MongoCrmRepository implements ICrmRepository {
    constructor(
        private readonly findByQueryOperation: IFindByQueryOperation<ICrmData>,
        private readonly updateOperation: IUpdateOperation<ICrmData>,
    ) {}

    async getOneCrmData(
        chargeflowId: string, clientSession?: ClientSession,
    ): Promise<ICrmData | null> {
        return this.findByQueryOperation.findOne({ 'Chargeflow ID':chargeflowId }, clientSession);
    }

    async updateOneCrmData(
        chargeflowId: string, data:Partial<ICrmData>, clientSession?: ClientSession,
    ): Promise<ICrmData | null> {
        return this.updateOperation.updateWithFilter(
            { 'Chargeflow ID': chargeflowId },
            {
                $set: {
                    ...data,
                },
            },
            clientSession,
        );
    }
}
