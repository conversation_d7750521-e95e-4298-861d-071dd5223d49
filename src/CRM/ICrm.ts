export type Month =
	| 'january'
	| 'february'
	| 'march'
	| 'april'
	| 'may'
	| 'june'
	| 'july'
	| 'august'
	| 'september'
	| 'october'
	| 'november'
	| 'december';

export type ShortMonth =
	| 'Jan'
	| 'Feb'
	| 'Mar'
	| 'Apr'
	| 'May'
	| 'Jun'
	| 'Jul'
	| 'Aug'
	| 'Sep'
	| 'Oct'
	| 'Nov'
	| 'Dec';

export interface ICrmData {
	_id: string | null;
	accountId?: string;
	'Account ID'?: string;
	'Alerts Authorization Date'?: Date;
	'Alerts Enrollment'?: boolean;
	'Alerts Enrollment Date'?: Date;
	'Average Monthly Value'?: number;
	'Billing Cap Amount'?: number;
	'Billing Status'?: string;
	'Chargeflow ID'?: string;
	'Company Address'?: string;
	'Company Name'?: string;
	'Contact Email'?: string;
	Currency?: string;
	'Customer Status'?: string;
	'Date Updated'?: Date;
	'Disputes Count'?: number;
	'Disputes Value'?: number;
	'Email 1'?: string;
	'Email 2'?: string;
	'Email 3'?: string;
	'Email 4'?: string;
	'Enrolled Processors'?: string;
	'First Name'?: string;
	'Last Name'?: string;
	Integrations?: string;
	'Main Domain'?: string;
	MyShopifyURL?: string;
	'Onboarding Stage'?: string;
	'Onboarding Stage Date'?: Date;
	Partner?: string;
	'Phone Number'?: string;
	Platform?: string;
	'Shopify Plan'?: string;
	'Sign-Up Date'?: Date;
	Source?: string;
	'Store Name'?: string;
	'Store Type'?: string;
	'Stripe Arns'?: string;
	'Stripe Statement Descriptor'?: string;
	'Success Fee'?: string;
	'Top Processors'?: string;
	'Total Resolved With Chargeflow'?: number;
	'Total Resolved Without Chargeflow'?: number;
	'Total Won Amount With Chargeflow'?: number;
	'Total Won Amount Without Chargeflow'?: number;
	'UTM Campaign'?: string;
	'UTM Medium'?: string;
	'UTM Source'?: string;
	'Win Rate With Chargeflow'?: number;
	'Win Rate Without Chargeflow'?: number;
	'Yearly MCV'?: Record<ShortMonth, string>;
	chargebackRatios?: ChargebackRatio[];
}

export interface ChargebackRatio {
	updatedAt: string;
	processor: string;
	processorAccountId: string;
	ratios: Record<Month, number>;
}
