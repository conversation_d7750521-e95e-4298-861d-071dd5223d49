import { ClientSession } from 'mongodb';
import { MongoCrmRepository } from './MongoCrmRepository';
import { IFindByQueryOperation } from '../shared/operation/FindByQueryOperation/IFindByQueryOperation';
import { ICrmData } from './ICrm';
import { IUpdateOperation } from '../shared/operation/UpdateOperation/IUpdateOperation';

jest.mock('../shared/operation/FindByQueryOperation/IFindByQueryOperation');
jest.mock('../shared/operation/UpdateOperation/IUpdateOperation');

describe('MongoCrmRepository', () => {
    let repository: MongoCrmRepository;
    let mockFindByQueryOperation: jest.Mocked<IFindByQueryOperation<ICrmData>>;
    let mockUpdateOperation: jest.Mocked<IUpdateOperation<ICrmData>>;
    let session: ClientSession;

    beforeEach(() => {
        mockFindByQueryOperation = {
            findOne: jest.fn(),
            find: jest.fn(),
            findWithAggregation: jest.fn(),
        } as unknown as jest.Mocked<IFindByQueryOperation<ICrmData>>;

        mockUpdateOperation = {
            update: jest.fn(),
            updateWithFilter: jest.fn(),
            updateMany: jest.fn(),
        } as unknown as jest.Mocked<IUpdateOperation<ICrmData>>;
        repository = new MongoCrmRepository( mockFindByQueryOperation, mockUpdateOperation);
        session = {} as ClientSession;
    });

    describe('getOneCrmData', () => {
        it('should return CRM data if found', async () => {
            const chargeflowId = '60c72b2f9b1e8b3f4c8b4567';
            const crmData = {
                'Chargeflow ID': chargeflowId,
            } as unknown as ICrmData;
            mockFindByQueryOperation.findOne.mockResolvedValue(crmData);

            const result = await repository.getOneCrmData(chargeflowId, session);

            expect(result).toEqual(crmData);
            expect(mockFindByQueryOperation.findOne).toHaveBeenCalledWith({ 'Chargeflow ID': chargeflowId }, session);
        });
    });

    describe('updateOneCrmData', () => {
        it('should update CRM data and return the updated data', async () => {
            const chargeflowId = '60c72b2f9b1e8b3f4c8b4567';
            const updateData = {
                'Chargeflow ID': chargeflowId,
                'New Field': 'New Value' } as any;
            const updatedCrmData = {
                'Chargeflow ID': chargeflowId,
                ...updateData,
            } as unknown as ICrmData;
            mockUpdateOperation.updateWithFilter.mockResolvedValue(updatedCrmData);

            const result = await repository.updateOneCrmData(chargeflowId, updateData, session);

            expect(result).toEqual(updatedCrmData);
            expect(mockUpdateOperation.updateWithFilter).toHaveBeenCalledWith(
                { 'Chargeflow ID': chargeflowId },
                {
                    $set: {
                        ...updateData,
                    },
                },
                session,
            );
        });
    });
});
