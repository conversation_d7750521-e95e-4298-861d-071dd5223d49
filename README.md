# data-access (in active development)

A common reusable package for Chargeflow data access. Note: `in active development` will be removed from title when all initial features + necessary docs are in place (work in progress).

### Installation

Github packages are published to GitHub registry, and then installed <strong>similarly to how you would install any other NPM package</strong>. In order for this to work locally, you need to configure our GitHub registry by running these commands under your data-access directory:

```bash
npm config set //npm.pkg.github.com/:_authToken ROTATING_RND_READ_PACKAGE_TOKEN
npm config set @chargeflow-team:registry https://npm.pkg.github.com/chargeflow-team
```

`ROTATING_TEAM_READ_PACKAGE_TOKEN` - is rotated monthly and is published in `rnd` channel.

The token above grants you read access to Chargflow NPM packages and will be rotated on a monthly basis.

You no longer need to run npm publish before committing. Simply commit your code in your feature branch as usual, push, create a pull request, code review, approval, and merge; the rest is taken care in the pipeline.

Once above is set up, to install:

```bash
npm i @chargeflow-team/data-access
```

### Usage

Before using the library make sure that your `MONGO_URI` and `MONGO_AUTH` environment variables are set properly.

When using disputes repository, also make sure to have `STACK_NAME` and `DISPUTES_EVENT_BUS` environment variables set like this:

```ts
Environment:
    Variables:
        STACK_NAME: !Ref AWS::StackName
        DISPUTES_EVENT_BUS: !ImportValue
            Fn::Sub: ${EnvType}-DisputesEventBusName
```

Once the variables are set the library is ready to use (the Dispute entity was used for the example):

```ts
import { MongoConfig } from '@chargeflow-team/chargeflow-utils-sdk';
import { MongoClient } from 'mongodb';
import { DataAccessUnitOfWork } from './DataAccessUnitOfWork';
import { IUnifiedDisputeObject } from './Types';

// prepare entities
const dispute: IUnifiedDisputeObject  = {}
const disputes: IUnifiedDisputeObject[] = []

try {
    const mongoClient: MongoClient = await MongoConfig.getMongoClient();
    const dataAccess = new DataAccessUnitOfWork(mongoClient);

    const session = dataAccess.startTransaction();

    try {
        const addedDispute = await dataAccess.disputeRepository.add(
            dispute,
            session
        );
        const addedDisputes = await dataAccess.disputeRepository.bulkAdd(
            disputes,
            session
        );
        const dispute = await dataAccess.disputeRepository.getByChargeflowId(
            dispute.chargeflowId,
            session
        );
    } catch (error) {
        await dataAccess.abortTransaction();

        throw error;
    }

    await dataAccess.commitTransaction();
} catch (error) {
    // handle error
}
```

All types are defined in the library.

### Notes

Currently the library using Mongo client for database interaction to unblock development of other parts of the system. The strategy will be changed in the nearest future to start using Prisma. However, one imporant thing has to be covered first. Prisma requires ReplicaSet for each DB that interacts with. Currently Chargeflow's databases does not use it. Once the ReplicaSet is enabled for every database in the system the Prisma will be introduced here in favour of the mongo client.

### Q&A

- Enumeration is missing values that I need
- Not all properties are defined
- What to do in case I need to add new property
- And similar questions

Answer is the same. This is work in progress and we should all contribute, feel free to expand, add, modify and make a PR. Be careful not to remove or modify something that can affect other services. PR-s will be carefully reviewed.
