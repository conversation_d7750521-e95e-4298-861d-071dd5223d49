{"name": "@chargeflow-team/data-access", "version": "1.0.413", "description": "A common, reusable package for Chargeflow data access", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepare": "if [ \\$NODE_ENV != \"production\" ] && [ \\$GITHUB_ACTIONS != \"true\" ]; then run-s prepare:install prepare:pre-commit prepare:commit-msg; fi || true", "prepare:install": "husky install", "prepare:pre-commit": "husky set .husky/pre-commit \"npx lint-staged --no-stash --config .lintstagedrc.js\"", "prepare:commit-msg": "husky set .husky/commit-msg 'node scripts/commit-msg-hook.js $1'", "test": "jest -i", "build": "tsc", "lint": "eslint --fix --ext .js,.ts ."}, "repository": {"type": "git", "url": "git+https://github.com/chargeflow-team/data-access.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/chargeflow-team/data-access/issues"}, "homepage": "https://github.com/chargeflow-team/data-access#readme", "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.26.3", "@faker-js/faker": "^8.3.1", "@types/eslint": "^8.56.2", "@types/jest": "^29.5.11", "@types/luxon": "^3.4.2", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "dotenv": "^16.4.7", "eslint": "^8.56.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^27.6.3", "eslint-plugin-json": "^3.1.0", "eslint-plugin-yml": "^1.12.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-mock-extended": "^3.0.5", "mongodb-memory-server": "^9.1.6", "npm-run-all": "^4.1.5", "ts-jest": "^29.2.5", "ts-to-zod": "^3.15.0", "typescript": "^5.3.3"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "optionalDependencies": {"mongodb": "^5.6.0"}, "dependencies": {"@chargeflow-team/chargeflow-utils-sdk": "^1.0.246", "@chargeflow-team/common-models": "^0.0.97", "@chargeflow-team/events-infra": "^0.0.314", "luxon": "^3.4.4", "pagination-calculator": "^1.0.0", "zod": "^3.23.4"}}