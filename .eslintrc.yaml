# https://eslint.org/docs/latest/use/configure/language-options#specifying-environments
env:
  commonjs: true
  # es2022: true
  node: true

parser: "@typescript-eslint/parser"
parserOptions:
  ecmaVersion: 2020
  sourceType: module

extends:
  # https://github.com/eslint/eslint/blob/master/conf/eslint-recommended.js
  - eslint:recommended
  # https://github.com/ota-meshi/eslint-plugin-yml/tree/master/docs/rules
  - plugin:yml/recommended
  # https://github.com/azeemba/eslint-plugin-json#individual-rules
  - plugin:json/recommended
  # https://mysticatea.github.io/eslint-plugin-eslint-comments/rules/
  - plugin:eslint-comments/recommended
  - plugin:@typescript-eslint/recommended

# https://eslint.org/docs/rules/
rules:
  array-bracket-spacing:
    - error
    - always
  arrow-spacing: error
  comma-dangle:
    - error
    - always-multiline
  curly: error
  eol-last: error
  eqeqeq: error
  func-call-spacing: error
  indent:
    - error
    - 4
    - SwitchCase: 1
  max-depth:
    - error
    - 3
  max-len:
    - error
    # TSMIGRATION: We should consider increasing this for the ts-migrate comments
    - code: 200
  max-nested-callbacks:
    - error
    - 5
  max-statements-per-line:
    - error
    - max: 1
  newline-per-chained-call:
    - error
    - ignoreChainWithDepth: 3
  no-duplicate-imports: error
  no-multi-assign: error
  no-multiple-empty-lines:
    - error
    - max: 1
      maxEOF: 0
  no-nested-ternary: error
  no-trailing-spaces: error
  no-unneeded-ternary: error
  no-var: error
  object-curly-spacing:
    - error
    - always
  prefer-const: error
  quotes:
    - error
    - single
    - avoid-escape
  semi: error
  space-before-blocks: error
  brace-style: error
  keyword-spacing:
    - error
  space-before-function-paren:
    - error
    - named: never
      anonymous: never
      asyncArrow: always
  spaced-comment:
    - error
    - always
  strict:
    - error
    - safe
  no-whitespace-before-property: error
  arrow-parens:
    - error
    - as-needed

  # plugin:yml
  yml/quotes:
    - error
    - prefer: single
      avoidEscape: true

  # plugin:eslint-comments
  eslint-comments/no-use:
    - error

  "@typescript-eslint/no-explicit-any": "off"
