module.exports = {
    "*.{js,yml,yaml,json}": () => 'eslint --fix',
    "template.yaml": () => 'sam validate --lint',
    "*": () => 'docker run --interactive ' +
        `--volume ${process.cwd()}:/workdir ` +
        '--rm trufflesecurity/trufflehog:latest ' +
        'git file:///workdir --branch develop --max-depth 1 ' +
        '--no-verification --fail --only-verified',
    "*": () => 'node -e \'const execSync = require("child_process").execSync;' +
        'const branchName = execSync("git rev-parse --abbrev-ref HEAD").toString().trim();' +
        'const regex = /^(cha|bug)-[1-9][0-9]{0,5}(-.*)?$/i; if (!regex.test(branchName)) { ' +
        'console.error("Invalid branch name " + branchName + ". ' +
        'It must match cha- or bug- format (case insensitive)."); process.exit(1); }\''    
};
