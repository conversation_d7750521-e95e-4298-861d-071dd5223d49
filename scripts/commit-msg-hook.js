'use strict';

const fs = require('fs');
const { execSync } = require('child_process');

function prependStoryIDToCommitMsg() {
    // Get the current branch name
    const branchName = execSync('git rev-parse --abbrev-ref HEAD')
        .toString()
        .trim();

    // Extract the story ID from the branch name using the given pattern
    const storyIDMatch =
    branchName.match(/^cha-(\d+)/i) ?? branchName.match(/^bug-(\d+)/i);

    if (!storyIDMatch) {
        throw new Error(
            `Invalid branch name: ${branchName}. Expected format: cha-<number>- or bug-<number>...`,
        );
    }
    const storyID = storyIDMatch[0];

    // Read the commit message being set
    const commitMsgPath = process.argv[2];
    if (!commitMsgPath) {
        console.error('Unable to find the commit message path.');
        process.exit(1);
    }

    // The path to the COMMIT_EDITMSG is passed as the second argument
    const originalCommitMsg = fs.readFileSync(commitMsgPath, 'utf-8');

    // If it's a merge commit, bypass the check
    if (originalCommitMsg.startsWith('Merge branch')) {
        return;
    }

    if (
        !originalCommitMsg.toLowerCase().startsWith(storyID) ||
    !originalCommitMsg.toLowerCase().includes(storyID)
    ) {
        const updatedCommitMsg = `[${storyID}] ${originalCommitMsg}`;
        fs.writeFileSync(commitMsgPath, updatedCommitMsg);
    }
}

prependStoryIDToCommitMsg();
